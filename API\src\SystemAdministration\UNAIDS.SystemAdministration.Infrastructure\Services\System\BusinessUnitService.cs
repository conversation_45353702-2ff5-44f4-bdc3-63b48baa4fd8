using AutoMapper;
using IdentityModel;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class BusinessUnitService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<BusinessUnit, SystemAdminDbContext, BusinessUnitDTO, IBusinessUnitRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IBusinessUnitService<BusinessUnitDTO>
    {
        /// <summary>
        /// Create the object
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public override async Task<BusinessUnitDTO> Create(BusinessUnitDTO businessUnit)
        {
            if (businessUnit == null)
                throw new ArgumentNullException(nameof(businessUnit));

            var repo = unitOfWork.Repository<IBusinessUnitRepository>();

            var objRec = mapper.Map<BusinessUnit>(businessUnit);
            objRec.Id = await sequenceRepository.GetSequenceNumber(repo.GetTableName());
            objRec.OBSSetting = null;

            await repo.AddAsync(objRec);
            await unitOfWork.SaveChangesAsync();

            return mapper.Map<BusinessUnitDTO>(objRec);
        }

        /// <summary>
        /// update the record
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public override async Task<BusinessUnitDTO> Update(BusinessUnitDTO businessUnit)
        {
            if (businessUnit == null)
                throw new ArgumentNullException(nameof(businessUnit));

            // Set ParentBusinessUnitId to null if it is -1
            if (businessUnit.ParentBusinessUnitId == -1)
            {
                businessUnit.ParentBusinessUnitId = null;
            }

            var objRec = mapper.Map<BusinessUnit>(businessUnit);
            objRec.OBSSetting = null;
            await unitOfWork.Repository<IBusinessUnitRepository>().UpdateAsync(objRec);
            await unitOfWork.SaveChangesAsync();

            return mapper.Map<BusinessUnitDTO>(objRec);
        }
        public async Task<List<BUHierarchyDTO>> GetBuHierarchy()
        {
            return await unitOfWork.Repository<IBusinessUnitRepository>().GetBUHierarchy();
        }

        public async Task<List<CommonLookUp>> GetOBSSettings()
        {
            return await unitOfWork.Repository<IBusinessUnitRepository>().GetOBSSettings();
        }

        public async Task<List<CommonLookUp>> GetParentBusinessUnits()
        {
            return await unitOfWork.Repository<IBusinessUnitRepository>().GetParentBusinessUnits();
        }
    }
}