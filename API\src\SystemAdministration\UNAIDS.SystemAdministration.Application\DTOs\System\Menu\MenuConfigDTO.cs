using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class MenuConfigDTO : BaseEntity
    {
        public int? ParentMenuId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? ComponentName { get; set; }
        public int? ModuleId { get; set; }
        public int MenuType { get; set; }
        public string? URL { get; set; }
        public int? SortOrder { get; set; }
        public string? Remarks { get; set; }
        public short? IsHidden { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? Path { get; set; }
        public string? ConfigurationSettings { get; set; }
        public int? TemplateId { get; set; }
        public int? ObjectType { get; set; }
        public int? ServiceComponentId { get; set; }
        public string? ControllerType { get; set; }
        public string? Area { get; set; }
        public string? Controller { get; set; }
        public string? Action { get; set; }
    }
}
