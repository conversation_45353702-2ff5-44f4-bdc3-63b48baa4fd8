using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class RoleConfiguration : IEntityTypeConfiguration<Role>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.ToTable("role_info");
            builder.<PERSON><PERSON><PERSON>(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever();
            builder.Property(c => c.Code).IsRequired().HasMaxLength(10);
            builder.Property(c => c.Name).IsRequired().HasMaxLength(100);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RoleConfiguration());
        }
    }
}
