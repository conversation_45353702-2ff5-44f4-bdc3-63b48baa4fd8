﻿using System.Text.Json;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class ProgramOnboardDTO
    {
        public int Id { get; set; }
        public string? Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public int? BusinessUnitId { get; set; }
        public List<ProgramModelOnBoardDTO>? ProgramModels { get; set; }
        public List<ProgramDataElementsOnBoardDTO>? ProgramDataElements { get; set; }
        public List<ProgramRolesOnBoardDTO>? ProgramRoles { get; set; }
        public List<ProgramMenuOnBoardDTO>? ProgramMenus { get; set; }
    }

    public class ProgramModelOnBoardDTO
    {
        public int ModelId { get; set; }
        public string? DisplayName { get; set; }
    }

    public class ProgramDataElementsOnBoardDTO
    {
        public int DataElementId { get; set; }
        public string? DisplayName { get; set; }
        public string? Description { get; set; }
    }

    public class ProgramRolesOnBoardDTO
    {
        public int RoleId { get; set; }
        public int[]? ModuleIds { get; set; }
    }

    public class ProgramMenuOnBoardDTO
    {
        public int MenuId { get; set; }
        public string? DisplayName { get; set; }
    }
}
