using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using System.Text.Json;
using System.Threading.Tasks;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/workflow-permission")]
    [ApiController]
    public class WorkflowPermissionController(IWorkflowPermissionService<WorkflowPermissionDTO> workflowPermissionService, IValidator<WorkflowPermissionDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Get all WorkFlowPermissions
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var workflowPermissions = await workflowPermissionService.GetAll(filter);
            return Ok(new
            {
                Record = workflowPermissions,
                RoleId = await workflowPermissionService.GetUserRoles(),
                UserRights = workflowPermissionService.AccessRights(SubjectTypes.WORKFLOWPERMISSION)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get a specific WorkFlowPermission by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            var data = await workflowPermissionService.GetById(id);

            if (data == null)
                return NotFound();

            return Ok(new
            {
                Record = data,
                RoleId = await workflowPermissionService.GetUserRoles(),
                UserRights = workflowPermissionService.AccessRights(SubjectTypes.WORKFLOWPERMISSION)
            });
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new WorkFlowPermission
        /// </summary>
        /// <param name="WorkFlowPermission"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] WorkflowPermissionDTO WorkflowPermission)
        {
            if (WorkflowPermission == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(WorkflowPermission);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await workflowPermissionService.Create(WorkflowPermission);
            return CreatedAtAction(nameof(Create), new { id = WorkflowPermission.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a WorkFlowPermission
        /// </summary>
        /// <param name="WorkFlowPermission"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] WorkflowPermissionDTO WorkflowPermission)
        {

            var validationResult = await validator.ValidateAsync(WorkflowPermission);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await workflowPermissionService.Update(WorkflowPermission);
            return Ok(WorkflowPermission);
        }
        #endregion

        #region Update Mapping...
        /// <summary>
        /// Update a WorkFlowPermission
        /// </summary>
        /// <param name="WorkFlowPermission"></param>
        /// <returns></returns>
        [HttpPut("{id}/transitions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> UpdateMapping(int id, [FromBody] JsonDocument transitions)
        {
            await workflowPermissionService.UpdateTransitions(id, transitions);
            return Ok();
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a WorkFlowPermission
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await workflowPermissionService.Delete(id);
            return Ok();
        }
        #endregion

        #region Transitions ...
        [HttpGet("{id}/transitions")]
        [Authorize($"{SubjectTypes.WORKFLOWPERMISSION}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetTransitions(int id)
        {
            return Ok(new {
                Record = await workflowPermissionService.GetPermissions(id),
                Steps = await workflowPermissionService.GetWorkflowSteps(id)
            });
        }
        #endregion

    }
}
