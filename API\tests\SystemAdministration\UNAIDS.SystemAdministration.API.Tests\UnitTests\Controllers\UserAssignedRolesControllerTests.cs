using Moq;
using AutoFixture;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Core.Base;
using UNAIDS.Authentication.Shared;
using UNAIDS.Authorization.Shared;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class UserAssignedRolesControllerTests
    {
        private readonly Mock<IUserAssignedRolesService<UserDTO>> _mockService;
        private readonly UserAssignedRolesController _controller;
        private readonly Fixture _fixture;

        public UserAssignedRolesControllerTests()
        {
            _mockService = new Mock<IUserAssignedRolesService<UserDTO>>();
            _controller = new UserAssignedRolesController(_mockService.Object);
            _fixture = new Fixture();
        }
  
        [Fact]
        public async Task GetUserAssignedRoles_ReturnsOkObjectResult_WithExpectedData()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedRoles = _fixture.Create<List<CommonLookUp>>();
            var expectedBusinessUnits = _fixture.Create<List<CommonLookUp>>();
            var expectedUserNames = _fixture.Create<List<CommonLookUp>>();
            var expectedLoginName = _fixture.Create<List<CommonLookUp>>();
            var expectedUserRights = "Read,Write";

            _mockService.Setup(s => s.GetUserAssignedRoles(filter)).ReturnsAsync(expectedRoles);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.GetUserNames()).ReturnsAsync(expectedUserNames);
            _mockService.Setup(s => s.GetLoginNames()).ReturnsAsync(expectedLoginName);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.USER)).Returns(expectedUserRights);

            // Act
            var result = await _controller.GetUserAssignedRoles(filter);

            // Assert
            _mockService.Verify(service => service.GetUserAssignedRoles(filter), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);
            _mockService.Verify(service => service.GetUserNames(), Times.Once);
            _mockService.Verify(service => service.GetLoginNames(), Times.Once);
            _mockService.Verify(service => service.AccessRights(SubjectTypes.USER), Times.Once);
       
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);            
            var businessUnitId = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);            
            var user = returnValue.GetType().GetProperty("LogedInUserNameId")?.GetValue(returnValue);
            var loginName = returnValue.GetType().GetProperty("LoginNameId")?.GetValue(returnValue);
            var title = returnValue.GetType().GetProperty("Title")?.GetValue(returnValue);
            var userRights = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedRoles, recordValue); 
            Assert.Equal(expectedBusinessUnits, businessUnitId); 
            Assert.Equal(expectedUserNames, user);
            Assert.Equal(expectedLoginName, loginName);
            Assert.Equal("", title);
            Assert.Equal(expectedUserRights, userRights);

        }

  
    }
}
