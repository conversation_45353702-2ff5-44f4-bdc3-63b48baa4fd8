using Microsoft.EntityFrameworkCore;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Shared;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System.Text.RegularExpressions;
using UNAIDS.SystemAdministration.Application.DTOs;
//using UNAIDS.Core.Workflow;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserProfileService(
        SystemAdminDbContext systemAdminDbContext,
        IUserContext userContext
        ) : IUserProfileService<UserProfileDTO>
    {
        public async Task<List<UserProfileDTO>> GetUserDetail()
        {

            return await (from usr in systemAdminDbContext.Users.AsNoTracking()
                          join role in systemAdminDbContext.Roles.AsNoTracking()
                          on usr.RoleId equals role.Id
                          where usr.Id == userContext.Id
                          select new UserProfileDTO
                          {
                              UserName = usr.UserName,
                              UserEmail = usr.Email,
                          }).ToListAsync();

        }

        public async Task ChangePassword(ChangePasswordDetailsDTO changePasswordDetails)
        {
            var objUser = await systemAdminDbContext.Users.FirstOrDefaultAsync(user => user.Id == userContext.Id);
            if (objUser == null)
            {
                throw new KeyNotFoundException("User not found.");
            }

            byte[] salt = PasswordUtility.GenerateSalt();
            objUser.PasswordHash = PasswordUtility.HashPassword(changePasswordDetails.NewPassword, salt);
            objUser.UpdatedById = userContext.Id;

            systemAdminDbContext.Entry(objUser).State = EntityState.Modified;
            await systemAdminDbContext.SaveChangesAsync();

        }


    }
}