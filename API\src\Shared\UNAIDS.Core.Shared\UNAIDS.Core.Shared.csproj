﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Interfaces\**" />
	  <EmbeddedResource Remove="Interfaces\**" />
	  <None Remove="Interfaces\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
		<PackageReference Include="MailKit" Version="4.8.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.8" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
		<PackageReference Include="Serilog" Version="4.0.1" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

</Project>
