using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class RoleService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<Role, SystemAdminDbContext, RoleDTO, IRoleRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IRoleService<RoleDTO>
    {
        /// <summary>
        /// Get Current Step 
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetCurrentStep(int id)
        {
            return await unitOfWork.Repository<IRoleRepository>().GetCurrentStep(id);
        }

    }
}
