﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "model_references",
                schema: "pan_gms",
                table: "program_data_elements");

            migrationBuilder.DropColumn(
                name: "model_references",
                schema: "pan_gms",
                table: "local_data_elements");

            migrationBuilder.DropColumn(
                name: "model_references",
                schema: "pan_gms",
                table: "data_elements");

            migrationBuilder.CreateTable(
                name: "models",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_models", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "model_data_elements",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    model_id = table.Column<int>(type: "integer", nullable: false),
                    model_id1 = table.Column<int>(type: "integer", nullable: true),
                    data_element_id = table.Column<int>(type: "integer", nullable: false),
                    is_global = table.Column<short>(type: "smallint", nullable: false),
                    navigation_model_id = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_model_data_elements", x => x.id);
                    table.ForeignKey(
                        name: "fk_model_data_elements_models_model_id",
                        column: x => x.model_id,
                        principalSchema: "pan_gms",
                        principalTable: "models",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_model_data_elements_models_model_id1",
                        column: x => x.model_id1,
                        principalSchema: "pan_gms",
                        principalTable: "models",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_model_data_elements_model_id",
                schema: "pan_gms",
                table: "model_data_elements",
                column: "model_id");

            migrationBuilder.CreateIndex(
                name: "ix_model_data_elements_model_id1",
                schema: "pan_gms",
                table: "model_data_elements",
                column: "model_id1");

            migrationBuilder.CreateIndex(
                name: "ix_model_data_elements_tenant_id",
                schema: "pan_gms",
                table: "model_data_elements",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_models_tenant_id",
                schema: "pan_gms",
                table: "models",
                column: "tenant_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "model_data_elements",
                schema: "pan_gms");

            migrationBuilder.DropTable(
                name: "models",
                schema: "pan_gms");

            migrationBuilder.AddColumn<string[]>(
                name: "model_references",
                schema: "pan_gms",
                table: "program_data_elements",
                type: "text[]",
                nullable: true);

            migrationBuilder.AddColumn<string[]>(
                name: "model_references",
                schema: "pan_gms",
                table: "local_data_elements",
                type: "text[]",
                nullable: true);

            migrationBuilder.AddColumn<string[]>(
                name: "model_references",
                schema: "pan_gms",
                table: "data_elements",
                type: "text[]",
                nullable: true);
        }
    }
}
