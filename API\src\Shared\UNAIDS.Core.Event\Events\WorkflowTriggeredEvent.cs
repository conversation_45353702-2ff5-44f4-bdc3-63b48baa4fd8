﻿using MediatR;
using Microsoft.EntityFrameworkCore;

namespace UNAIDS.Core.Event
{
    public class WorkflowTriggeredEvent<T> : BaseEvent<T>, INotification
    {
        public string EventName { get; }
        public EntityState Action { get; }

        public WorkflowTriggeredEvent(T entity, string eventName, EntityState action) : base(entity)
        {
            EventName = eventName;
            Action = action;
        }
    }
}
