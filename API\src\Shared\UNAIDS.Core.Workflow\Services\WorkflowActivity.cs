﻿namespace UNAIDS.Core.Workflow
{
    public abstract class WorkflowActivity: IWorkflowActivity
    {
        public virtual string Type => GetType().Name;

        public virtual Task<ActivityExecutionResult> BeforeExecuteAsync(ActivityBeforeExecutionContext context)
        {
            throw new NotImplementedException();
        }

        public virtual Task<ActivityExecutionResult> AfterExecuteAsync(ActivityAfterExecutionContext context)
        {
            throw new NotImplementedException();
        }
    }
}
