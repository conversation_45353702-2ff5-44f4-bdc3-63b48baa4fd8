﻿using System.Text.Json;

namespace UNAIDS.HivScorecards.Domain
{
    public class WorkflowStep : BaseEntity
    {
        public int WorkflowDefinitionId { get; set; }
        public virtual WorkflowDefinition? WorkflowDefinition { get; set; }
        public int StepNo { get; set; }
        public string StepCode { get; set; } = string.Empty;
        public string StepName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string? Action { get; set; }
        public int SortOrder { get; set; }
        public short? IsForwardFlow { get; set; }
        public short? AllowRollback { get; set; }
        public short? LockStatus { get; set; }
        public short? CanPause { get; set; }
        public string? ReturnStatus { get; set; }
        public short? TriggerService { get; set; }
        public JsonDocument? Properties { get; set; }
    }
}
