﻿using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class BusinessUnitServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IBusinessUnitRepository> _mockBusinessUnitRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly BusinessUnitService _businessUnitService;
        private readonly Fixture _fixture;

        public BusinessUnitServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockBusinessUnitRepository = new Mock<IBusinessUnitRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IBusinessUnitRepository>())
                           .Returns(_mockBusinessUnitRepository.Object);

            
            _businessUnitService = new BusinessUnitService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }
        [Fact]
        public async Task GetBuHierarchy_ReturnsListOfBUHierarchyDTO()
        {
            // Arrange          
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            var expectedHierarchy = _fixture.CreateMany<BUHierarchyDTO>(2).ToList();
            _mockBusinessUnitRepository.Setup(repo => repo.GetBUHierarchy())
                                        .ReturnsAsync(expectedHierarchy);

            // Act
            var result = await _businessUnitService.GetBuHierarchy();

            // Assert
            Assert.Equal(expectedHierarchy, result);
            // Ensure the method is called once
            _mockBusinessUnitRepository.Verify(repo => repo.GetBUHierarchy(), Times.Once); 
        }

        [Fact]
        public async Task GetOBSSettings_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var expectedSettings = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockBusinessUnitRepository.Setup(repo => repo.GetOBSSettings())
                                        .ReturnsAsync(expectedSettings);

            // Act
            var result = await _businessUnitService.GetOBSSettings();

            // Assert
           Assert.Equal(expectedSettings,result); 
            _mockBusinessUnitRepository.Verify(repo => repo.GetOBSSettings(), Times.Once); 
        }

        [Fact]
        public async Task GetParentBusinessUnits_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var expectedParentBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockBusinessUnitRepository.Setup(repo => repo.GetParentBusinessUnits())
                                        .ReturnsAsync(expectedParentBusinessUnits);

            // Act
            var result = await _businessUnitService.GetParentBusinessUnits();

            // Assert
            Assert.Equal(expectedParentBusinessUnits,result); 
            _mockBusinessUnitRepository.Verify(repo => repo.GetParentBusinessUnits(), Times.Once); 
        }

        [Fact]
        public async Task Update_WithNullData_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _businessUnitService.Update(null));
        }

        [Fact]
        public async Task Update_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            var businessUnitDto = _fixture.Create<BusinessUnitDTO>();
            var businessUnitEntity = _fixture.Create<BusinessUnit>();
            var updatedBusinessUnitDto = _fixture.Create<BusinessUnitDTO>();

            _mockMapper.Setup(m => m.Map<BusinessUnit>(businessUnitDto)).Returns(businessUnitEntity);
            _mockMapper.Setup(m => m.Map<BusinessUnitDTO>(businessUnitEntity)).Returns(updatedBusinessUnitDto);
            _mockUnitOfWork.Setup(uow => uow.Repository<IBusinessUnitRepository>().UpdateAsync(businessUnitEntity)).Returns(Task.FromResult(businessUnitEntity));                 
            _mockUnitOfWork.Setup(uow => uow.SaveChangesAsync()).Returns(Task.FromResult(1));  
            
            // Act
            var result = await _businessUnitService.Update(businessUnitDto);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IBusinessUnitRepository>().UpdateAsync(businessUnitEntity), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);

            Assert.NotNull(result);
            Assert.Equal(updatedBusinessUnitDto, result); 
        }

        [Fact]
        public async Task Create_WithNullData_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _businessUnitService.Create(null));
        }

        [Fact]
        public async Task Create_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            var businessUnitDto = _fixture.Create<BusinessUnitDTO>();
            var businessUnitEntity = _fixture.Create<BusinessUnit>();

            _mockMapper.Setup(m => m.Map<BusinessUnit>(businessUnitDto)).Returns(businessUnitEntity);
            _mockMapper.Setup(m => m.Map<BusinessUnitDTO>(businessUnitEntity)).Returns(businessUnitDto);
            _mockUnitOfWork.Setup(uow => uow.Repository<IBusinessUnitRepository>().AddAsync(businessUnitEntity)).Returns(Task.FromResult(businessUnitEntity));                 
            _mockUnitOfWork.Setup(uow => uow.Repository<IBusinessUnitRepository>().GetTableName()).Returns("TableName");                 
            _mockUnitOfWork.Setup(uow => uow.SaveChangesAsync()).Returns(Task.FromResult(1));  
            _mockSequenceRepository.Setup(s => s.GetSequenceNumber(It.IsAny<string>())).ReturnsAsync(1);
      
            // Act
            var result = await _businessUnitService.Create(businessUnitDto);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IBusinessUnitRepository>().AddAsync(businessUnitEntity), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);

            Assert.NotNull(result);
            Assert.Equal(businessUnitDto, result); 
        }

    }
}
