using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface ILookupTypeService
    {
        Task<Result<List<LookupTypeResponseDTO>>> GetAll();
        Task<Result<LookupTypeDTO>> GetById(int id);
        Task<Result<LookupTypeDTO>> Create(LookupTypeDTO lookupType);
        Task<Result<LookupTypeDTO>> Update(int id, LookupTypeDTO lookupType);
        Task<Result> Delete(int id);
    }
}
