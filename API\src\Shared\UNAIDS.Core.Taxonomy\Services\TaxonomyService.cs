﻿using UNAIDS.Core.Base;
using UNAIDS.Core.Base.DTOs;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Core.Taxonomy.Services
{
    public class TaxonomyService(IUnitOfWork<CommonDbContext> unitOfWork) : ITaxonomyService
    {
        public async Task<List<ModelDTO>> GetModels()
        {
            return await unitOfWork.Repository<ITaxonomyRepository>().GetModels();
        }
        public async Task<List<ConceptDTO>> GetConcepts()
        {
            return await unitOfWork.Repository<ITaxonomyRepository>().GetConcepts();
        }
        public async Task<List<DataElementDTO>> GetConceptElements(string slug)
        {
            return await unitOfWork.Repository<ITaxonomyRepository>().GetConceptElements(slug);
        }
        public async Task<List<DataElementDTO>> GetModelElements(string model)
        {
            return await unitOfWork.Repository<ITaxonomyRepository>().GetModelElements(model);
        }
        public async Task<List<DataElementDTO>> GetDataElements()
        {
            return await unitOfWork.Repository<ITaxonomyRepository>().GetDataElements();
        }
    }
}
