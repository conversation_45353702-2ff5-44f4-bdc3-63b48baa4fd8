using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class WorkflowDefinitionDTOValidator : AbstractValidator<WorkflowDefinitionDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public WorkflowDefinitionDTOValidator(SystemAdminDbContext dbcontext)
        {
            _dbContext = dbcontext;

            // Name: Required, max length of 100, and alphanumeric
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("The Name is required.")
                .Matches("^[a-zA-Z0-9' ']+$").WithMessage("The Name should be alphanumeric.")
                .MaximumLength(100).WithMessage("The Name must be at most 100 characters long.");

            // Description: Optional, but max length of 250, and alphanumeric if provided
            RuleFor(x => x.Description)
                .Matches("^[a-zA-Z0-9' ']*$").WithMessage("The Description should be alphanumeric.")
                .MaximumLength(250).WithMessage("The Description must be at most 250 characters long.")
                .When(x => !string.IsNullOrEmpty(x.Description));

            // Ensure that the workflow definition name is unique, ignoring case
            RuleFor(x => x)
                .MustAsync(async (dto, cancellationToken) =>
                {
                    var existingWorkFlow = await _dbContext.WorkflowDefinitions.AsNoTracking()
                        .AnyAsync(a => a.Name.Trim().ToUpper() == dto.Name.Trim().ToUpper() && a.Id != dto.Id, cancellationToken);
                    return !existingWorkFlow;
                }).WithMessage("The WorkFlow Definition already exists.");

        }
    }
}
