﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\Role\RoleMenuMappingService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Application\UNAIDS.SystemAdministration.Application.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Domain\UNAIDS.SystemAdministration.Domain.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Infrastructure.Persistence\UNAIDS.SystemAdministration.Infrastructure.Persistence.csproj" />
  </ItemGroup>

</Project>
