using FluentValidation;
using UNAIDS.Core.Base;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.Authorization.Shared;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/lookups")]
    [ApiController]
    public class LookupController(ILookUpService<LookupInfoDTO> lookupInfoService, IValidator<LookupInfoDTO> lookupInfoValidator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list lookupinfo
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUP}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var data = await lookupInfoService.GetAll(filter);
            return Ok(new
            {
                Record = data,
                UserRigts = lookupInfoService.AccessRights(SubjectTypes.LOOKUP)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// get the lookup info by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUP}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if(id == 0)
                return BadRequest();

            var data = await lookupInfoService.GetById(id);
            return Ok(new {
                Record = data,
                UserRigts = lookupInfoService.AccessRights(SubjectTypes.LOOKUP)
            });            
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new lookupinfo
        /// </summary>
        /// <param name="lookupinfo"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.LOOKUP}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] LookupInfoDTO lookupinfo)
        {
            if (lookupinfo == null)
                return BadRequest();

            var validationResult = await lookupInfoValidator.ValidateAsync(lookupinfo);
            if (!validationResult.IsValid)
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));

            var result = await lookupInfoService.Create(lookupinfo);
            return CreatedAtAction(nameof(Create), new { id = lookupinfo.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a lookupinfo
        /// </summary>
        /// <param name="lookupinfo"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.LOOKUP}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] LookupInfoDTO lookupinfo)
        {
            var validationResult = await lookupInfoValidator.ValidateAsync(lookupinfo);
            if (!validationResult.IsValid)
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));

            await lookupInfoService.Update(lookupinfo);
            return Ok(lookupinfo);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// delete lookupinfo
        /// </summary>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUP}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if(id==0)
                return BadRequest();

            await lookupInfoService.Delete(id);
            return Ok();
        }
        #endregion
    }
}
