﻿using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Model.Administration;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Domain.TenantManagement;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class ProgramService(
        IUnitOfWork<TenantManagementDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) : IProgramService
    {
        /// <summary>
        /// Get all programs
        /// </summary>
        /// <returns>List of programs</returns>
        public async Task<Result<List<ProgramListResponseDTO>>> GetAll()
        {
            var programs = await unitOfWork.Repository<IProgramRepository>().GetAllAsync();

            return mapper.Map<List<ProgramListResponseDTO>>(programs);
        }

        public async Task<List<CommonLookUp>> GetModels()
        {
            return await unitOfWork.Repository<IProgramRepository>().GetModels();
        }

        public async Task<List<CommonLookUp>> GetDataElements()
        {
            return await unitOfWork.Repository<IProgramRepository>().GetDataElements();
        }

        public async Task<List<CommonLookUp>> GetModules()
        {
            return await unitOfWork.Repository<IProgramRepository>().GetModules();
        }

        public async Task<List<CommonLookUp>> GetRoles()
        {
            return await unitOfWork.Repository<IProgramRepository>().GetRoles();
        }

        public async Task<List<CommonLookUp>> GetMenus()
        {
            return await unitOfWork.Repository<IProgramRepository>().GetMenus();
        }

        /// <summary>
        /// Onboard new program
        /// </summary>
        /// <param name="program">Program to onboard</param>
        /// <returns>Program was onboarded</returns>
        public async Task<Result<ProgramOnboardDTO>> OnBoardProgram(ProgramOnboardDTO program)
        {
            // #. add business unit / tenent
            var objBusinessUnit = await AddBusinessUnit(program);
            program.BusinessUnitId = objBusinessUnit.Id;

            // #. add program
            var objProgram = await AddProgram(program);

            program.Id = objProgram.Id;

            // #. add program role permissions
            await AddRolePermissions(program);

            await unitOfWork.SaveChangesAsync();

            return objProgram;
        }

        #region private functions...

        /// <summary>
        /// Add business unit
        /// </summary>
        /// <param name="objProgram"></param>
        /// <returns></returns>
        private async Task<BusinessUnit> AddBusinessUnit(ProgramOnboardDTO objProgram)
        {
            var businessUnit = new BusinessUnit();

            if (objProgram != null)
            {
                var buRepo = unitOfWork.Repository<ITenantRepository>();

                businessUnit.Id = await sequenceRepository.GetSequenceNumber(buRepo.GetTableName());
                businessUnit.OBSSettingId = 2;
                businessUnit.Tenant = businessUnit.Id;
                businessUnit.Code = objProgram?.Code ?? "";
                businessUnit.Name = objProgram?.Name ?? "";
                businessUnit.Level = 1;
                businessUnit.Hierarchy = Convert.ToString(businessUnit.Id);

                businessUnit.Active = 1;
                businessUnit.IsDeleted = false;
                businessUnit.CreatedAt = DateTime.UtcNow;
                businessUnit.CreatedById = userContext.Id;

                await buRepo.AddAsync(businessUnit);

                UserPermission userPermission = new()
                {
                    Id = await sequenceRepository.GetSequenceNumber(buRepo.GetUserPermissionTable()),
                    BusinessUnitId = businessUnit.Id,
                    BusinessUnitValue = string.Empty,
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = userContext.Id,
                    EffectiveFrom = DateTime.UtcNow,
                    IsDeleted = false,
                    RoleId = userContext.Roles.Length > 0 ? userContext.Roles[0] : 0,
                    UserId = userContext.Id,
                };

                await buRepo.AddUserPermission(userPermission);
            }

            return businessUnit;
        }

        /// <summary>
        /// Add program
        /// </summary>
        /// <param name="objProgram"></param>
        /// <returns></returns>
        private async Task<ProgramOnboardDTO> AddProgram(ProgramOnboardDTO objProgram)
        {
            // add program
            var programRepo = unitOfWork.Repository<IProgramRepository>();

            var program = mapper.Map<Program>(objProgram);
            program.Id = await sequenceRepository.GetSequenceNumber(programRepo.GetTableName());

            program.IsDeleted = false;
            program.CreatedById = userContext.Id;
            program.CreatedAt = DateTime.UtcNow;

            await programRepo.AddAsync(program);

            var result = mapper.Map<ProgramOnboardDTO>(program);

            return result;
        }

        #endregion private functions...

        private async Task AddRolePermissions(ProgramOnboardDTO objProgram)
        {
            var tenantRepo = unitOfWork.Repository<ITenantRepository>();

            if (objProgram.ProgramRoles != null && objProgram.ProgramRoles.Count > 0)
            {
                // program menus
                var menus = await tenantRepo.GetModuleMenus();

                var roleModuleCnt = objProgram.ProgramRoles.Select(x => x.ModuleIds?.Count()).Sum() ?? 0;
                var nextId = await sequenceRepository.GetSequenceNumber(tenantRepo.GetRoleMenuMasterTable(), roleModuleCnt);

                foreach (var role in objProgram.ProgramRoles)
                {
                    if (role.ModuleIds != null && role.ModuleIds.Length > 0)
                    {
                        foreach (var moduleId in role.ModuleIds)
                        {
                            RoleMenuMaster roleMenuMaster = new()
                            {
                                Id = nextId,
                                ModuleId = moduleId,
                                BusinessUnitId = objProgram.BusinessUnitId ?? 0,
                                RoleId = role.RoleId,
                                IsDeleted = false,
                                CreatedAt = DateTime.UtcNow,
                                CreatedById = userContext.Id
                            };

                            await tenantRepo.AddRoleMenuMaster(roleMenuMaster, objProgram.BusinessUnitId);

                            await tenantRepo.AddRoleMenuMapping(menus.Where(x => x.ModuleId == moduleId).Select(x => new RoleMenuMapping()
                            {
                                IsDeleted = false,
                                MenuAdd = 1,
                                MenuDelete = 1,
                                MenuModify = 1,
                                MenuId = x.MenuId,
                                MenuView = 1,
                                RoleMenuMasterId = nextId
                            }).ToList(), objProgram.BusinessUnitId);

                            nextId += 1;
                        }
                    }
                }
            }
        }
    }
}