using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.TenantManagement;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{

    public sealed class SettingRepository(SystemAdminDbContext dbContext): BaseRepository<Setting>(dbContext), ISettingRepository
    {
        public async Task<List<CommonLookUp>> GetDataTypes()
        {
            return await dbContext.LookUpInfo.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

         public async Task<List<CommonLookUp>> GetModules()
        {
            return await dbContext.Modules.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }
    }
}
