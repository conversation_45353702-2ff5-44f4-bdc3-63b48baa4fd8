using AutoMapper;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserAssignedPermissionService(
        IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
    ) : BaseAPIService<User, SystemAdminDbContext, UserDTO, IUserAssignedPermissionRepository>(
            unitOfWork, mapper, sequenceRepository, userContext
        ),
        IUserAssignedPermissionService<UserDTO>
    {
        public async Task<List<CommonLookUp>> GetUserAssignedPermissions(Filter filter)
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetUserAssignedPermissions(filter);
        }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetBusinessUnits();
        }

        public async Task<List<CommonLookUp>> GetUserNames()
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetUserNames();
        }

        public async Task<List<CommonLookUp>> GetScreenName()
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetScreenName();
        }

        public async Task<List<CommonLookUp>> GetModule()
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetModule();
        }

        public async Task<List<CommonLookUp>> GetRole()
        {
            return await unitOfWork.Repository<IUserAssignedPermissionRepository>().GetRole();
        }
    }
}
