using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IOBSSettingService
    {
        //Task<Result<List<OBSSettingResponseDTO>>> GetAll();
        Task<Result<OBSSettingDTO>> GetById(int id);
        Task<Result<OBSSettingDTO>> Create(OBSSettingDTO obsSetting);
        Task<Result<OBSSettingDTO>> Update(int id, OBSSettingDTO obsSetting);
        //Task<Result> Delete(int id);
    }
}
