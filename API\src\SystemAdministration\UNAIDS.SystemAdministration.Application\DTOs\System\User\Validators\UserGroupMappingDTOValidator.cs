using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.Model.Administration;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class UserGroupMappingDTOValidator : AbstractValidator<UserGroupMappingDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public UserGroupMappingDTOValidator(SystemAdminDbContext dbContext)
        {
            _dbContext = dbContext;

            // Validate UserGroupsId: must be greater than 0
            RuleFor(ug => ug.UserGroupsId)
                .GreaterThan(0).WithMessage("{PropertyName} must be a positive integer.");

            // Validate UserId: must be greater than 0
            RuleFor(ug => ug.UserId)
                .GreaterThan(0).WithMessage("{PropertyName} must be a positive integer.");

            // Check for duplicate user-group mapping
            RuleFor(ug => ug)
                .MustAsync(async (userGroupMapping, cancellation) =>
                {
                    return await IsUniqueUserGroupMapping(userGroupMapping.UserGroupsId, userGroupMapping.UserId, userGroupMapping.Id);
                })
                .WithMessage("The User Name already exists in the selected User Group.");

        }
        // Method to check for unique UserGroupMapping (UserGroupsId, UserId must be unique)
        private async Task<bool> IsUniqueUserGroupMapping(int userGroupsId, int userId, int userGroupMappingId)
        {
            return !await _dbContext.UserGroupMappings.AsNoTracking()
                .AnyAsync(ug => ug.UserGroupsId == userGroupsId 
                                && ug.UserId == userId 
                                && ug.Id != userGroupMappingId);
        }
    }
}
