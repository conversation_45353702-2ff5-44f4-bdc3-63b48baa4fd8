using System;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Diagnostics.CodeAnalysis;

#nullable disable

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    [ExcludeFromCodeCoverageAttribute]
    public partial class APL_1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "pan_apps");

            migrationBuilder.CreateTable(
                name: "service_components",
                schema: "pan_apps",
                columns: table => new
                {
                    service_component_id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    parameter_schema_definition_id = table.Column<int>(type: "integer", nullable: true),
                    default_param_json = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    caption = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    tooltip = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: true),
                    controller = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    area = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    action = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    object_type = table.Column<int>(type: "integer", nullable: true),
                    menu_type = table.Column<int>(type: "integer", nullable: true),
                    controller_type = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    menu_details_json = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    model_definition_id = table.Column<int>(type: "integer", nullable: true),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    sys_schema_id = table.Column<int>(type: "integer", nullable: true),
                    sys_json = table.Column<string>(type: "text", nullable: false),
                    cus_schema_id = table.Column<int>(type: "integer", nullable: true),
                    cus_json = table.Column<string>(type: "text", nullable: false),
                    origin_business_unit_id = table.Column<int>(type: "integer", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_service_components", x => x.service_component_id);
                });

            migrationBuilder.CreateTable(
                name: "templates",
                schema: "pan_apps",
                columns: table => new
                {
                    templates_id = table.Column<int>(type: "integer", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    dashboard_json = table.Column<string>(type: "text", nullable: false),
                    active = table.Column<short>(type: "smallint", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_templates", x => x.templates_id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "service_components",
                schema: "pan_apps");

            migrationBuilder.DropTable(
                name: "templates",
                schema: "pan_apps");
        }
    }
}
