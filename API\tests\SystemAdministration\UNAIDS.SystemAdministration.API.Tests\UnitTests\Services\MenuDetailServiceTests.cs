using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class MenuDetailServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IMenuDetailRepository> _mockMenuDetailRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly MenuDetailService _menuDetailService;
        private readonly Fixture _fixture;

        public MenuDetailServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockMenuDetailRepository = new Mock<IMenuDetailRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IMenuDetailRepository>())
                           .Returns(_mockMenuDetailRepository.Object);

            
            _menuDetailService = new MenuDetailService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetMenuConfig_ReturnsListOfMenuConfigs()
        {
            // Arrange          
            var expectedMenuConfigs = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockMenuDetailRepository.Setup(repo => repo.GetMenuConfig())
                                        .ReturnsAsync(expectedMenuConfigs);

            // Act
            var result = await _menuDetailService.GetMenuConfig();

            // Assert
            Assert.Equal(expectedMenuConfigs, result);

            _mockMenuDetailRepository.Verify(repo => repo.GetMenuConfig(), Times.Once); 
        }

    }
}
