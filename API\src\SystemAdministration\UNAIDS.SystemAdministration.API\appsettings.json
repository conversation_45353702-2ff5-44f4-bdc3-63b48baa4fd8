{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs\\log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "UNAIDS"}}}