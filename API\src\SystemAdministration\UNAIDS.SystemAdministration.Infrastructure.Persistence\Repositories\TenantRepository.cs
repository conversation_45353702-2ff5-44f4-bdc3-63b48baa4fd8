﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Core.Shared;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class TenantRepository(TenantManagementDbContext dbContext) : BaseRepository<BusinessUnit>(dbContext), ITenantRepository
    {
        public override async Task<BusinessUnit> AddAsync(BusinessUnit businessUnit)
        {
            await dbContext.BusinessUnits.AddAsync(businessUnit);

            // Set shadow property explicitly
            dbContext.Entry(businessUnit).Property("TenantId").CurrentValue = businessUnit.Id;

            return businessUnit;
        }

        public async Task<UserPermission> AddUserPermission(UserPermission userPermission)
        {
            await dbContext.UserPermissions.AddAsync(userPermission);

            // Set shadow property explicitly
            dbContext.Entry(userPermission).Property("TenantId").CurrentValue = userPermission.BusinessUnitId;

            return userPermission;
        }

        public string GetUserPermissionTable()
        {
            return dbContext.Model.FindEntityType(typeof(UserPermission))?.GetAnnotation("Relational:TableName")?.Value?.ToString() ?? "";
        }

        public async Task<Dictionary<string, int>> GetGrantStages()
        {
            return await (from lkType in dbContext.LookUpTypes.AsNoTracking()
                          join lkpInfo in dbContext.LookUpInfos.AsNoTracking()
                          on lkType.Id equals lkpInfo.LookUpTypeId
                          where lkType.FieldName == AppConstants.LKP_TYPE_GRANTSTAGE
                          select new { lkpInfo.Id, lkpInfo.Name }
                         ).ToDictionaryAsync(x => x.Name, x => x.Id);
        }

        public async Task<List<ModuleMenuDTO>> GetModuleMenus()
        {
            return await dbContext.Menus.AsNoTracking()
                .Where(x => x.ControllerType == "component"
                && x.ObjectType != (int)AppConstants.MenuType.Container)
                .Select(x => new ModuleMenuDTO()
                {
                    MenuId = x.Id,
                    ModuleId = x.ModuleId ?? 0
                }).ToListAsync();
        }

        public string GetRoleMenuMasterTable()
        {
            return dbContext.Model.FindEntityType(typeof(RoleMenuMaster))?.GetAnnotation("Relational:TableName")?.Value?.ToString() ?? "";
        }

        public async Task<RoleMenuMaster> AddRoleMenuMaster(RoleMenuMaster roleMenuMaster, int? tenantId)
        {
            await dbContext.RoleMenuMasters.AddAsync(roleMenuMaster);

            // Set shadow property explicitly
            dbContext.Entry(roleMenuMaster).Property("TenantId").CurrentValue = tenantId;

            return roleMenuMaster;
        }

        public async Task<List<RoleMenuMapping>> AddRoleMenuMapping(List<RoleMenuMapping> roleMenuMappings, int? tenantId)
        {
            await dbContext.RoleMenuMappings.AddRangeAsync(roleMenuMappings);

            foreach (var mapping in roleMenuMappings)
            {
                // Set shadow property explicitly
                dbContext.Entry(mapping).Property("TenantId").CurrentValue = tenantId;
            }

            return roleMenuMappings;
        }

        public string GetProgramModelTable()
        {
            throw new NotImplementedException();
        }

        public string GetProgramConceptTable()
        {
            throw new NotImplementedException();
        }

        public string GetProgramDataElementTable()
        {
            throw new NotImplementedException();
        }

        public string GetProgramCategoryTable()
        {
            throw new NotImplementedException();
        }

        public string GetProgramCategoryOptionTable()
        {
            throw new NotImplementedException();
        }
    }
}