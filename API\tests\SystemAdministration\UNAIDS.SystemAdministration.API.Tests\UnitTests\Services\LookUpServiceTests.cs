using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain.System;

// LookUpInfoRepository is Empty

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class LookUpServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<ILookUpInfoRepository> _mockLookUpRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly LookUpService _lookUpService;
        private readonly Fixture _fixture;

        public LookUpServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockLookUpRepository = new Mock<ILookUpInfoRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<ILookUpInfoRepository>())
                           .Returns(_mockLookUpRepository.Object);

            
            _lookUpService = new LookUpService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            var filter = _fixture.Create<Filter>();

            var lookUpInfos = _fixture.CreateMany<LookUpInfo>().ToList();
            var lookupInfoDtos = _fixture.CreateMany<LookupInfoDTO>(lookUpInfos.Count).ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<ILookUpInfoRepository>().GetManyAsync(x => x.LookUpTypeId  == filter.Id,null, null, null)).ReturnsAsync(lookUpInfos);

            _mockMapper.Setup(m => m.Map<List<LookupInfoDTO>>(lookUpInfos)).Returns(lookupInfoDtos);

            // Act
            var result = await _lookUpService.GetAll(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(lookupInfoDtos, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<ILookUpInfoRepository>().GetManyAsync(x => x.LookUpTypeId == filter.Id, null, null, null), Times.Once);

            _mockMapper.Verify(m => m.Map<List<LookupInfoDTO>>(lookUpInfos), Times.Once);
        }

        [Fact]
        public async Task GetById_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            var lookupInfoEntity = _fixture.Create<LookUpInfo>();
            var lookupInfoDto = _fixture.Create<LookupInfoDTO>();

            _mockUnitOfWork.Setup(uow => uow.Repository<ILookUpInfoRepository>().GetByIdAsync(lookupInfoEntity.Id)).ReturnsAsync(lookupInfoEntity);
            _mockMapper.Setup(m => m.Map<LookupInfoDTO>(lookupInfoEntity)).Returns(lookupInfoDto);

            // Act
            var result = await _lookUpService.GetById(lookupInfoEntity.Id);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<ILookUpInfoRepository>().GetByIdAsync(lookupInfoEntity.Id));
            _mockMapper.Verify(m => m.Map<LookupInfoDTO>(lookupInfoEntity));

            Assert.NotNull(result);
            Assert.Equal(lookupInfoDto, result);
        }

        [Fact]
        public async Task Create_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            var lookupInfoDto = _fixture.Create<LookupInfoDTO>();
            var lookupInfoEntity = _fixture.Create<LookUpInfo>();
            var expectedLookupInfoDto = _fixture.Create<LookupInfoDTO>();

            _mockMapper.Setup(m => m.Map<LookUpInfo>(lookupInfoDto)).Returns(lookupInfoEntity);
            _mockMapper.Setup(m => m.Map<LookupInfoDTO>(lookupInfoEntity)).Returns(expectedLookupInfoDto);
            _mockSequenceRepository.Setup(s => s.GetSequenceNumber(It.IsAny<string>())).ReturnsAsync(1);
            _mockUnitOfWork.Setup(uow => uow.Repository<ILookUpInfoRepository>()).Returns(_mockLookUpRepository.Object);
            _mockLookUpRepository.Setup(repo => repo.AddAsync(lookupInfoEntity)).Returns(Task.FromResult(lookupInfoEntity));
            _mockUnitOfWork.Setup(uow => uow.SaveChangesAsync()).Returns(Task.FromResult(1));

            // Act
            var result = await _lookUpService.Create(lookupInfoDto);

            // Assert
            _mockLookUpRepository.Verify(repo => repo.AddAsync(lookupInfoEntity), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);

            Assert.NotNull(result);
            Assert.Equal(expectedLookupInfoDto, result);
        }

        [Fact]
        public async Task Create_WithNullLookupInfo_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _lookUpService.Create(null));
        }

       
        [Fact]
        public async Task Update_ReturnsMappedDTO()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            
            var lookupDto = _fixture.Create<LookupInfoDTO>();
            var lookupEntity = _fixture.Create<LookUpInfo>();
            var updatedLookupDto = _fixture.Create<LookupInfoDTO>();

            _mockMapper.Setup(m => m.Map<LookUpInfo>(lookupDto)).Returns(lookupEntity);
            _mockMapper.Setup(m => m.Map<LookupInfoDTO>(lookupEntity)).Returns(updatedLookupDto);
            _mockUnitOfWork.Setup(uow => uow.Repository<ILookUpInfoRepository>().UpdateAsync(lookupEntity)).Returns(Task.FromResult(lookupEntity));                 
            _mockUnitOfWork.Setup(uow => uow.SaveChangesAsync()).Returns(Task.FromResult(1));  
            
            // Act
            var result = await _lookUpService.Update(lookupDto);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<ILookUpInfoRepository>().UpdateAsync(lookupEntity), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);

            Assert.NotNull(result);
            Assert.Equal(updatedLookupDto, result); 
        }

        [Fact]
        public async Task Update_WithNullLookupInfo_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _lookUpService.Update(null));
        }

        [Fact]
        public async Task Delete_DeletesLookup()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            var lookup = _fixture.Create<LookUpInfo>();

            _mockLookUpRepository.Setup(repo => repo.GetByIdAsync(lookup.Id)).ReturnsAsync(lookup);
            _mockLookUpRepository.Setup(repo => repo.DeleteAsync(lookup)).Returns(Task.CompletedTask); 
            _mockUnitOfWork.Setup(uow => uow.Repository<ILookUpInfoRepository>()).Returns(_mockLookUpRepository.Object);

            // Act
            await _lookUpService.Delete(lookup.Id); 

            // Assert
            // _mockLookUpRepository.Verify(repo => repo.GetByIdAsync(lookup.Id), Times.Once);            
            // _mockLookUpRepository.Verify(repo => repo.DeleteAsync(lookup), Times.Once);            
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public void AccessRights_ReturnsCorrectRights()
        {
            // Arrange
            var subject = "Admin";
            var expectedAccessRights = "R,W,U"; 
            _mockUserContext.Setup(uc => uc.AccessRights(subject)).Returns(expectedAccessRights);

            // Act
            var result = _lookUpService.AccessRights(subject);

            // Assert
            Assert.Equal(expectedAccessRights, result);

            _mockUserContext.Verify(uc => uc.AccessRights(subject), Times.Once);
        }


    }
}
