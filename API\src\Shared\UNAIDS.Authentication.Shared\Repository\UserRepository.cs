﻿
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Authentication.Shared
{
    public sealed class UserRepository(CommonDbContext dbContext, ITenantProvider tenantProvider) : BaseRepository<User>(dbContext), IUserRepository
    {
        /// <summary>
        /// Get the list of users of the given tenant
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<List<User>> GetTenantUsers(int tenantId)
        {
            return await
                (from usr in (from user in dbContext.Users.AsNoTracking()
                              join perm in dbContext.UserPermissions.AsNoTracking()
                              on user.Id equals perm.UserId
                              where perm.BusinessUnitId == tenantId
                              select new
                              {
                                  user.Id,
                                  perm.RoleId,
                                  user.EffectiveFrom,
                                  user.EffectiveTo,
                                  user.Email,
                                  user.IsActive,
                                  user.IsLocked,
                                  user.LoginName,
                                  user.UserName,
                                  user.NormalizedEmail,
                                  user.NormalizedLoginName,
                                  user.PasswordHash,
                                  user.StatusId
                              })
                 group usr by usr.Id into grp
                 select new User
                 {
                     Id = grp.Key,
                     UserName = grp.FirstOrDefault().UserName,
                     PasswordHash = grp.FirstOrDefault().PasswordHash,
                     RoleId = grp.FirstOrDefault().RoleId,
                     EffectiveFrom = grp.FirstOrDefault().EffectiveFrom,
                     EffectiveTo = grp.FirstOrDefault().EffectiveTo,
                     Email = grp.FirstOrDefault().Email,
                     IsActive = grp.FirstOrDefault().IsActive,
                     IsLocked = grp.FirstOrDefault().IsLocked,
                     LoginName = grp.FirstOrDefault().LoginName,
                     NormalizedEmail = grp.FirstOrDefault().NormalizedEmail,
                     NormalizedLoginName = grp.FirstOrDefault().NormalizedLoginName,
                     StatusId = grp.FirstOrDefault().StatusId,
                 }).ToListAsync();                         
        }

        /// <summary>
        /// Get the user of the tenant
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<User> GetUser(int userId, int tenantId)
        {
            return await
                (from usr in (from user in dbContext.Users.AsNoTracking()
                              join perm in dbContext.UserPermissions.AsNoTracking()
                              on user.Id equals perm.UserId
                              where perm.BusinessUnitId == tenantId && perm.UserId == userId
                              select new
                              {
                                  user.Id,
                                  perm.RoleId,
                                  user.EffectiveFrom,
                                  user.EffectiveTo,
                                  user.Email,
                                  user.IsActive,
                                  user.IsLocked,
                                  user.LoginName,
                                  user.UserName,
                                  user.NormalizedEmail,
                                  user.NormalizedLoginName,
                                  user.PasswordHash,
                                  user.StatusId,
                                  user.CreatedAt,
                                  user.CreatedById
                              })
                 group usr by usr.Id into grp
                 select new User
                 {
                     Id = grp.Key,
                     UserName = grp.FirstOrDefault().UserName,
                     PasswordHash = grp.FirstOrDefault().PasswordHash,
                     RoleId = grp.FirstOrDefault().RoleId,
                     EffectiveFrom = grp.FirstOrDefault().EffectiveFrom,
                     EffectiveTo = grp.FirstOrDefault().EffectiveTo,
                     Email = grp.FirstOrDefault().Email,
                     IsActive = grp.FirstOrDefault().IsActive,
                     IsLocked = grp.FirstOrDefault().IsLocked,
                     LoginName = grp.FirstOrDefault().LoginName,
                     NormalizedEmail = grp.FirstOrDefault().NormalizedEmail,
                     NormalizedLoginName = grp.FirstOrDefault().NormalizedLoginName,
                     StatusId = grp.FirstOrDefault().StatusId,
                     CreatedAt = grp.FirstOrDefault().CreatedAt,
                     CreatedById = grp.FirstOrDefault().CreatedById
                 }).FirstOrDefaultAsync();
        }


        /// <summary>
        /// Check whether user exists or not
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<bool> ExistsAsync(string email)
        {
            var normalizedEmail = email.ToLower();

            return await dbContext.Users.AsNoTracking()
                .AnyAsync(x => x.NormalizedLoginName == normalizedEmail);
        }

        public async Task<User> GetUserByEmail(string email)
        {
            var normalizedEmail = email.ToLower();

            return await dbContext.Users.AsNoTracking()
                .FirstOrDefaultAsync(x => x.NormalizedLoginName == normalizedEmail);
        }

        public async Task<UserPermission> AddUserPermission(UserPermission userPermission)
        {
            await dbContext.UserPermissions.AddAsync(userPermission);
            // Set shadow property explicitly
            dbContext.Entry(userPermission).Property("TenantId").CurrentValue = tenantProvider.TenantId;

            return userPermission;
        }

        public async Task<UserInvitation> AddUserInvitation(UserInvitation invitation)
        {
            await dbContext.UserInvitations.AddAsync(invitation);

            return invitation;
        }

        public async Task<List<CommonLookUp>> GetSearchable(int id, string fieldName)
        {
            // Retrieve users without tracking changes
            var users = await dbContext.Users.AsNoTracking().ToListAsync();

            // Use reflection to get the property value dynamically
            var result = users.Select(usr => new CommonLookUp
            {
                DisplayText = typeof(User).GetProperty(fieldName)?.GetValue(usr, null)?.ToString() ?? string.Empty,
                Value = usr.Id
            }).ToList();
            return result;
        }

        public async Task<List<CommonLookUp>> GetRole()
        {
            return await dbContext.Roles.AsNoTracking()
                .Select(x => new CommonLookUp
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                })
                .ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                })
                .ToListAsync();
        }
    }
}
