using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class MenuDetailDTOValidator : AbstractValidator<MenuDetailDTO>
    {
        public MenuDetailDTOValidator()
        {
            // MenuConfigId: Required, must be greater than 0
            RuleFor(x => x.MenuConfigId)
                .GreaterThan(0).WithMessage("MenuConfigId must be greater than 0.");

            // MenuDelete: Required, must be greater than 0
            RuleFor(x => x.MenuDelete)
                .GreaterThan(0).WithMessage("MenuDelete must be greater than 0.");

            // MenuApprove: Required, must be greater than 0
            RuleFor(x => x.MenuApprove)
                .GreaterThan(0).WithMessage("MenuApprove must be greater than 0.");

            // MenuPreDatedEntry: Required, must be greater than 0
            RuleFor(x => x.MenuPreDatedEntry)
                .GreaterThan(0).WithMessage("MenuPreDatedEntry must be greater than 0.");

            // MenuImport: Required, must be greater than 0
            RuleFor(x => x.MenuImport)
                .GreaterThan(0).WithMessage("MenuImport must be greater than 0.");

            // MenuExport: Required, must be greater than 0
            RuleFor(x => x.MenuExport)
                .GreaterThan(0).WithMessage("MenuExport must be greater than 0.");

            // MenuValidation: Required, must be greater than 0
            RuleFor(x => x.MenuValidation)
                .GreaterThan(0).WithMessage("MenuValidation must be greater than 0.");
        }
    }
}
