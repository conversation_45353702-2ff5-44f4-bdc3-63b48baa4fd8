﻿namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    internal static class TenantContext
    {
        private sealed class ContextDisposable : IDisposable
        {
            private bool _disposed;

            public void Dispose()
            {
                if (!_disposed)
                {
                    EndScope();
                    _disposed = true;
                }
            }
        }

        private static readonly AsyncLocal<TenantContextHolder> TenantHolder = new();

        /// <summary>
        /// Gets current tenant context
        /// </summary>
        public static TenantContextHolder CurrentTenant => TenantHolder.Value;

        public static IDisposable BeginScope(TenantContextHolder contextHolder)
        {
            // Use an object indirection to hold the OperationContext in the AsyncLocal,
            // so it can be cleared in all ExecutionContexts when its cleared.
            TenantHolder.Value = contextHolder;

            return new ContextDisposable();
        }

        public static void EndScope()
        {
            var holder = TenantHolder.Value;

            if (holder != null)
            {
                // Clear current Tenant trapped in the AsyncLocals, as its gone
                holder.TenantId = null;
                holder.Tenant = null;
            }
        }

        public class TenantContextHolder
        {
            public int? TenantId { get; set; }
            public string? Tenant { get; set; }
        }
    }
}
