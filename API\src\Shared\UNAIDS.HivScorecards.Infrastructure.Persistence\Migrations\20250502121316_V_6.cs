﻿using System;
using System.Text.Json;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_6 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "published_model_templates",
                schema: "pan_gms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    model_header_id = table.Column<int>(type: "integer", nullable: false),
                    language = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    template = table.Column<JsonDocument>(type: "json", nullable: true),
                    tenant_id = table.Column<int>(type: "integer", nullable: true),
                    created_by_id = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updated_by_id = table.Column<int>(type: "integer", nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_published_model_templates", x => x.id);
                    table.ForeignKey(
                        name: "fk_published_model_templates_model_headers_model_header_id",
                        column: x => x.model_header_id,
                        principalSchema: "pan_gms",
                        principalTable: "model_headers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_published_model_templates_model_header_id",
                schema: "pan_gms",
                table: "published_model_templates",
                column: "model_header_id");

            migrationBuilder.CreateIndex(
                name: "ix_published_model_templates_tenant_id",
                schema: "pan_gms",
                table: "published_model_templates",
                column: "tenant_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "published_model_templates",
                schema: "pan_gms");
        }
    }
}
