using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class MenuDetailService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<MenuDetail, SystemAdminDbContext, MenuDetailDTO, IMenuDetailRepository>(unitOfWork, mapper, sequenceRepository,userContext),
        IMenuDetailService<MenuDetailDTO>
    {
        /// <summary>
        /// Get MenuConfig
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await unitOfWork.Repository<IMenuDetailRepository>().GetMenuConfig();
        }
    }
}