using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;


namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class MenuConfigControllerTests {
        private readonly Mock<IMenuConfigService<MenuConfigDTO>> _mockService;
        private readonly Mock<IValidator<MenuConfigDTO>> _mockValidator;
        private readonly MenuConfigController _controller;
        private readonly Fixture _fixture;

        public MenuConfigControllerTests() {
            _mockService = new Mock<IMenuConfigService<MenuConfigDTO>>();
            _mockValidator = new Mock<IValidator<MenuConfigDTO>>();
            _controller = new MenuConfigController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = new Filter();
            var expectedUserRights = "AEB";

            _mockService.Setup(service => service.GetMenuHierarchy()).ReturnsAsync(new List<MCHierarchyDTO>());
            _mockService.Setup(service => service.GetMenuConfig()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetModule()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ReplicationTypeId", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ScreenType", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetModule()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ObjectTypes", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.AccessRights(SubjectTypes.MENUCONFIG));

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WithZeroId() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedMenuConfig = _fixture.Create<MenuConfigDTO>();    
            var expectedUserRights ="AEB";

            _mockService.Setup(service => service.GetById(expectedMenuConfig.Id)).ReturnsAsync(expectedMenuConfig);
            _mockService.Setup(service => service.GetMenuConfigDetails()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetModule()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ReplicationTypeId", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ScreenType", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetModule()).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.GetLookUp("ObjectTypes", false)).ReturnsAsync(new List<CommonLookUp>());
            _mockService.Setup(service => service.AccessRights(SubjectTypes.MENUCONFIG));
     
            // Act
            var result = await _controller.Get(expectedMenuConfig.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result);
            var value = actionResult.Value;
            var recordProperty = value.GetType().GetProperty("Record");
            var record = recordProperty.GetValue(value);
            Assert.Equal(expectedMenuConfig, record);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenMenuConfigIsNull() {
            // Arrange
            MenuConfigDTO? menuconfig = null;

            // Act
            var result = await _controller.Create(menuconfig);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<MenuConfigDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("ComponentName", "ComponentName cannot be blank.");
            validationFailures[1] = new ValidationFailure("ComponentName", "ComponentName  cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<MenuConfigDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenMenuConfigIsValid() {
            // Arrange
            var menuConfigDTO = _fixture.Create<MenuConfigDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(menuConfigDTO, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(menuConfigDTO)).ReturnsAsync(menuConfigDTO);

            // Act
            var result = await _controller.Create(menuConfigDTO);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(menuConfigDTO, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<MenuConfigDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("ComponentName", "ComponentName cannot be blank.");
            validationFailures[1] = new ValidationFailure("ComponentName", "ComponentName  cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<MenuConfigDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenMenuConfigIsValid() {
            // Arrange
            var menuConfigDTO = _fixture.Create<MenuConfigDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(menuConfigDTO, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(menuConfigDTO)).ReturnsAsync(menuConfigDTO);

            // Act
            var result = await _controller.Update(menuConfigDTO);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var menuConfig = _fixture.Create<MenuConfigDTO>();
           _mockService.Setup(s => s.Delete(menuConfig.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(menuConfig.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}