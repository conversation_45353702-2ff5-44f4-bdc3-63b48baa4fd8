﻿
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;

namespace UNAIDS.Core.Shared
{
    public class EmailService(EmailConfig _emailConfig) : IEmailService
    {
        #region Send Mail
        /// <summary>
        /// Sends the specified message to an smtp server for deliver.
        /// </summary>
        /// <param name="from">String that contains sender e-mail address.</param>
        /// <param name="to">String that contains recepient e-mail address.</param>
        /// <param name="replyTo">String that contains reply to e-mail address.</param>
        /// <param name="subject">String that contains message subject.</param>
        /// <param name="body">String that contains message body.</param>
        /// <param name="isBodyHtml">Indicates whether the mail message body is in html.</param>
        /// <param name="file">String that contains a file path to use to create this attachment.</param>
        /// <returns></returns>
        public async Task<bool> SendMailAsync(InternetAddress from, InternetAddressList to, InternetAddress replyTo, string subject, string body, bool isBodyHtml, params string[] attachments)
        {
            var bodyBuilder = new BodyBuilder();
            var mail = new MimeMessage();

            from.Name = _emailConfig.SenderName;

            mail.From.Add(from);
            mail.To.AddRange(to);

            if (replyTo != from)
            {
                replyTo.Name = _emailConfig.SenderName;
                mail.ReplyTo.Add(replyTo);
            }

            mail.Subject = subject;

            if (attachments != null && attachments.Count() > 0)
            {
                foreach (var file in attachments)
                {
                    // Checks file exists or not
                    if (!File.Exists(file))
                        return false;

                    FileInfo fileInfo = new FileInfo(file);
                    using (var stream = File.Create(file))
                    {
                        bodyBuilder.Attachments.Add(fileInfo.Name, stream);
                    }
                }
            }

            // Checks message body type
            if (isBodyHtml)
                bodyBuilder.HtmlBody = body;
            else
                bodyBuilder.TextBody = body;

            mail.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                string mailServer = _emailConfig.Server;
                int port = _emailConfig.Port;

                SecureSocketOptions secureSocketOption = SecureSocketOptions.Auto;
                if (_emailConfig.SecureSocketOption != null)
                    secureSocketOption = (SecureSocketOptions)_emailConfig.SecureSocketOption;

                //The last parameter here is to use SSL
                await client.ConnectAsync(mailServer, port, secureSocketOption);

                //Remove any OAuth functionality as we won't be using it
                client.AuthenticationMechanisms.Remove("XOAUTH2");

                if (_emailConfig.Security ?? false)
                {
                    string mailAccount = _emailConfig.Account;
                    string mailPassword = _emailConfig.Password;

                    await client.AuthenticateAsync(mailAccount, mailPassword);
                }


                var k = await client.SendAsync(mail);
                await client.DisconnectAsync(true);
            }

            return await Task.FromResult(true);
        }
        #endregion

        #region Send Mail
        /// <summary>
        /// Sends the specified message to an smtp server for deliver.
        /// </summary>
        /// <param name="to">String that contains recepient e-mail address.</param>
        /// <param name="subject">String that contains message subject.</param>
        /// <param name="body">String that contains message body.</param>
        /// <param name="isBodyHtml">Indicates whether the mail message body is in html.</param>
        /// <returns></returns>
        public async Task<bool> SendMailAsync(string to, string subject, string body, bool isBodyHtml)
        {
            if (to.Contains(";"))
            {
                return await SendMailAsync(_emailConfig.SenderEmail, to.Split(';'), subject, body, isBodyHtml);
            }
            else
            {
                InternetAddressList toList = new InternetAddressList() { InternetAddress.Parse(to) };
                return await SendMailAsync(new MailboxAddress(_emailConfig.SenderName, _emailConfig.SenderEmail), toList, new MailboxAddress(_emailConfig.SenderName, _emailConfig.SenderEmail), subject, body, isBodyHtml, null);
            }
        }

        #endregion

        #region Send Mail
        /// <summary>
        /// Sends the specified message to an smtp server for deliver.
        /// </summary>
        /// <param name="from">String that contains sender e-mail address.</param>
        /// <param name="to">String that contains recepient e-mail address.</param>
        /// <param name="subject">String that contains message subject.</param>
        /// <param name="body">String that contains message body.</param>
        /// <param name="isBodyHtml">Indicates whether the mail message body is in html.</param>
        /// <param name="fileName">String that contains a file path to use to create this attachment.</param>
        /// <returns></returns>
        public async Task<bool> SendMailAsync(string from, string to, string subject, string body, bool isBodyHtml, string fileName)
        {
            if (to.Contains(";"))
            {
                return await SendMailAsync(from, to.Split(';'), subject, body, isBodyHtml, fileName);
            }
            else
            {
                InternetAddressList toList = new InternetAddressList() { InternetAddress.Parse(to) };
                return await SendMailAsync(new MailboxAddress(from, from), toList, new MailboxAddress(from, from), subject, body, isBodyHtml, fileName);
            }
        }

        #endregion

        #region Send Mail
        /// <summary>
        /// Sends the specified message to an smtp server for deliver.
        /// </summary>
        /// <param name="from">String that contains sender e-mail address.</param>
        /// <param name="to">Array that contains recepient e-mail address.</param>
        /// <param name="subject">String that contains message subject.</param>
        /// <param name="body">String that contains message body.</param>
        /// <param name="isBodyHtml">Indicates whether the mail message body is in html.</param>
        /// <returns></returns>
        public async Task<bool> SendMailAsync(string from, string[] to, string subject, string body, bool isBodyHtml)
        {
            InternetAddressList toList = new InternetAddressList();
            for (Int32 i = 0; i < to.Length; i++)
            {
                if (to[i].Length > 0 && to[i].Contains("@") && to[i].Contains("."))
                {
                    toList.Add(InternetAddress.Parse(to[i]));
                }
            }

            return await SendMailAsync(InternetAddress.Parse(from), toList, InternetAddress.Parse(from), subject, body, isBodyHtml);
        }

        #endregion

        #region Send Mail
        /// <summary>
        /// Sends the specified message to an smtp server for deliver.
        /// </summary>
        /// <param name="from">String that contains sender e-mail address.</param>
        /// <param name="to">Array that contains recepient e-mail address.</param>
        /// <param name="subject">String that contains message subject.</param>
        /// <param name="body">String that contains message body.</param>
        /// <param name="isBodyHtml">Indicates whether the mail message body is in html.</param>
        /// <param name="fileName">String that contains a file path to use to create this attachment.</param>
        /// <returns></returns>
        public async Task<bool> SendMailAsync(string from, string[] to, string subject, string body, bool isBodyHtml, string fileName)
        {
            InternetAddressList toList = new InternetAddressList();
            for (Int32 i = 0; i < to.Length; i++)
            {
                if (to[i].Length > 0 && to[i].Contains("@") && to[i].Contains("."))
                {
                    toList.Add(InternetAddress.Parse(to[i]));
                }
            }

            return await SendMailAsync(InternetAddress.Parse(from), toList, InternetAddress.Parse(from), subject, body, isBodyHtml, fileName);
        }

        #endregion
    }
}
