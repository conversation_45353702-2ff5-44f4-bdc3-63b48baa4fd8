using UNAIDS.Core.Base;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IWorkflowPermissionService<T> : IBaseAPIService<T> where T : class
    {
        Task UpdateTransitions(int id, JsonDocument transitions);
        Task<List<CommonLookUp>> GetUserRoles();
        Task<JsonDocument?> GetPermissions(int permissionId);
        Task<List<CommonLookUp>> GetWorkflowSteps(int wfPermissionId);
    }
}
