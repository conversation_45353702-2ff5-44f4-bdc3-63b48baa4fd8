using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class WorkflowStepService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<WorkflowStep, SystemAdminDbContext, WorkflowStepDTO, IWorkflowStepRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IWorkflowStepService<WorkflowStepDTO>
    {
        public override async Task<List<WorkflowStepDTO>> GetAll(Filter filter)
        {
            var steps = await unitOfWork.Repository<IWorkflowStepRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id, null, null, null);
            return mapper.Map<List<WorkflowStepDTO>>(steps);
        }

        /// <summary>
        /// Get ModuleGroups
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetSchemaDefinition()
        {
            return await unitOfWork.Repository<IWorkflowStepRepository>().GetSchemaDefinition();
        }

        /// <summary>
        /// Get ModuleGroups
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetWorkflowServiceName(int id)
        {
            return await unitOfWork.Repository<IWorkflowStepRepository>().GetWorkflowServiceName(id);
        }

    }
}
