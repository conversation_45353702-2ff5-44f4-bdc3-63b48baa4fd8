﻿using UNAIDS.Core.Base;
using UNAIDS.Core.Workflow.Models;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Domain.System;
using System.Text.Json;

namespace UNAIDS.Core.Workflow
{
    public interface IWorkflowRepository: IBaseRepository<WorkflowInstance>
    {
        Task<string> GetWorkflowName(int workflowId);
        Task<string> GetWorkflowStep(int stepId);
        Task<string?> GetStepCode(int stepId);
        Task<WorkflowDefinition?> GetWorkflowByName(string name);
        Task<int> GetInitialStepId(int workflowId);
        Task<WorkflowInstance> GetWorkflowInstance(Guid id);
        Task<object> GetWorkflowSteps(string workflowName);
        Task<List<WorkflowStep>> GetWorkflowSteps(int workflowId);
        Task<WorkflowStatusInfo> GetCurrentStep(Guid instanceId);
        Task<List<JsonDocument>> GetWorkflowPermissions(int workflowId, int[] roles);
        Task<List<WorkflowJourneyHistory>> GetWorklowHistory(Guid instanceId);
    }
}
