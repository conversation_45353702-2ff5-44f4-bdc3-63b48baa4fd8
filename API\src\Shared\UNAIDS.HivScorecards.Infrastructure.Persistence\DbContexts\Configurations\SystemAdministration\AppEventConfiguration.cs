﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class AppEventConfiguration : IEntityTypeConfiguration<AppEvent>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<AppEvent> builder)
        {
            builder.ToTable("app_events");
            builder.<PERSON><PERSON>ey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Description).HasMaxLength(500);
            builder.Property(e => e.EventData).HasColumnType("jsonb");
            builder.Property(e => e.TriggerCondition).HasColumnType("jsonb");

            builder.HasIndex(e => e.Name).IsUnique();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new AppEventConfiguration());
        }
    }
}