﻿using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using FluentValidation.Results;
using AutoFixture;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class ProgramsControllerTests
    {

        private readonly Mock<IProgramService> _mockService;
        private readonly Mock<IValidator<ProgramOnboardDTO>> _mockValidator;
        private readonly ProgramsController _controller;
        private readonly Fixture _fixture;

        public ProgramsControllerTests()
        {
            // Arrange: Initialize the mock services and controller
            _mockService = new Mock<IProgramService>();
            _mockValidator = new Mock<IValidator<ProgramOnboardDTO>>();
            _controller = new ProgramsController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }
        
        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData()
        {
            // Arrange
         
            var expectedData = _fixture.CreateMany<ProgramListResponseDTO>(2).ToList();

            var expectedResult = new Result<List<ProgramListResponseDTO>>(expectedData);
            var ex = _mockService.Setup(service => service.GetAll()).ReturnsAsync(expectedResult);

            //Act
            var result = await _controller.GetAll();
            var actualResult = (result as dynamic).Value;
            var propertyInfo = actualResult.GetType().GetProperty("Record");
            var returnedData = (Result<List<ProgramListResponseDTO>>)propertyInfo.GetValue(actualResult);

            //Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResult.Value, returnedData.Value);
            Assert.Equal(expectedResult.IsSuccess, returnedData.IsSuccess);

        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenProgramIsNull()
        {
            // Act
            var result = await _controller.Create(null);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var invalidProgram = new ProgramOnboardDTO{
                Id = 1,
                Name = "ProgramName"
            };
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name  cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<ProgramOnboardDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenProgramOnboardIsValid()
        {
            // Arrange
            var programOnBoardDTO = new ProgramOnboardDTO{
                Id = 1,
                Name = "ProgramName"
            };
            var programResult = new Result<ProgramOnboardDTO>(programOnBoardDTO);
            var validationResult = new FluentValidation.Results.ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(programOnBoardDTO, default)).ReturnsAsync(validationResult);
            var exResult= _mockService.Setup(s => s.OnBoardProgram(programOnBoardDTO)).ReturnsAsync(programResult);

            // Act
            var result = await _controller.Create(programOnBoardDTO);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            var actualResult = Assert.IsType<Result< ProgramOnboardDTO>> (createdAtActionResult.Value);
            Assert.Equal(programResult.Value.Id, actualResult.Value.Id);

        }

    }
}
