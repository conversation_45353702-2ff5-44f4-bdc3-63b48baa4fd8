﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace UNAIDS.Core.Shared
{
    public class AddSwaggerHeaderParameter: IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.Parameters == null)
                operation.Parameters = new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "Tenant",
                In = ParameterLocation.Header,
                Description = "Tenant Id",
                Required = false
            });
        }
    }
}
