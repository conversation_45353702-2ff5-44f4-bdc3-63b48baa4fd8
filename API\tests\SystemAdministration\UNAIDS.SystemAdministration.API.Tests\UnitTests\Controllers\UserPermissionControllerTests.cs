﻿using AutoFixture;
using FluentValidation;
using Moq;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.SystemAdministration.Application;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using FluentValidation.Results;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class UserPermissionControllerTests
    {

        private readonly Mock<IUserPermissionService<UserPermissionDTO>> _mockService;
        private readonly Mock<IValidator<UserPermissionDTO>> _mockValidator;
        private readonly UserPermissionController _controller;
        private readonly Fixture _fixture;

        public UserPermissionControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<IUserPermissionService<UserPermissionDTO>>();
            _mockValidator = new Mock<IValidator<UserPermissionDTO>>();
            _controller = new UserPermissionController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_WithValidFilter_ReturnsOkObjectResult()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var roleId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var userId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var businessUnitId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var users = _fixture.CreateMany<UserPermissionDTO>(2).ToList();
          

            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(users);
            _mockService.Setup(s => s.GetUser()).ReturnsAsync(userId);
            _mockService.Setup(s => s.GetRole()).ReturnsAsync(roleId);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(businessUnitId);

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            _mockService.Verify(s => s.GetAll(filter), Times.Once);
            _mockService.Verify(s => s.GetUser(), Times.Once);
            _mockService.Verify(s => s.GetRole(), Times.Once);
            _mockService.Verify(s => s.GetBusinessUnits(), Times.Once);

            var okResult = Assert.IsType<OkObjectResult>(result);
            dynamic returnValue = okResult.Value;
            var resultedRoleId = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var resultedRecord = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var resultedBusinessunit= returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var resultedUserId = returnValue.GetType().GetProperty("UserId")?.GetValue(returnValue);

            Assert.Equal(businessUnitId, resultedBusinessunit);
            Assert.Equal(roleId, resultedRoleId);
            Assert.Equal(userId, resultedUserId);
            Assert.Equal(users, resultedRecord);

        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {

            //Assign
            var tempId = 0;

            //Act
            var result = await _controller.Get(tempId);

            //Assert
            Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task GetById_ReturnsOkResult_WithCorrectData()
        {

            // Arrange
            var user = _fixture.Create<UserPermissionDTO>();   
            var roleId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var userId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var businessUnitId = _fixture.CreateMany<CommonLookUp>(2).ToList();
            
            _mockService.Setup(s => s.GetById(user.Id)).ReturnsAsync(user);        
            _mockService.Setup(s => s.GetUser()).ReturnsAsync(userId);
            _mockService.Setup(s => s.GetRole()).ReturnsAsync(roleId);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(businessUnitId);

            //Act
            var result = await _controller.Get(user.Id);

            //Assert
            _mockService.Verify(s => s.GetById(user.Id), Times.Once);
            _mockService.Verify(s => s.GetUser(), Times.Once);
            _mockService.Verify(s => s.GetRole(), Times.Once);
            _mockService.Verify(s => s.GetBusinessUnits(), Times.Once);

            var okResult = Assert.IsType<OkObjectResult>(result);
            dynamic returnValue = okResult.Value;
            var resultedRoleId = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var resultedRecord = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var resultedBusinessunit= returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var resultedUserId = returnValue.GetType().GetProperty("UserId")?.GetValue(returnValue);

            Assert.Equal(businessUnitId, resultedBusinessunit);
            Assert.Equal(roleId, resultedRoleId);
            Assert.Equal(userId, resultedUserId);
            Assert.Equal(user, resultedRecord);

        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenUserPermissionIsNull()

        {
            // Arrange
            UserPermissionDTO? userPermission = null;

            // Act
            var result = await _controller.Create(userPermission);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var expectedData = _fixture.Create<UserPermissionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "UserId";
                failure.ErrorMessage = "The UserId is required.";
            }
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Create(expectedData);

            //Asert 
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), It.IsAny<CancellationToken>()), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenUserPermissionIsValid()
        {
            // Arrange
            var expectedData = _fixture.Create<UserPermissionDTO>();

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), default)).ReturnsAsync(new ValidationResult());
            _mockService.Setup(service => service.Create(expectedData)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.Create(expectedData);

            //Assert
            Assert.IsType<CreatedAtActionResult>(result);
        }



        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var expectedData = _fixture.Create<UserPermissionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "UserId";
                failure.ErrorMessage = "The UserId is required.";
            }

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //ACT 
            var result = await _controller.Update(expectedData);

            // Assert
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), It.IsAny<CancellationToken>()), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResultObject_WhenUpdateIsSuccessful()
        {
            // Arrange
            var expectedData = _fixture.Create<UserPermissionDTO>();

            _mockService.Setup(service => service.Update(expectedData)).ReturnsAsync(expectedData);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), default)).ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update(expectedData);
            
            // Assert
            _mockService.Verify(s => s.Update(expectedData),Times.Once);
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserPermissionDTO>(), default), Times.Once);

            Assert.IsType<OkObjectResult>(result);
        }


        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsInvalid()
        {
            // Arrange
            int tempId = 0;

            // Act
            var result = await _controller.Delete(tempId);

            // Assert
            Assert.IsType<BadRequestResult>(result);

        }


        [Fact]
        public async Task Delete_ReturnsOk_WhenDeleteIsSuccessful()
        {
            // Arrange
            var user = _fixture.Create<UserPermissionDTO>();  

            _mockService.Setup(s => s.Delete(user.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(user.Id);

            // Assert
            _mockService.Verify(s => s.Delete(user.Id),Times.Once);

            Assert.IsType<OkResult>(result);

        }

    }
}
