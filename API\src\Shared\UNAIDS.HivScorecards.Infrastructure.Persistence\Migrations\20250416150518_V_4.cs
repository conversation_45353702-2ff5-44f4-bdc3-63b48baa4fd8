﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_4 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_model_headers_program_stages_entity_id",
                schema: "pan_gms",
                table: "model_headers");

            migrationBuilder.AddForeignKey(
                name: "fk_model_headers_program_models_entity_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "entity_id",
                principalSchema: "pan_gms",
                principalTable: "program_models",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_model_headers_program_models_entity_id",
                schema: "pan_gms",
                table: "model_headers");

            migrationBuilder.AddForeignKey(
                name: "fk_model_headers_program_stages_entity_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "entity_id",
                principalSchema: "pan_gms",
                principalTable: "program_stage",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
