﻿using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IProgramService
    {
        Task<Result<List<ProgramListResponseDTO>>> GetAll();
        Task<Result<ProgramOnboardDTO>> OnBoardProgram(ProgramOnboardDTO program);
        Task<List<CommonLookUp>> GetModels();
        Task<List<CommonLookUp>> GetDataElements();
        Task<List<CommonLookUp>> GetModules();
        Task<List<CommonLookUp>> GetRoles();
        Task<List<CommonLookUp>> GetMenus();
    }
}
