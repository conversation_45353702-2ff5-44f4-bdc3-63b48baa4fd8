using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.Core.Base.Contracts;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserGroupMappingService(
        IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
    ) : 
        BaseAPIService<UserGroupMapping, SystemAdminDbContext, UserGroupMappingDTO, IUserGroupMappingRepository>(unitOfWork, mapper, sequenceRepository, userContext), 
        IUserGroupMappingService<UserGroupMappingDTO>
    {

        public override async Task<List<UserGroupMappingDTO>> GetAll(Filter filter)
        {
            var result = await unitOfWork.Repository<IUserGroupMappingRepository>().GetManyAsync(x => x.UserGroupsId == filter.Id, null, null, null);

            return mapper.Map<List<UserGroupMappingDTO>>(result);
        }
        
        /// <summary>
        /// Get searchable user dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetUser()
        {
            return await unitOfWork.Repository<IUserGroupMappingRepository>().GetUser();
        }
    }
}
