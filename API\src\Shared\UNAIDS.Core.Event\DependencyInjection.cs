﻿using MediatR;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Event.Events;
using System.Reflection;

namespace UNAIDS.Core.Event
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddEventHandling(this IServiceCollection services)
        {
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
            services.AddTransient(typeof(INotificationHandler<>), typeof(GenericEventHandler<>));
            services.AddTransient(typeof(INotificationHandler<>), typeof(WorkflowEventHandler<>));
            services.AddTransient<EventDispatcher>();

            return services;
        }
    }
}
