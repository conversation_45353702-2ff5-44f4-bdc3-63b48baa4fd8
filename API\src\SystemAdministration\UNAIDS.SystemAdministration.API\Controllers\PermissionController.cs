using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/permission")]
    [ApiController]
    public class PermissionController(IPermissionService<RoleMenuMasterDTO> permissionService, IValidator<RoleMenuMasterDTO> validator) : ControllerBase
    {
        #region Get by id...

        /// <summary>
        /// Apply filter and list permission
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.PERMISSION}.{ActionAliases.VIEW}")]
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            if(id == 0)
                return BadRequest();

            var returnData = await permissionService.GetById(id);
            return Ok(new
            {
                Record = returnData,
                RoleId = await permissionService.GetRoles(),
                ModuleId = await permissionService.GetModules(),
                BusinessUnitId = await permissionService.GetBusinessUnits(),
                UserRights = permissionService.AccessRights(SubjectTypes.PERMISSION)
            });
        }

        #endregion

        #region GetAll...
        /// <summary>
        /// GetAll
        /// </summary>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.PERMISSION}.{ActionAliases.VIEW}")]
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {

            var returnData = await permissionService.GetAll(filter);
            return Ok(new
            {
                Record = returnData,
                RoleId = await permissionService.GetRoles(),
                ModuleId = await permissionService.GetModules(),
                BusinessUnitId = await permissionService.GetBusinessUnits(),
                UserRights = permissionService.AccessRights(SubjectTypes.PERMISSION)
            });
        }
        #endregion

        #region Post...
        /// <summary>
        /// Create
        /// </summary>
        /// <returns></returns> 
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.PERMISSION}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] RoleMenuMasterDTO roleMenuMaster)
        {
            if (roleMenuMaster == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(roleMenuMaster);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var data = await permissionService.Create(roleMenuMaster);
            return CreatedAtAction(nameof(Create), new { id = roleMenuMaster.Id }, data);
        }
        #endregion

        #region Put...
        /// <summary>
        /// Update
        /// </summary>
        /// <returns></returns> 
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.PERMISSION}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] RoleMenuMasterDTO roleMenuMaster)
        {
            // Validate the DTO
            var validationResult = await validator.ValidateAsync(roleMenuMaster);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await permissionService.Update(roleMenuMaster);
            return Ok(roleMenuMaster);
        }
        #endregion

        #region Delete...

        /// <summary>
        /// delete permission
        /// </summary>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.PERMISSION}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await permissionService.Delete(id);
            return Ok();
        }

        #endregion
    }
}