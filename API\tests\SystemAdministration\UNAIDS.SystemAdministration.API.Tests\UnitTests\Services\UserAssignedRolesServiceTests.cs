using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class UserAssignedRolesServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly UserAssignedRolesService _service;
        private readonly Fixture _fixture;

        public UserAssignedRolesServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockUserContext = new Mock<IUserContext>();
            
            _service = new UserAssignedRolesService(
                _mockUnitOfWork.Object,
                _mockMapper.Object,
                _mockSequenceRepository.Object,
                _mockUserContext.Object
            );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetUserAssignedRoles_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedRolesRepository>().GetUserAssignedRoles(filter))
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetUserAssignedRoles(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedRolesRepository>().GetUserAssignedRoles(filter), Times.Once);

        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedRolesRepository>().GetBusinessUnits())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetBusinessUnits();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedRolesRepository>().GetBusinessUnits(), Times.Once);

        }

        [Fact]
        public async Task GetUserNames_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedRolesRepository>().GetUserNames())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetUserNames();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedRolesRepository>().GetUserNames(), Times.Once);

        }

        [Fact]
        public async Task GetLoginNames_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedRolesRepository>().GetLoginNames())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetLoginNames();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedRolesRepository>().GetLoginNames(), Times.Once);

        }
        
    }

}
