﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;

namespace UNAIDS.Core.DataAccess.Dynamic
{
    public class DynamicRepository<TEntity, TContext>(TContext dbContext): BaseRepository<TEntity>(dbContext),  IDynamicRepository<TEntity> where TEntity: class where TContext : DbContext
    {
        public IQueryable<TEntity> AsQueriable()
        {
            return _dbSet.AsQueryable();
        }
    }
}
