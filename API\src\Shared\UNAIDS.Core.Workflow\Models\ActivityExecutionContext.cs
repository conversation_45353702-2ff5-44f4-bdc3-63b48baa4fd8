﻿namespace UNAIDS.Core.Workflow
{
    public class ActivityBeforeExecutionContext
    {
        public IServiceProvider ServiceProvider { get; }
        public WorkflowInstanceDTO WorkflowInstance { get; }
        public WorkflowPermissionMapping Workflow { get; }
        public Variables Variables { get; }

        public ActivityBeforeExecutionContext(
            IServiceProvider serviceProvider,
            WorkflowInstanceDTO workflowInstance,
            WorkflowPermissionMapping workflow,
            Variables variables
        )
        {
            ServiceProvider = serviceProvider;
            WorkflowInstance = workflowInstance;
            Workflow = workflow;
            Variables = variables;
        }
    }

    public class ActivityAfterExecutionContext
    {
        public IServiceProvider ServiceProvider { get; }
        public WorkflowInstanceDTO WorkflowInstance { get; }
        public WorkflowPermissionMapping Workflow { get; }
        public Variables Variables { get; }

        public ActivityAfterExecutionContext(
            IServiceProvider serviceProvider,
            WorkflowInstanceDTO workflowInstance,
            WorkflowPermissionMapping workflow,
            Variables variables
        )
        {
            ServiceProvider = serviceProvider;
            WorkflowInstance = workflowInstance;
            Workflow = workflow;
            Variables = variables;
        }
    }
}
