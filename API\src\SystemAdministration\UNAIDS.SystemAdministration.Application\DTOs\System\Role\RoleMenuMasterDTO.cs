namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class RoleMenuMasterDTO
    {
        public int Id { get; set; } 
        public int BusinessUnitId { get; set; }
        public int RoleId { get; set; }
        public int ModuleId { get; set; }
        public short? IsSystemDefined { get; set; }
        public bool EqualRoleLevelFlag { get; set; }
        public ICollection<RoleMenuMappingDTO>? RoleMenuMapping { get; set; }
    }
}
