namespace UNAIDS.SystemAdministration.Application
{
    public class LookupTypeDTO
    {
        public int Id { get; set; }
        public required string FieldName { get; set; }
        public string? Description { get; set; }
        public short? IsSystemDefined { get; set; }
        public int TenantId { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class LookupInfoDTO
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int LookUpTypeId { get; set; }
        public decimal? SortOrder { get; set; }
        public short? IsSystemDefined { get; set;}
        public int TenantId { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedAt { get; set; } 
    }
}
