using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserPermissionService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
                                    IMapper mapper,
                                    ISequenceRepository sequenceRepository,
        IUserContext userContext,
        ICommonRepository commonRepository)
        : BaseAPIService<UserPermission, SystemAdminDbContext, UserPermissionDTO, IUserPermissionRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IUserPermissionService<UserPermissionDTO>
    {


        /// <summary>
        /// Get all permissions
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public override async Task<List<UserPermissionDTO>> GetAll(Filter filter)
        {
            var records = await unitOfWork.Repository<IUserPermissionRepository>().GetPermissions(filter.Id);
            return mapper.Map<List<UserPermissionDTO>>(records);
        }

        /// <summary>
        /// Get searchable user dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetUser()
        {
            return await unitOfWork.Repository<IUserPermissionRepository>().GetUser();
        }

        /// <summary>
        /// Get role dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetRole()
        {
            return await unitOfWork.Repository<IUserPermissionRepository>().GetRole();
        }

        /// <summary>
        /// Get business units dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await commonRepository.GetTenants();
        }

    }
}
