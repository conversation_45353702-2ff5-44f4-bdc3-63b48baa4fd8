using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.Model.Administration;
using UNAIDS.SystemAdministration.Application.DTOs;


namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class UserGroupMappingServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IUserGroupMappingRepository> _mockUserGroupMappingRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly UserGroupMappingService _userGroupMappingService;
        private readonly Fixture _fixture;

        public UserGroupMappingServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockUserGroupMappingRepository = new Mock<IUserGroupMappingRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IUserGroupMappingRepository>())
                           .Returns(_mockUserGroupMappingRepository.Object);

            
            _userGroupMappingService = new UserGroupMappingService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

         [Fact]
        public async Task GetAll_ReturnsUserGroupMappings()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            var filter = _fixture.Create<Filter>();

            var userGroupMappings = _fixture.CreateMany<UserGroupMapping>().ToList();
            var userGroupMappingDTOs = _fixture.CreateMany<UserGroupMappingDTO>(userGroupMappings.Count).ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserGroupMappingRepository>().GetManyAsync(x => x.UserGroupsId == filter.Id,null, null, null)).ReturnsAsync(userGroupMappings);

            _mockMapper.Setup(m => m.Map<List<UserGroupMappingDTO>>(userGroupMappings))
                    .Returns(userGroupMappingDTOs);

            // Act
            var result = await _userGroupMappingService.GetAll(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userGroupMappingDTOs, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserGroupMappingRepository>().GetManyAsync(x => x.UserGroupsId == filter.Id, null, null, null), Times.Once);

            _mockMapper.Verify(m => m.Map<List<UserGroupMappingDTO>>(userGroupMappings), Times.Once);
        }

        [Fact]
        public async Task GetUser_ReturnsListOfUser()
        {
            // Arrange          
            var expectedUser = _fixture.CreateMany<CommonLookUp>(1).ToList();
            _mockUserGroupMappingRepository.Setup(repo => repo.GetUser()).ReturnsAsync(expectedUser);

            // Act
            var result = await _userGroupMappingService.GetUser();

            // Assert
            Assert.Equal(expectedUser, result);
            
            _mockUserGroupMappingRepository.Verify(repo => repo.GetUser(), Times.Once); 
        }

    }
}
