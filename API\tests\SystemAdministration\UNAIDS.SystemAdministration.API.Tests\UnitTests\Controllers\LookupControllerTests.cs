using Moq;
using AutoFixture;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.API.Controllers;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class LookupControllerTests
    {
        private readonly Mock<ILookUpService<LookupInfoDTO>> _mockService;
        private readonly Mock<IValidator<LookupInfoDTO>> _mockValidator;
        private readonly LookupController _controller;
        private readonly Fixture _fixture;

        public LookupControllerTests()
        {
            _mockService = new Mock<ILookUpService<LookupInfoDTO>>();
            _mockValidator = new Mock<IValidator<LookupInfoDTO>>();
            _controller = new LookupController(
                _mockService.Object,
                _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var lookupInfos = _fixture.Create<List<LookupInfoDTO>>();

            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(lookupInfos);
          
            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            Assert.NotNull(result);
            
            Assert.NotNull(actionResult);
            Assert.Equal(lookupInfos, recordValue); 
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {
            // Arrange
            int id = 0;
            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WhenIdIsFound()
        {
            // Arrange
            var lookupInfo = _fixture.Create<LookupInfoDTO>();
            _mockService.Setup(s => s.GetById(lookupInfo.Id)).ReturnsAsync(lookupInfo);

            // Act
            var result = await _controller.Get(lookupInfo.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            Assert.NotNull(result);
            
            Assert.NotNull(actionResult);
            Assert.Equal(lookupInfo, recordValue); 
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenLookupInfoIsNull()
        {
            // Arrange
            LookupInfoDTO? lookupInfo = null;

            // Act
            var result = await _controller.Create(lookupInfo);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var invalidProgram = _fixture.Create<LookupInfoDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<LookupInfoDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedResult_WhenLookupInfoIsValid()
        {
            // Arrange
            var lookupInfo = _fixture.Create<LookupInfoDTO>();
            _mockValidator.Setup(v => v.ValidateAsync(lookupInfo, default))
                .ReturnsAsync(new ValidationResult());
            _mockService.Setup(s => s.Create(lookupInfo)).ReturnsAsync(lookupInfo);

            // Act
            var result = await _controller.Create(lookupInfo);

             // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(lookupInfo, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var invalidProgram = _fixture.Create<LookupInfoDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<LookupInfoDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenLookupInfoIsValid()
        {
            // Arrange
            var lookupInfo = _fixture.Create<LookupInfoDTO>();
            _mockValidator.Setup(v => v.ValidateAsync(lookupInfo, default))
                .ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update(lookupInfo);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }


        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid()
        {
            // Arrange
            int id = 1;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<OkResult>(result);
        }
    }
}