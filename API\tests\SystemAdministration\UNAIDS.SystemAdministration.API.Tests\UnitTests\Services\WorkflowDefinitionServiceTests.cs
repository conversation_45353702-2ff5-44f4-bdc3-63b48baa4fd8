using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class WorkflowDefinitionServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IWorkflowDefinitionRepository> _mockWorkflowDefinitionRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly WorkflowDefinitionService _workflowDefinitionService;
        private readonly Fixture _fixture;

        public WorkflowDefinitionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockWorkflowDefinitionRepository = new Mock<IWorkflowDefinitionRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IWorkflowDefinitionRepository>())
                           .Returns(_mockWorkflowDefinitionRepository.Object);

            
            _workflowDefinitionService = new WorkflowDefinitionService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }
        [Fact]
        public async Task GetModuleGroups_ReturnsListOfModuleGroups()
        {
            // Arrange          
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            var expectedModuleGroups = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockWorkflowDefinitionRepository.Setup(repo => repo.GetModuleGroups())
                                        .ReturnsAsync(expectedModuleGroups);

            // Act
            var result = await _workflowDefinitionService.GetModuleGroups();

            // Assert
            Assert.Equal(expectedModuleGroups, result);
            
            _mockWorkflowDefinitionRepository.Verify(repo => repo.GetModuleGroups(), Times.Once); 
        }

    }
}
