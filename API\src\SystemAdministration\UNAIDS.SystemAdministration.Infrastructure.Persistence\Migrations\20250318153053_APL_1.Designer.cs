// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

#nullable disable

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250318153053_APL_1")]
    partial class APL_1
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("pan_apps")
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UNAIDS.SystemAdministration.Domain.ServiceComponent", b =>
                {
                    b.Property<int>("ServiceComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("service_component_id");

                    b.Property<string>("Action")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("action");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<string>("Area")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("area");

                    b.Property<string>("Caption")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("caption");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("code");

                    b.Property<string>("Controller")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("controller");

                    b.Property<string>("ControllerType")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("controller_type");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("CusJSON")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cus_json");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasColumnName("cus_schema_id");

                    b.Property<string>("DefaultParamJSON")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("default_param_json");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("MenuDetailsJSON")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("menu_details_json");

                    b.Property<int?>("MenuType")
                        .HasColumnType("integer")
                        .HasColumnName("menu_type");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("model_definition_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("ObjectType")
                        .HasColumnType("integer")
                        .HasColumnName("object_type");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("origin_business_unit_id");

                    b.Property<int?>("ParameterSchemaDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("parameter_schema_definition_id");

                    b.Property<string>("SysJSON")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sys_json");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasColumnName("sys_schema_id");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Tooltip")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)")
                        .HasColumnName("tooltip");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("ServiceComponentId")
                        .HasName("pk_service_components");

                    b.ToTable("service_components", "pan_apps");
                });

            modelBuilder.Entity("UNAIDS.SystemAdministration.Domain.Template", b =>
                {
                    b.Property<int>("TemplatesId")
                        .HasColumnType("integer")
                        .HasColumnName("templates_id");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<string>("DashboardJson")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("dashboard_json");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("TemplatesId")
                        .HasName("pk_templates");

                    b.ToTable("templates", "pan_apps");
                });
#pragma warning restore 612, 618
        }
    }
}
