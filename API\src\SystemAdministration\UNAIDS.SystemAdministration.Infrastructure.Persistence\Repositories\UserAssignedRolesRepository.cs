using Microsoft.EntityFrameworkCore;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class UserAssignedRolesRepository(SystemAdminDbContext dbContext)
        : BaseRepository<User>(dbContext), IUserAssignedRolesRepository
    {
        public async Task<List<CommonLookUp>> GetUserAssignedRoles(Filter filter)
        {
            // var query = (from u in dbContext.Users.AsNoTracking()
            //              join perm in dbContext.UserPermissions.AsNoTracking() on u.Id equals perm.UserId
            //              join bu in dbContext.BusinessUnits.AsNoTracking() on perm.BusinessUnitId equals bu.Id
            //              join role in dbContext.Roles.AsNoTracking() on perm.RoleId equals role.Id
            //              join modgp in dbContext.ModuleGroups.AsNoTracking() on perm.ModuleGroupId equals modgp.Id
            //              join mod in dbContext.Modules.AsNoTracking() on perm.ModuleId equals mod.Id
            //              join utypes in dbContext.LookUpInfo.AsNoTracking() on u.UserTypeId equals utypes.Id into ps
            //              from utypes in ps.DefaultIfEmpty()
            //              where u.IsActive == true
            //              select new
            //              {
            //                  UserId = u.UserId,
            //                  UserName = u.UserName,
            //                  LoginName = u.LoginName,
            //                  UserType = utypes.Description ?? "",
            //                  BusinessUnitId = bu.BusinessUnitId,
            //                  BusinessUnit = bu.Name,
            //                  Role = role.Description,
            //                  ModuleGroup = modgp.ModuleGroupName,
            //                  Module = mod.ModuleName,
            //                  EffectiveFrom = perm.EffectiveFrom,
            //                  EffectiveTo = perm.EffectiveTo
            //              }).Distinct().ToListAsync();

            var query = (from u in dbContext.Users.AsNoTracking()
                         join perm in dbContext.UserPermissions.AsNoTracking() on u.Id equals perm.UserId
                         join bu in dbContext.BusinessUnits.AsNoTracking() on perm.BusinessUnitId equals bu.Id
                         join role in dbContext.Roles.AsNoTracking() on perm.RoleId equals role.Id
                         join modgp in dbContext.ModuleGroups.AsNoTracking() on perm.ModuleGroupId equals modgp.Id
                         join mod in dbContext.Modules.AsNoTracking() on perm.ModuleId equals mod.Id
                         where u.IsActive == true
                         select new
                         {
                             UserId = u.Id,
                             UserName = u.UserName,
                             LoginName = u.LoginName,
                             BusinessUnitId = bu.Id,
                             BusinessUnit = bu.Name,
                             Role = role.Name,
                             ModuleGroup = modgp.Name,
                             Module = mod.Name,
                             EffectiveFrom = perm.EffectiveFrom,
                             EffectiveTo = perm.EffectiveTo
                         }).Distinct().ToListAsync();


            // Execute query immediately
            var queryResult = await query;

            // Handle manual search filtering
            if (!string.IsNullOrEmpty(filter.JsonAdvancedSearchFilterCriteria))
            {
                // Example: Simple search (adjust this according to your needs)
                queryResult = queryResult
                                .Where(x => x.UserName.Contains("example") || x.LoginName.Contains("example"))
                                .ToList();

                // var query = dbContext.MyEntities.AsQueryable();
                // string jsonFilters = "{\"Rules\":[{\"Field\":\"Name\",\"Operator\":\"eq\",\"Value\":\"John\"}]}";

                // var filteredData = QueryBuilder.AdvancedSearchQueryBuilder<MyEntity>(query, jsonFilters);
            }

            // Handle manual pagination
            if (filter.PageSize != 0)
            {
                int pageSize = filter.PageSize ?? 10;
                int pageIndex = filter.StartIndex ?? 0;
                queryResult = queryResult
                                .Skip(pageIndex)
                                .Take(pageSize)
                                .ToList();
            }

            // Convert results to CommonLookUp objects
            var result = queryResult.Select(x => new CommonLookUp
            {
                Value = x.UserId,
                DisplayText = $"{x.UserName} ({x.LoginName})",
                Active = 1
            }).ToList();

            return result;
        }
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetUserNames()
        {
            return await dbContext.Users.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.UserName, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetLoginNames()
        {
            return await dbContext.Users.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.LoginName, Value = x.Id, Active = 1 }).ToListAsync();
        }
    }
}
