﻿using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class LookUpTypeService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) : 
        BaseAPIService<LookUpType, SystemAdminDbContext, LookupTypeDTO, ILookupTypeRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        ILookUpTypeService<LookupTypeDTO>
    {
        /// <summary>
        /// Get module dropdown optoions
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetModules()
        {
            return await unitOfWork.Repository<ILookupTypeRepository>().GetModules();
        }

        /// <summary>
        /// Get business unit dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await unitOfWork.Repository<ILookupTypeRepository>().GetBusinessUnits();
        }

    }
}
