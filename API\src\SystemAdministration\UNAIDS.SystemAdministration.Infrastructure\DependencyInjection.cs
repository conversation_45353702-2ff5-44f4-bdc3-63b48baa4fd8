﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.Authentication.Shared;
using UNAIDS.SystemAdministration.Application;
using FluentValidation;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
            => services
                .AddMultitenancy(configuration)
                .AddCors(configuration)
                .AddSingleton(typeof(IHttpContextAccessor), typeof(HttpContextAccessor))
                .AddScoped(typeof(IUserContext), typeof(UserContext))
                .AddScoped<IUserProfileService<UserProfileDTO>, UserProfileService>()
                .AddScoped(typeof(IApplicationSettingService), typeof(ApplicationSettingService))
                .AddServices();

        private static IServiceCollection AddCors(this IServiceCollection services, IConfiguration configuration)
        {
            // Read CORS settings from appsettings.json
            var corsSettings = configuration.GetSection("AllowedOrigins").Get<string[]>();
            services.AddCors(options =>
            {
                options.AddPolicy("CustomCorsPolicy", policy =>
                {
                    policy.WithOrigins(corsSettings)
                          .AllowAnyMethod()
                          .AllowAnyHeader();
                });
            });

            return services;
        }

        private static IServiceCollection AddServices(this IServiceCollection services)
        {
            services.AddScoped<IProgramService, ProgramService>();
            services.AddScoped<IBusinessUnitService<BusinessUnitDTO>, BusinessUnitService>();
            services.AddScoped<ILookUpTypeService<LookupTypeDTO>, LookUpTypeService>();
            services.AddScoped<ILookUpService<LookupInfoDTO>, LookUpService>();
            services.AddScoped<ISettingService<SettingDTO>, SettingService>();
            services.AddScoped<IRoleService<RoleDTO>, RoleService>();
            services.AddScoped<IRoleMenuMappingService<RoleMenuMappingDTO>, RoleMenuMappingService>();
            services.AddScoped<IRoleMenuMasterService<RoleMenuMasterDTO>, RoleMenuMasterService>();
            services.AddScoped<IRolePermissionMappingService<RolePermissionMappingDTO>, RolePermissionMappingService>();
            services.AddScoped<IMenuConfigService<MenuConfigDTO>, MenuConfigService>();
            services.AddScoped<IMenuDetailService<MenuDetailDTO>, MenuDetailService>();
            services.AddScoped<IUserService<UserDTO>, UserService>();
            services.AddScoped<IUserGroupsService<UserGroupsDTO>, UserGroupsService>();
            services.AddScoped<IUserGroupMappingService<UserGroupMappingDTO>, UserGroupMappingService>();
            services.AddScoped<IUserPermissionService<UserPermissionDTO>, UserPermissionService>();
            services.AddScoped<IUserManager, FormsUserManager>();
            services.AddScoped<IWorkflowDefinitionService<WorkflowDefinitionDTO>, WorkflowDefinitionService>();
            services.AddScoped<IWorkflowStepService<WorkflowStepDTO>, WorkflowStepService>();
            services.AddScoped<IWorkflowPermissionService<WorkflowPermissionDTO>, WorkflowPermissionService>();
            services.AddScoped<IPermissionService<RoleMenuMasterDTO>, PermissionService>();
            services.AddScoped<IUserAssignedPermissionService<UserDTO>, UserAssignedPermissionService>();
            services.AddScoped<IUserAssignedRolesService<UserDTO>, UserAssignedRolesService>();

            services.AddScoped<IUserProfileService<UserProfileDTO>, UserProfileService>();
            services.AddScoped<ChangePasswordDetailsDTOValidator>();
            services.AddScoped<IValidator<ChangePasswordDetailsDTO>, ChangePasswordDetailsDTOValidator>();

            return services;
        }
    }
}
