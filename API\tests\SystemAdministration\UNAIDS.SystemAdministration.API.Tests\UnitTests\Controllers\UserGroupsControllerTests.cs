﻿using FluentValidation;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Application;
using AutoFixture;
using Moq;
using UNAIDS.SystemAdministration.API.Controllers;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using FluentValidation.Results;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class UserGroupsControllerTests
    {
        private readonly Mock<IUserGroupsService<UserGroupsDTO>> _mockService;
        private readonly Mock<IValidator<UserGroupsDTO>> _mockValidator;
        private readonly UserGroupsController _controller;
        private readonly Fixture _fixture;

        public UserGroupsControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<IUserGroupsService<UserGroupsDTO>>();
            _mockValidator = new Mock<IValidator<UserGroupsDTO>>();
            _controller = new UserGroupsController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsOkObjectResult_WithValidId()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedUserGroups = _fixture.CreateMany<UserGroupsDTO>(3).ToList();
            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(expectedUserGroups);

            //Act
            var result = await _controller.GetAll(filter);

            //Assert
            _mockService.Verify(s=>s.GetAll(filter), Times.Once);

            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = okResult.Value as dynamic;

            var resultedUserDTO = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);        
            Assert.Equal(expectedUserGroups, resultedUserDTO);
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero() {
            // Act
            var result = await _controller.Get(0);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkObject_WhenIdIsValid() {
            // Arrange
            var expectedUserGroup = _fixture.Create<UserGroupsDTO>();

            _mockService.Setup(s => s.GetById(expectedUserGroup.Id)).ReturnsAsync(expectedUserGroup);

            // Act
            var result = await _controller.Get(expectedUserGroup.Id);

            // Assert
            _mockService.Verify(s => s.GetById(expectedUserGroup.Id), Times.Once);

            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenUserGroupIsNull()

        {
            // Arrange
            UserGroupsDTO? userGroup = null;

            // Act
            var result = await _controller.Create(userGroup);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenUserGroupIsValid()
        {
            // Arrange
            var userGroup = _fixture.Create<UserGroupsDTO>();
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), default)).ReturnsAsync(new ValidationResult());
            _mockService.Setup(service => service.Create(userGroup)).ReturnsAsync(userGroup);

            // Act
            var result = await _controller.Create(userGroup);

            //Assert
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockService.Verify(s => s.Create(userGroup), Times.Once);

            Assert.IsType<CreatedAtActionResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var userGroup = _fixture.Create<UserGroupsDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Create(userGroup);

            //Asert 
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>()), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var userGroup = _fixture.Create<UserGroupsDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Update(userGroup);

            //Asert 
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>()), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);
        }


        [Fact]
        public async Task Update_ReturnsUserGroups_WhenUpdateIsSuccessful()
        {
            // Arrange
            var userGroup = _fixture.Create<UserGroupsDTO>();

            _mockService.Setup(service => service.Update(userGroup)).ReturnsAsync(userGroup);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), default)).ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update( userGroup);

            // Assert
            _mockService.Verify(s=>s.Update(userGroup),Times.Once);
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<UserGroupsDTO>(), It.IsAny<CancellationToken>()), Times.Once);

            Assert.IsType<OkObjectResult>(result);
        }


        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsInvalid()
        {
            // Arrange
            int tempId = 0;

            // Act
            var result = await _controller.Delete(tempId);

            // Assert
             Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenDeleteIsSuccessfull()
        {
            // Arrange
            int tempId = 1;
            _mockService.Setup(s => s.Delete(tempId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(tempId);

            // Assert
            _mockService.Verify(s=>s.Delete(tempId), Times.Once);
            Assert.IsType<OkResult>(result);

        }

    }
}
