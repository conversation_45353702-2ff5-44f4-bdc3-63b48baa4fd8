using UNAIDS.Core.Shared;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Add serilog to the logging pipeline
builder.AddLogger();
// Add core services (health check and memory caching)
builder.Services.AddCoreSharedServices(builder.Configuration);
// Add application service
builder.Services.AddApplicationServices();
// Add DbContext to the container
builder.Services.AddPersistence(builder.Configuration);
// Add infrastructure (business services)
builder.Services.AddInfrastructure(builder.Configuration);
// Add claim transformation & authorizations
builder.Services.AddAuthorizationService();

builder.Services.AddControllers()
    .AddJsonOptions(options => options.JsonSerializerOptions.PropertyNamingPolicy = null);
builder.Services.AddProblemDetails();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
//builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerConfig(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseCors("CustomCorsPolicy");

// Add middleware for JWT-based authentication
app.UseAuthentication();

app.UseAuthorization();

app.UseMultitenancy();


app.MapControllers();

app.Run();
