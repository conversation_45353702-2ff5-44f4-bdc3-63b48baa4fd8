﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Core.Shared;
using UNAIDS.HivScorecards.Domain.TenantManagement;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class ProgramRepository(TenantManagementDbContext dbContext) : BaseRepository<Program>(dbContext), IProgramRepository
    {
        public override async Task<Program> AddAsync(Program program)
        {
            await dbContext.Programs.AddAsync(program);

            // Set shadow property explicitly
            dbContext.Entry(program).Property("TenantId").CurrentValue = program.BusinessUnitId;

            return program;
        }

        /// <summary>
        /// Get the list of modules
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetModules()
        {
            return await dbContext.Modules.AsNoTracking()
                .OrderBy(x => x.Id)
                 .Select(x => new CommonLookUp()
                 {
                     Value = x.Id,
                     DisplayText = x.Name,
                 }).ToListAsync();
        }

        /// <summary>
        /// Get the list of roles
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetRoles()
        {
            return await dbContext.Roles.AsNoTracking()
                 .Select(x => new CommonLookUp()
                 {
                     Value = x.Id,
                     DisplayText = x.Name,
                 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetMenus()
        {
            return await dbContext.Menus.AsNoTracking()
                .Where(x => x.ControllerType == "component"
                && x.ObjectType == (int)AppConstants.MenuType.Master)
                .OrderBy(x => x.Id)
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Title,
                    GroupId = x.ModuleId
                }).ToListAsync();
        }

        public Task<List<CommonLookUp>> GetModels()
        {
            throw new NotImplementedException();
        }

        public Task<List<CommonLookUp>> GetDataElements()
        {
            throw new NotImplementedException();
        }
    }
}
