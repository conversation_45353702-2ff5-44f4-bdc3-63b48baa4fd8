﻿using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.SystemAdministration.Application
{
    public interface ITenantRepository : IBaseRepository<BusinessUnit>
    {
        Task<UserPermission> AddUserPermission(UserPermission userPermission);
        string GetUserPermissionTable();
        Task<Dictionary<string, int>> GetGrantStages();
        string GetProgramModelTable();
        string GetProgramConceptTable();
        string GetProgramDataElementTable();
        string GetProgramCategoryTable();
        string GetProgramCategoryOptionTable();
        Task<List<ModuleMenuDTO>> GetModuleMenus();
        string GetRoleMenuMasterTable();
        Task<RoleMenuMaster> AddRoleMenuMaster(RoleMenuMaster roleMenuMaster, int? tenantId);
        Task<List<RoleMenuMapping>> AddRoleMenuMapping(List<RoleMenuMapping> roleMenuMappings, int? tenantId);
    }
}
