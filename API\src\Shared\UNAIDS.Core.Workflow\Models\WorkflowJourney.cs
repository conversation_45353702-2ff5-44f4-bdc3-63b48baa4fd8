﻿using System.Text.Json;

namespace UNAIDS.Core.Workflow.Models
{
    public class WorkflowJourney
    {
        public List<WorkflowJourneyStep>? Journey { get; set; }
        public List<WorkflowJourneyHistory>? LogStatus { get; set; }
        public List<WorkflowAction>? ActionItems { get; set; }
        public WorkflowStatusInfo? CurrentStep { get; set; }
    }

    public class WorkflowJourneyStep
    {
        public int StepId { get; set; }
        public int? StepNo { get; set; }
        public string? Status { get; set; }
        public string? Action { get; set; }
        public string? ChildAction { get; set; }
        public short? IsForward { get; set; }
        public short? AllowRollback { get; set; }
        public WorkflowData? WorkflowData { get; set; }
        public List<WorkflowJourneyStep>? ChildSteps { get; set; }
        public short? CanPause { get; set; }
        public string? ReturnStatus { get; set; }
    }

    public class WorkflowJourneyHistory : WorkflowJourneyStep
    {
        public Guid? HistoryId { get; set; }
        public DateTime? LogDate { get; set; }
        public string? Comments { get; set; }
        public short? IsCompleted { get; set; }
        public string? Username { get; set; }
        public JsonDocument? ChildData { get; set; }
        public List<ChildWorkflowHistory>? ChildWorkflowHistory { get; set; }
    }

    public class ChildWorkflowHistory : WorkflowJourneyHistory
    {
        public string? ChildStepId { get; set; }
    }

    public class WorkflowAction : WorkflowJourneyStep
    {
        public int Id { get; set; }
        public short? AllowSharing { get; set; }
        public short? AllowCustomMessage { get; set; }
        public string? Recipient { get; set; }
        public string? Template { get; set; }
    }

    public class WorkflowStatusInfo
    {
        public Guid WorkflowHistoryId { get; set; }
        public int WorkFlowStepId { get; set; }
        public int? ParentId { get; set; }
        public int? CanPause { get; set; }
        public short? IsLocked { get; set; }
        public Guid InstanceId { get; set; }
        public string? Entity { get; set; }
        public int? ReturnStatus { get; set; }
        public string? ReturnStatusDescription { get; set; }
    }
}
