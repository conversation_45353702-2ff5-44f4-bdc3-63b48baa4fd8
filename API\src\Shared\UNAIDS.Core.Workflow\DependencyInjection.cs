﻿using Microsoft.Extensions.DependencyInjection;

namespace UNAIDS.Core.Workflow
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddWorkflow(this IServiceCollection services)
        => services
                .AddWorkflowEngine()
                .AddAutoMapper(typeof(WorkflowMappingProfile));

        private static IServiceCollection AddWorkflowEngine(this IServiceCollection services)
        {
            services.AddTransient<IWorkflowRegistry, WorkflowRegistry>();
            services.AddTransient<IWorkflowExecutor, WorkflowExecutor>();
            services.AddTransient<IWorkflowUtils, WorkflowUtils>();
            services.AddTransient<IWorkflowTransition, MasterWorkflowTransition>();
            services.AddTransient<IWorkflowHistoryRepository, WorkflowHistoryRepository>();
            services.AddTransient<IWorkflowRepository, WorkflowRepository>();
            services.AddTransient<IWorkflowReturnLogRepository, WorkflowReturnLogRepository>();

            return services;
        }

    }
}
