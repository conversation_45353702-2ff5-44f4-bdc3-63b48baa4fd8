using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/menu-detail")]
    [ApiController]
    public class MenuDetailController(IMenuDetailService<MenuDetailDTO> menuDetailService, IValidator<MenuDetailDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Get all MenuDetails
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUDETAIL}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var menuDetails = await menuDetailService.GetAll(filter);
            return Ok(new
            {
                Record = menuDetails,
                MenuConfigId = await menuDetailService.GetMenuConfig(),
                UserRigts = menuDetailService.AccessRights(SubjectTypes.MENUDETAIL)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get a specific MenuDetail by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUDETAIL}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            var data = await menuDetailService.GetById(id);

            if(id == 0)
                return BadRequest();

            return Ok(new
            {
                Record = data,
                MenuConfigId = await menuDetailService.GetMenuConfig(),
                UserRights = menuDetailService.AccessRights(SubjectTypes.MENUDETAIL)
            });
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new MenuDetail
        /// </summary>
        /// <param name="MenuDetail"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.MENUDETAIL}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] MenuDetailDTO menuDetail)
        {
            if (menuDetail == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(menuDetail);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await menuDetailService.Create(menuDetail);
            return CreatedAtAction(nameof(Create), new { id = menuDetail.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a MenuDetail
        /// </summary>
        /// <param name="MenuDetail"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.MENUDETAIL}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] MenuDetailDTO menudetail)
        {
            var validationResult = await validator.ValidateAsync(menudetail);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await menuDetailService.Update(menudetail);
            return Ok(menudetail);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a MenuDetail
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUDETAIL}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await menuDetailService.Delete(id);
            return Ok();
        }
        #endregion
    }
}