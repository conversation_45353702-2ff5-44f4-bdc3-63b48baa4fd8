﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_8 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_model_headers_model_schemas_schema_id",
                schema: "pan_gms",
                table: "model_headers");

            migrationBuilder.DropIndex(
                name: "ix_model_headers_schema_id",
                schema: "pan_gms",
                table: "model_headers");

            migrationBuilder.DropColumn(
                name: "schema_id",
                schema: "pan_gms",
                table: "model_headers");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "schema_id",
                schema: "pan_gms",
                table: "model_headers",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "ix_model_headers_schema_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "schema_id");

            migrationBuilder.AddForeignKey(
                name: "fk_model_headers_model_schemas_schema_id",
                schema: "pan_gms",
                table: "model_headers",
                column: "schema_id",
                principalSchema: "pan_gms",
                principalTable: "model_schemas",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
