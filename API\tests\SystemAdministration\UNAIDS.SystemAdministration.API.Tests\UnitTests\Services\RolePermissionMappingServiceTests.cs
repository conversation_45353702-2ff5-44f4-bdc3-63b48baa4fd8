using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class RolePermissionMappingServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IRolePermissionMappingRepository> _mockRolePermissionMappingRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly RolePermissionMappingService _rolePermissionMappingService;
        private readonly Fixture _fixture;

        public RolePermissionMappingServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockRolePermissionMappingRepository = new Mock<IRolePermissionMappingRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IRolePermissionMappingRepository>())
                           .Returns(_mockRolePermissionMappingRepository.Object);

            
            _rolePermissionMappingService = new RolePermissionMappingService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }
        
    }
}
