﻿using UNAIDS.Core.Workflow.Models;

namespace UNAIDS.Core.Workflow
{
    public interface IWorkflowRegistry
    {
        Task<WorkflowExecutorResult> StartWorkflow(int workflowId, int userId, Variables variables);
        Task<WorkflowExecutorResult> StartWorkflow(string workflowName, int userId, Variables variables);
        Task<WorkflowExecutorResult> StartWorkflow(string workflowDefinitionName, int userId);
        Task<WorkflowExecutorResult> TriggerWorkflow(Guid workflowInstanceId, int stepId, int userId, Variables variables);
        Task<WorkflowExecutorResult> TriggerWorkflow(Guid workflowInstanceId, int stepId, int userId);
        Task<WorkflowJourney> GetHistoryWithActionItem(Guid workflowInstanceId, int userId, string language);
        Task<object> GetWorkflowSteps(string workflowName);
        Task<WorkflowExecutorResult> Return(Guid workflowInstaceId, int userId, Variables? variables);
        Task<WorkflowExecutorResult> ReSubmit(Guid workflowInstaceId, int userId, Variables? variables);
    }
}
