using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class WorkflowPermissionDTOValidator : AbstractValidator<WorkflowPermissionDTO>
    {
        public WorkflowPermissionDTOValidator()
        {
            // WorkflowDefinitionId: Required and must be greater than 0
            RuleFor(x => x.WorkflowDefinitionId)
                .GreaterThan(0).WithMessage("The WorkflowDefinitionId must be greater than 0.");

            RuleFor(x => x.RoleId)
                .GreaterThan(0).WithMessage("The WorkflowDefinitionId must be greater than 0.");



        }
    }
}
