using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class PermissionControllerTests {
        private readonly Mock<IPermissionService<RoleMenuMasterDTO>> _mockService;
        private readonly Mock<IValidator<RoleMenuMasterDTO>> _mockValidator;
        private readonly PermissionController _controller;
        private readonly Fixture _fixture;

        public PermissionControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IPermissionService<RoleMenuMasterDTO>>();
            _mockValidator = new Mock<IValidator<RoleMenuMasterDTO>>();
            _controller = new PermissionController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedReturnData = _fixture.CreateMany<RoleMenuMasterDTO>(3).ToList();
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AED"; 
            
            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(service => service.GetModules()).ReturnsAsync(expectedModules);
            _mockService.Setup(service => service.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.PERMISSION)).Returns(expectedUserRights);


            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var roleValue = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var moduleValue = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnitValue = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedRoles, roleValue);
            Assert.Equal(expectedModules, moduleValue);
            Assert.Equal(expectedBusinessUnits, businessUnitValue);
            Assert.Equal(expectedUserRights, userRightsValue);
        }


        [Fact]
        public async Task Get_ReturnsBadRequest_WithZeroId() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedReturnData = _fixture.Create<RoleMenuMasterDTO>();   
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedUserRights = "AED";

            _mockService.Setup(service => service.GetById(expectedReturnData.Id)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(service => service.GetModules()).ReturnsAsync(expectedModules);
            _mockService.Setup(service => service.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.PERMISSION)).Returns(expectedUserRights);

     
            // Act
            var result = await _controller.Get(expectedReturnData.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var roleValue = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var moduleValue = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnitValue = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedRoles, roleValue);
            Assert.Equal(expectedModules, moduleValue);
            Assert.Equal(expectedBusinessUnits, businessUnitValue);
            Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenRoleMenuMasterIsNull() {
            // Arrange
            RoleMenuMasterDTO? roleMenuMaster = null;

            // Act
            var result = await _controller.Create(roleMenuMaster);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<RoleMenuMasterDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenRoleMenuMasterIsValid() {
            // Arrange
            var roleMenuMaster = _fixture.Create<RoleMenuMasterDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(roleMenuMaster, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(roleMenuMaster)).ReturnsAsync(roleMenuMaster);

            // Act
            var result = await _controller.Create(roleMenuMaster);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(roleMenuMaster, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<RoleMenuMasterDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenRoleMenuMasterIsValid() {
            // Arrange
            var roleMenuMaster = _fixture.Create<RoleMenuMasterDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(roleMenuMaster, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(roleMenuMaster)).ReturnsAsync(roleMenuMaster);

            // Act
            var result = await _controller.Update(roleMenuMaster);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var roleMenuMaster = _fixture.Create<RoleMenuMasterDTO>();
           _mockService.Setup(s => s.Delete(roleMenuMaster.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(roleMenuMaster.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}