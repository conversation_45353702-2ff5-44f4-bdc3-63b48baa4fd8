﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class UserConfiguration : IEntityTypeConfiguration<User>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable("user_info");
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever();
            builder.Property(c => c.UserName).IsRequired().HasMaxLength(100);
            builder.Property(c => c.LoginName).IsRequired().HasMaxLength(100);
            builder.Property(c => c.PasswordHash).IsRequired().HasMaxLength(256);
            builder.Property(c => c.Email).IsRequired().HasMaxLength(100);

            builder.HasOne(e => e.Role)
                .WithMany()
                .HasForeignKey(e => e.RoleId)
                .HasPrincipalKey(e => e.Id);

            // Indexes for "normalized" loginname and email, to allow efficient lookups
            builder.HasIndex(c => c.NormalizedLoginName).HasDatabaseName("LoginNameIndex").IsUnique();
            builder.HasIndex(c => c.NormalizedEmail).HasDatabaseName("EmailIndex").IsUnique();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new UserConfiguration());
        }
    }
}
