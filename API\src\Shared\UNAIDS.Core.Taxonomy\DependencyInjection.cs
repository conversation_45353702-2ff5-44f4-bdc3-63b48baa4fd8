﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Base;
using UNAIDS.Core.Taxonomy.Services;

namespace UNAIDS.Core.Taxonomy
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddTaxonomyServices(this IServiceCollection services, IConfiguration configuration)
            => services
                .AddScoped<IGenericDropdownRepository, GenericDropdownRepository>()
                .AddScoped<ITaxonomyRepository, TaxonomyRepository>()
                .AddScoped<ITaxonomyService, TaxonomyService>();
    }
}
