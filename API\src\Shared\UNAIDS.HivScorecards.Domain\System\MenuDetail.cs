namespace UNAIDS.HivScorecards.Domain.System
{
    public class MenuDetail: BaseEntity
    {
        public int MenuConfigId { get; set; }
        public virtual Menu? MenuConfig { get; set; }
        public int? MenuAdd { get; set; }
        public int? MenuModify { get; set; }
        public int? Menucancel { get; set; }
        public int? MenuView { get; set; }
        public int? MenuPrint { get; set; }
        public int? MenuRePrint { get; set; }
        public int MenuDelete { get; set; }
        public int? MenuProcess { get; set; }
        public int MenuApprove { get; set; }
        public int MenuPreDatedEntry { get; set; }
        public int MenuImport { get; set; }
        public int MenuExport { get; set; }
        public int MenuValidation { get; set; }
        public int? MenuComments { get; set; }
        public int? MenuCorrect { get; set; }
        public int? MenuBulkImport { get; set; }
        public int? MenuExportRecord { get; set; }
        public int? MenuJSONEdit { get; set; }
        public int? MenuRestrictedView { get; set; }
        public int? MenuSpecial1 { get; set; }
        public int? MenuSpecial2 { get; set; }
        public int? MenuSpecial3 { get; set; }
        public int? MenuSpecial4 { get; set; }
        public int? MenuSpecial5 { get; set; }
        public int? MenuToolTip { get; set; }
        public short? IsSystemDefined { get; set; }
        public short? Active { get; set; }
        public int? ModelDefinitionId { get; set; }
    }
}