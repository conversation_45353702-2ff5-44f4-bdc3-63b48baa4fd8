using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class MenuConfigDTOValidator : AbstractValidator<MenuConfigDTO>
    {
        public MenuConfigDTOValidator()
        {
            // Title: Required and max length of 200
            RuleFor(x => x.Title)
                .NotEmpty().WithMessage("The Title is required.")
                .MaximumLength(200).WithMessage("The Title must be at most 200 characters long.");

            // ComponentName: Optional, max length of 100
            RuleFor(x => x.ComponentName)
                .MaximumLength(100).WithMessage("The Component Name must be at most 100 characters long.");

             // ModuleId: Required
            RuleFor(x => x.ModuleId)
                .NotNull().WithMessage("The Module Name is required.");

            // MenuType: Required and must be greater than 0
            RuleFor(x => x.MenuType)
                .GreaterThan(0).WithMessage("The Menu Type is required.");

            // URL: Optional, must be a valid URL if provided
            RuleFor(x => x.URL)
                .MaximumLength(200).WithMessage("The URL must be at most 200 characters long.")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
                .When(x => !string.IsNullOrEmpty(x.URL))
                .WithMessage("The URL must be a valid absolute URL.");

            // SortOrder: Optional, must be positive if provided
            RuleFor(x => x.SortOrder)
                .GreaterThanOrEqualTo(0).When(x => x.SortOrder.HasValue)
                .WithMessage("The Sort Order must be zero or greater.");

            // IsHidden: Optional, must be 0 or 1 if provided
            RuleFor(x => x.IsHidden)
                .InclusiveBetween((short)0, (short)1).When(x => x.IsHidden.HasValue)
                .WithMessage("The IsHidden field must be 0 (false) or 1 (true).");

            // Icon: Optional, max length of 50
            RuleFor(x => x.Icon)
                .MaximumLength(50).WithMessage("The Icon must be at most 50 characters long.");

            // Color: Optional, max length of 20
            RuleFor(x => x.Color)
                .MaximumLength(20).WithMessage("The Color must be at most 20 characters long.");

            // Path: Optional, max length of 200
            RuleFor(x => x.Path)
                .MaximumLength(200).WithMessage("The Path must be at most 200 characters long.");

            // ConfigurationSettings: Optional, max length of 500
            RuleFor(x => x.ConfigurationSettings)
                .MaximumLength(500).WithMessage("The Configuration Settings must be at most 500 characters long.");

            // ControllerType: Optional, max length of 100
            RuleFor(x => x.ControllerType)
                .MaximumLength(100).WithMessage("The Controller Type must be at most 100 characters long.");

            // Area: Optional, max length of 100
            RuleFor(x => x.Area)
                .MaximumLength(100).WithMessage("The Area must be at most 100 characters long.");

            // Controller: Optional, max length of 100
            RuleFor(x => x.Controller)
                .MaximumLength(100).WithMessage("The Controller must be at most 100 characters long.");

            // Action: Optional, max length of 100
            RuleFor(x => x.Action)
                .MaximumLength(100).WithMessage("The Action must be at most 100 characters long.");
        }

    }
}
