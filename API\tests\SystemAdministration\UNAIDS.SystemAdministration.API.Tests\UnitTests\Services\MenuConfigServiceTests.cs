using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class MenuConfigServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IMenuConfigRepository> _mockMenuConfigRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly MenuConfigService _menuConfigService;
        private readonly Fixture _fixture;

        public MenuConfigServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockMenuConfigRepository = new Mock<IMenuConfigRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IMenuConfigRepository>())
                           .Returns(_mockMenuConfigRepository.Object);

            
            _menuConfigService = new MenuConfigService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetMenuHierarchy_ReturnsListOfMenuHierarchy()
        {
            // Arrange          
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
            var expectedHierarchy = _fixture.CreateMany<MCHierarchyDTO>(2).ToList();
            _mockMenuConfigRepository.Setup(repo => repo.GetMenuHierarchy())
                                        .ReturnsAsync(expectedHierarchy);

            // Act
            var result = await _menuConfigService.GetMenuHierarchy();

            // Assert
            Assert.Equal(expectedHierarchy, result);
            _mockMenuConfigRepository.Verify(repo => repo.GetMenuHierarchy(), Times.Once); 
        }

        [Fact]
        public async Task GetMenuConfig_ReturnsListOfMenuConfig()
        {
            // Arrange          
            var expectedMenuConfigs = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockMenuConfigRepository.Setup(repo => repo.GetMenuConfig())
                                        .ReturnsAsync(expectedMenuConfigs);

            // Act
            var result = await _menuConfigService.GetMenuConfig();

            // Assert
            Assert.Equal(expectedMenuConfigs, result);
            _mockMenuConfigRepository.Verify(repo => repo.GetMenuConfig(), Times.Once); 
        }

        [Fact]
        public async Task GetModule_ReturnsListOfModules()
        {
            // Arrange          
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockMenuConfigRepository.Setup(repo => repo.GetModule())
                                        .ReturnsAsync(expectedModules);

            // Act
            var result = await _menuConfigService.GetModule();

            // Assert
            Assert.Equal(expectedModules, result);
            _mockMenuConfigRepository.Verify(repo => repo.GetModule(), Times.Once); 
        }

        [Fact]
        public async Task GetLookUp_ReturnsListOfLookUps()
        {
            // Arrange 
            var fieldName = "FieldName";
            var flag = false;         
            var expectedLookUps = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockMenuConfigRepository.Setup(repo => repo.GetLookUp(fieldName, flag))
                                        .ReturnsAsync(expectedLookUps);

            // Act
            var result = await _menuConfigService.GetLookUp(fieldName, flag);

            // Assert
            Assert.Equal(expectedLookUps, result);

            _mockMenuConfigRepository.Verify(repo => repo.GetLookUp(fieldName, flag), Times.Once); 
        }

        [Fact]
        public async Task GetObjectTypes_ReturnsListOfObjectTypes()
        {
            // Arrange 
            var fieldName = "FieldName";    
            var expectedObjectTypes = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockMenuConfigRepository.Setup(repo => repo.GetObjectTypes(fieldName)).ReturnsAsync(expectedObjectTypes);

            // Act
            var result = await _menuConfigService.GetObjectTypes(fieldName);

            // Assert
            Assert.Equal(expectedObjectTypes, result);

            _mockMenuConfigRepository.Verify(repo => repo.GetObjectTypes(fieldName), Times.Once); 
        }

        [Fact]
        public async Task GetMenuConfigDetails_ReturnsListOfMenuConfigDetails()
        {
            // Arrange    
            var expectedObjectTypes = _fixture.CreateMany<CommonLookUp>(2).ToList();
            
            _mockMenuConfigRepository.Setup(repo => repo.GetMenuConfigDetails()).ReturnsAsync(expectedObjectTypes);

            // Act
            var result = await _menuConfigService.GetMenuConfigDetails();

            // Assert
            Assert.Equal(expectedObjectTypes, result);

            _mockMenuConfigRepository.Verify(repo => repo.GetMenuConfigDetails(), Times.Once); 
        }

    }
}
