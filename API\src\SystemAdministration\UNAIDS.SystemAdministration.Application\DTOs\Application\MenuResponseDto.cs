﻿using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.SystemAdministration.Application
{
    public class MenuResponseDto
    {
        public List<LanguageLookupDto>? Language { get; set; }
        public List<CommonLookUp>? Tenants { get; set; }
        public string? Locale { get; set; }
        public string? CurrencySymbol { get; set; }
        public string? DecimalSeparator { get; set; }
        public List<ModuleGroupDto>? ModuleGroup { get; set; }
        public List<MenuDto>? Menu { get; set; }
        public string? AccountingLocation { get; set; }
        public string? AppGreeting { get; set; }
        public List<ModuleDto>? Modules { get; set; }
        public string? UserName { get; set; }
        public string? Email { get; set; }
        public DateTime? Date { get; set; }
        public string? Profile { get; set; }
        public string? AppInstanceIndicator { get; set; }
        public int? AnnouncementCount { get; set; } = 0;
        public string? AppInstanceName { get; set; }
        public ThemeDto? Theme { get; set; }
        public string? DateFormat { get; set; }
        public string? DateWithTimeFormat { get; set; }
        public string? DateWithShortTimeFormat { get; set; }
        public int BusinessUnitId { get; set; }
        public int UserId { get; set; }
        public int[]? Roles { get; set; }
        public string? AppName { get; set;}
        public double? TokenLifeTime { get; set; }
        public double? TokenLifeTimeWarning { get; set; }

    }

    public class LanguageLookupDto
    {
        public string? Value { get; set; }
        public string? DisplayText { get; set; }
        public short Active { get; set; }
    }

    public class ModuleGroupDto
    {
        public string? Icon {get; set;}
        public int? Width { get; set;}
        public int? Height { get; set;}
        public string? Color { get; set;}
        public int Id { get; set; }
        public string? Title { get; set; }
        public string? Name { get; set; }
        public string? Teaser { get; set; }
        public string? IconOffset { get; set; }
        public string? ModuleCaption { get; set; }
        public string? GridPosition { get; set; }
        public string? ModuleiconPosition { get; set; }
        public string? URL { get; set; }
        public string? CallBack { get; set; }
        public bool? IsDisabled {  get; set; }
        public List<ModuleDto>? Modules { get; set; }
    }

    public class MenuDto
    {
        public int Id { get; set; }

        public string Name { get; set; } = string.Empty;

        public string? MenuLink { get; set; }

        public string? Action { get; set; }

        public string URL { get; set; } = string.Empty;

        public string? Controller { get; set; }

        public string? Area { get; set; }

        public int? ParentId { get; set; }

        public int? SortOrder { get; set; }

        public int? ModuleId { get; set; }

        public int? ModuleGroupId { get; set; }

        public List<MenuDto>? Children { get; set; }

        public string Color { get; set; } = string.Empty;

        public string ParentName { get; set; } = string.Empty;

        public string File { get; set; } = string.Empty;

        public string BaseUrl { get; set; } = string.Empty;

        public string? ControllerType { get; set; }

        public int? SourceModuleId { get; set; }

        public string Module { get; set; } = string.Empty;

        public string? ModuleTeaser { get; set; }

        public string Service { get; set; } = string.Empty;

        public string ScreenLayout { get; set; } = string.Empty;

        public string? MenuParamJson { get; set; }

        public string? IconColor { get; set; }

        public string? MenuIcon { get; set; }
    }

    public class ModuleDto
    {
        public int ModuleId { get; set; }
        public string? ModuleTeaser { get; set; }
        public string? ModuleName { get; set; }
        public int ModuleOrder { get; set; }
        public int ModuleGroupId { get; set; }
        public string? SourceUrl { get; set; }
        public string? DeployedGroup { get; set; }
        public int[]? Roles { get; set; }
    }

    public class ThemeDto
    {
        public string? Color { get; set; }
        public string? FontSize { get; set; }
        public string? FontFamily { get; set; }
        public string? Layout { get; set; }
        public string? SecondaryColor { get; set; }
    }
}
