﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class WorkflowReturnLogConfiguration : IEntityTypeConfiguration<WorkflowReturnLog>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowReturnLog> builder)
        {
            builder.ToTable("workflow_return_logs");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedOnAdd();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowReturnLogConfiguration());
        }
    }
}
