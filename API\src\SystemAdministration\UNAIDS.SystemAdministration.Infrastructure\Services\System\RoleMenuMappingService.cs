using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class RoleMenuMappingService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
                                     IMapper mapper,
                                     ISequenceRepository sequenceRepository,
        IUserContext userContext) :
        BaseAPIService<RoleMenuMapping, SystemAdminDbContext, RoleMenuMappingDTO, IRoleMenuMappingRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IRoleMenuMappingService<RoleMenuMappingDTO>
    {
        public override async Task<RoleMenuMappingDTO> Update(RoleMenuMappingDTO roleMenuMapping)
        {
            var obj = mapper.Map<RoleMenuMapping>(roleMenuMapping);

            if (obj.Id == null || obj.Id == 0)
            {
                await unitOfWork.Repository<IRoleMenuMappingRepository>().AddAsync(obj);
            }
            else
            {
                await unitOfWork.Repository<IRoleMenuMappingRepository>().UpdateAsync(obj);
            }

            await unitOfWork.SaveChangesAsync();

            return mapper.Map<RoleMenuMappingDTO>(obj);
        }

        public async Task<List<RoleMenuMappingDTO>> BulkUpdate(List<RoleMenuMappingDTO> permissions)
        {
            List<RoleMenuMappingDTO> result = new();
            List<RoleMenuMapping> newPerms = new();
            List<RoleMenuMapping> extPerms = new();

            if(permissions.Count > 0)
            {
                var existingPermissions =  await unitOfWork.Repository<IRoleMenuMappingRepository>().GetExistingPermissions(permissions[0].RoleMenuMasterId ?? 0);

                foreach (var perm in permissions)
                {
                    var obj = mapper.Map<RoleMenuMapping>(perm);

                    if (!existingPermissions.Any(x => x.MenuId == obj.MenuId))
                    {
                        //await unitOfWork.Repository<IRoleMenuMappingRepository>().AddAsync(obj);
                        // await unitOfWork.SaveChangesAsync();
                        newPerms.Add(obj);
                    }
                    else
                    {
                        obj.Id = existingPermissions.FirstOrDefault(x => x.MenuId == obj.MenuId)?.Id ?? 0;
                        //await unitOfWork.Repository<IRoleMenuMappingRepository>().UpdateAsync(obj);
                        //await unitOfWork.SaveChangesAsync();
                        extPerms.Add(obj);
                    }

                }

                await unitOfWork.Repository<IRoleMenuMappingRepository>().AddManyAsync(newPerms);

                foreach (var perm in extPerms)
                {
                    await unitOfWork.Repository<IRoleMenuMappingRepository>().UpdateAsync(perm);
                }

                await unitOfWork.SaveChangesAsync();


                foreach (var perm in newPerms.Union(extPerms))
                {
                    result.Add(mapper.Map<RoleMenuMappingDTO>(perm));
                }
            }            

            return result;
        }



        public async Task<List<RoleMenuMappingDTO>> GetRoleMenus(int rolemenuId)
        {
            return await unitOfWork.Repository<IRoleMenuMappingRepository>().GetRoleMenus(rolemenuId);
        }

        /// <summary>
        /// Get menu config dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await unitOfWork.Repository<IRoleMenuMappingRepository>().GetMenuConfig();
        }


        /// <summary>
        /// Get lookup dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false)
        {
            return await unitOfWork.Repository<IRoleMenuMappingRepository>().GetLookUp(fieldName, false);
        }

        
        /// <summary>
        /// Save permission settings
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> SavePermission()
        {
            // Implement save permission logic if applicable, for now returning empty list
            return await Task.FromResult(new List<CommonLookUp>());
        }


    }
}
