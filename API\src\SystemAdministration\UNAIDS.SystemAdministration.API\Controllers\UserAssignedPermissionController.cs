using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Authentication.Shared;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-assigned-permissions")]
    [ApiController]
    public class UserAssignedPermissionController(IUserAssignedPermissionService<UserDTO> userAssignedPermissionService) : ControllerBase
    {
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.VIEW}")]
        [HttpGet("get-user-assigned-permissions")]
        public async Task<IActionResult> GetUserAssignedPermission([FromQuery] Filter filter)
        {
            var permissions = await userAssignedPermissionService.GetUserAssignedPermissions(filter);
            return Ok(new
            {
                Record = permissions,
                BusinessUnitId = await userAssignedPermissionService.GetBusinessUnits(),
                UserId = await userAssignedPermissionService.GetUserNames(),
                ScreenName = await userAssignedPermissionService.GetScreenName(),
                Module = await userAssignedPermissionService.GetModule(),
                Role = await userAssignedPermissionService.GetRole(),
                Title = "",
                UserRights = userAssignedPermissionService.AccessRights(SubjectTypes.USER)
            });
        }
    }
}


