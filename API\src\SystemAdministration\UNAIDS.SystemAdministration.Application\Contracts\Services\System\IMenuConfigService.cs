using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IMenuConfigService<T> : IBaseAPIService<T> where T : class
    {
        Task<List<MCHierarchyDTO>> GetMenuHierarchy();
        Task<List<CommonLookUp>> GetMenuConfig();
        Task<List<CommonLookUp>> GetModule();
        Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false);
        Task<List<CommonLookUp>> GetObjectTypes(string fieldName);
        Task<List<CommonLookUp>> GetMenuConfigDetails();
    }
}
