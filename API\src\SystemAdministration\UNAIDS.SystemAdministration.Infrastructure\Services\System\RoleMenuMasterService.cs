using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class RoleMenuMasterService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
                                     IMapper mapper,
                                     ISequenceRepository sequenceRepository,
        IUserContext userContext) : 
        BaseAPIService<RoleMenuMaster, SystemAdminDbContext, RoleMenuMasterDTO, IRoleMenuMasterRepository>(unitOfWork, mapper, sequenceRepository, userContext), 
        IRoleMenuMasterService<RoleMenuMasterDTO>
    {

        
        /// <summary>
        /// Get business unit dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await unitOfWork.Repository<IRoleMenuMasterRepository>().GetBusinessUnits();
        }

        /// <summary>
        /// Get role dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetRoles()
        {
            return await unitOfWork.Repository<IRoleMenuMasterRepository>().GetRoles();
        }

        /// <summary>
        /// Get module dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> Getmodules()
        {
            return await unitOfWork.Repository<IRoleMenuMasterRepository>().Getmodules();
        }

    }
}
