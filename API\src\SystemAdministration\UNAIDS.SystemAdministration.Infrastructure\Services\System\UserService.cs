using AutoMapper;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserService(IUnitOfWork<CommonDbContext> unitOfWork,
                                    IMapper mapper,
                                    ISequenceRepository sequenceRepository,
                                    IUserManager userManager,
        IUserContext userContext,
        ICommonRepository commonRepository)
        : BaseAPIService<User, CommonDbContext, UserDTO, IUserRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IUserService<UserDTO>
    {

        /// <summary>
        /// Get all users
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public override async Task<List<UserDTO>> GetAll(Filter filter)
        {
            // return get all users...
            return await userManager.GetAll();
        }

        /// <summary>
        /// Get the user by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public override async Task<UserDTO> GetById(int id)
        {
            return await userManager.GetById(id);
        }

        /// <summary>
        /// Create the user
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public override async Task<UserDTO> Create(UserDTO user)
        {
            return await userManager.AddUser(user);
        }

        /// <summary>
        /// Get searchable user dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetSearchable(int id, string fieldName)
        {
            return await unitOfWork.Repository<IUserRepository>().GetSearchable(id, fieldName);
        }


        /// <summary>
        /// Get role dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetRole()
        {
            return await unitOfWork.Repository<IUserRepository>().GetRole();
        }

        /// <summary>
        /// Get business units dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await commonRepository.GetTenants();
        }

    }
}
