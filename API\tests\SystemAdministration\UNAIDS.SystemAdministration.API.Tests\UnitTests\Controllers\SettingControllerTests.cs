﻿using AutoFixture;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Mvc;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class SettingControllerTests
    {
        private readonly Mock<ISettingService<SettingDTO>> _mockService;
        private readonly Mock<IValidator<SettingDTO>> _mockValidator;
        private readonly SettingController _controller;
        private readonly Fixture _fixture;


        public SettingControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<ISettingService<SettingDTO>>();
            _mockValidator = new Mock<IValidator<SettingDTO>>();
            _controller = new SettingController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsSettings_WithValidId()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedSetting = _fixture.CreateMany<SettingDTO>(3).ToList();
            var expectedModules = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedDataTypes = _fixture.CreateMany<CommonLookUp>(3).ToList();
  
  
            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(expectedSetting);
            _mockService.Setup(s => s.GetModules()).ReturnsAsync(expectedModules);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.GetDataTypes()).ReturnsAsync(expectedDataTypes); 
            
            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            _mockService.Verify(service => service.GetAll(filter), Times.Once);
            _mockService.Verify(service => service.GetDataTypes(), Times.Once);
            _mockService.Verify(service => service.GetModules(), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);     

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            
            var roles = returnValue.GetType().GetProperty("DataTypeId")?.GetValue(returnValue);
            var modules = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnits = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRigts")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedSetting, recordValue); 
            Assert.Equal(expectedDataTypes, expectedDataTypes);
            Assert.Equal(expectedModules, modules); 
            Assert.Equal(expectedBusinessUnits, businessUnits); 
            
        }

       
        [Fact]
        public async Task Get_ReturnsOkResult_WithCorrectData()
        {

            //Assign
            var expectedData = _fixture.Create<SettingDTO>();
            var expectedModules = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(3).ToList();
            var expectedDataTypes = _fixture.CreateMany<CommonLookUp>(3).ToList();

            _mockService.Setup(s => s.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockService.Setup(s => s.GetModules()).ReturnsAsync(expectedModules);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.GetDataTypes()).ReturnsAsync(expectedDataTypes); 

            //Act
            var result = await _controller.Get(expectedData.Id);

            //Assert
            _mockService.Verify(service => service.GetById(expectedData.Id), Times.Once);
            _mockService.Verify(service => service.GetDataTypes(), Times.Once);
            _mockService.Verify(service => service.GetModules(), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);  

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            
            var roles = returnValue.GetType().GetProperty("DataTypeId")?.GetValue(returnValue);
            var modules = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnits = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedData, recordValue); 
            Assert.Equal(expectedDataTypes, expectedDataTypes);
            Assert.Equal(expectedModules, modules); 
            Assert.Equal(expectedBusinessUnits, businessUnits); 

        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {

            //Assign
            var id = 0;
      
            //Act
            var result = await _controller.Get(id);

            //Assert
            Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenDataIsNull()
        
        {
            // Arrange
            SettingDTO? setting = null;

            // Act
            var result = await _controller.Create(setting);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenSettingIsValid()
        {
            // Arrange
            var setting = _fixture.Create<SettingDTO>(); 

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default)).ReturnsAsync(new ValidationResult()); 
            _mockService.Setup(service => service.Create(setting)).ReturnsAsync(setting);

            // Act
            var result = await _controller.Create(setting);

            //Assert
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default), Times.Once);
            _mockService.Verify(service => service.Create(setting), Times.Once);
            
            Assert.IsType<CreatedAtActionResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var setting = _fixture.Create<SettingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "Code cannot be empty";
            }

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<SettingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));
           
            //Act
            var result = await _controller.Create(setting);

            //Asert 
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);
            
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var setting = _fixture.Create<SettingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "Code cannot be empty";
            }

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<SettingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));
           
            //Act
            var result = await _controller.Update(setting);

            //Asert 
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default), Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);
            
        }
    
        [Fact]
        public async Task Update_ReturnsOkResult_WhenUpdateIsSuccessful()
        {
            // Arrange
            var setting = _fixture.Create<SettingDTO>();
         
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default)).ReturnsAsync(new ValidationResult());
            _mockService.Setup(s => s.Update(setting)).ReturnsAsync(setting);
           
            // Act
            var result = await _controller.Update(setting);

            // Assert
            _mockValidator.Verify(v => v.ValidateAsync(It.IsAny<SettingDTO>(), default), Times.Once);
            _mockService.Verify(service => service.Update(setting), Times.Once);

            Assert.IsType<OkObjectResult>(result);
        }



        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero()
        {
            // Arrange
            int tempSettingId = 0;

            // Act
            var result = await _controller.Delete(tempSettingId);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOk_WhenDeleteIsSuccessful()
        {
            // Arrange
            var setting = _fixture.Create<SettingDTO>();
            _mockService.Setup(s => s.Delete(setting.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(setting.Id);

            // Assert
            _mockService.Verify(service => service.Delete(setting.Id), Times.Once);        
            Assert.IsType<OkResult>(result);

        }
        
    }
}
