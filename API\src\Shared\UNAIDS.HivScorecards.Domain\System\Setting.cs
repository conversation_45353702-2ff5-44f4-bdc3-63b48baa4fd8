﻿namespace UNAIDS.HivScorecards.Domain.System
{
    public class Setting: BaseEntity
    {
        public int ModuleId { get; set; }
        public virtual Module? Module { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Criteria { get; set; } = string.Empty;
        public int DataTypeId { get; set; }
        public virtual LookUpInfo? DataType { get; set; }
        public string Value { get; set; } = string.Empty;
        public string Group { get; set; } = string.Empty;
        public int? SchemaDefinitionId { get; set; }
        //public virtual SchemaDefinition? SchemaDefinition { get; set; }
        public string? Remarks { get; set; }
        public string? BusinessRules { get; set; }

    }
}
