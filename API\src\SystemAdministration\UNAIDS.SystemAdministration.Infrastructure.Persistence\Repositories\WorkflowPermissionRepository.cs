using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class WorkflowPermissionRepository(SystemAdminDbContext dbContext) : BaseRepository<WorkflowPermission>(dbContext), IWorkflowPermissionRepository
    {
        public Task<JsonDocument?> GetPermissions(int permissionId)
        {
            throw new NotImplementedException();
        }

        public async Task<List<CommonLookUp>> GetUserRoles()
        {
            return await dbContext.Roles.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public Task<List<CommonLookUp>> GetWorkflowSteps(int wfPermissionId)
        {
            throw new NotImplementedException();
        }

        public Task UpdateTransitions(int id, JsonDocument transitions)
        {
            throw new NotImplementedException();
        }
    }
}
