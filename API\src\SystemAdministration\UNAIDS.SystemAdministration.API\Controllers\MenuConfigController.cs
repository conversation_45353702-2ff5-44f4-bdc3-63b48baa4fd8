using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/menu-config")]
    [ApiController]
    public class MenuConfigController(IMenuConfigService<MenuConfigDTO> menuConfigService, IValidator<MenuConfigDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Get all menuconfig
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUCONFIG}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var menuconfig = await menuConfigService.GetMenuHierarchy();
            return Ok(new
            {
                Record = menuconfig,
                ParentMenuId = await menuConfigService.GetMenuConfig(),
                ModuleId = await menuConfigService.GetModule(),
                ReplicationTypeId = await menuConfigService.GetLookUp("ReplicationTypeId"),
                MenuType = await menuConfigService.GetLookUp("ScreenType"),
                SourceModuleId = await menuConfigService.GetModule(),
                ObjectType = await menuConfigService.GetLookUp("ObjectTypes"),
                UserRigts = menuConfigService.AccessRights(SubjectTypes.MENUCONFIG)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get a specific menuconfig by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUCONFIG}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            var data = await menuConfigService.GetById(id);

            if(id == 0)
                return BadRequest();

            return Ok(new
            {
                Record = data,
                ParentMenuId = await menuConfigService.GetMenuConfigDetails(),
                ModuleId = await menuConfigService.GetModule(),
                ReplicationTypeId = await menuConfigService.GetLookUp("ReplicationTypeId"),
                MenuType = await menuConfigService.GetLookUp("ScreenType"),
                SourceModuleId = await menuConfigService.GetModule(),
                ObjectType = await menuConfigService.GetObjectTypes("ObjectTypes"),
                UserRights = menuConfigService.AccessRights(SubjectTypes.MENUCONFIG)
            });
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new menuconfig
        /// </summary>
        /// <param name="menuconfig"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.MENUCONFIG}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] MenuConfigDTO menuconfig)
        {
            if (menuconfig == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(menuconfig);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await menuConfigService.Create(menuconfig);
            return CreatedAtAction(nameof(Create), new { id = menuconfig.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a menuconfig
        /// </summary>
        /// <param name="menuconfig"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.MENUCONFIG}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] MenuConfigDTO menuconfig)
        {

            var validationResult = await validator.ValidateAsync(menuconfig);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await menuConfigService.Update(menuconfig);
            return Ok(menuconfig);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a menuconfig
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.MENUCONFIG}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await menuConfigService.Delete(id);
            return Ok();
        }
        #endregion
    }
}