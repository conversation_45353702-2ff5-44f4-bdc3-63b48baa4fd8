﻿using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Core.Workflow
{
    public class MasterWorkflowTransition(
        IServiceProvider serviceProvider,
        IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IWorkflowUtils utils,
        IMapper mapper
        ) : BaseWorkflowTransition(unitOfWork, serviceProvider), IWorkflowTransition
    {
        public async Task<WorkflowExecutorResult> Execute(WorkflowInstanceDTO workflowInstance, int nextStepId, int userId, Variables variables)
        {
            // Build workflow permissions of a user
            var workflow = await utils.BuildWorkflowSteps(workflowInstance.WorkflowDefinitionId, userId, true);

            // Creates execution context
            var workflowExecutionContext = new WorkflowExecutionContext(serviceProvider, workflowInstance, workflow, variables);

            // Validate
            var executionResult = await Validate(workflowExecutionContext, nextStepId, userId);

            // Check if any error exists
            if (executionResult.Errors.Count > 0)
            {
                // Set current workflow instance
                executionResult.WorkflowInstance = workflowInstance;

                return executionResult;
            }

            string activity = await DeriveActivityName(workflow, workflowInstance.CurrentStepId ?? 0, nextStepId);

            IWorkflowActivity activityInstance = await GetActivity(workflow, workflowInstance.CurrentStepId ?? 0, nextStepId, activity);

            // clone the current instance object before update..
            var objInstance = mapper.Map<WorkflowInstance>(workflowInstance);
            var currentInstance = objInstance.CloneObj<WorkflowInstance>();

            // Change CorrelationId on each transition
            objInstance.CorrelationId = Guid.NewGuid();
            // Set previos step
            objInstance.PreviousStepId = workflowInstance.CurrentStepId;
            objInstance.IsInitialStep = Convert.ToInt16(workflowInstance.CurrentStepId == null);
            objInstance.CurrentStepId = nextStepId;
            objInstance.IsReturned = 0;
            objInstance.UpdatedOn = DateTime.UtcNow;
            objInstance.ExecutionStatus = Convert.ToInt16(WorkflowStatus.Runnable);

            // Trigger before activity
            if (activityInstance != null)
            {
                var activityBeforeExecutionContext = new ActivityBeforeExecutionContext(serviceProvider, workflowInstance, workflow, variables);
                var beforeExecutionResult = await activityInstance.BeforeExecuteAsync(activityBeforeExecutionContext);

                // Adds activity execution error
                executionResult.Errors.AddRange(beforeExecutionResult.Errors);

                // Cancel executor if user sets terminate transition from before execution activity
                bool isTerminated = activityBeforeExecutionContext.WorkflowInstance.ExecutionStatus == Convert.ToInt16(WorkflowStatus.Terminated);

                if (isTerminated || beforeExecutionResult.Errors.Count > 0)
                {
                    executionResult.WorkflowInstance = mapper.Map<WorkflowInstanceDTO>(currentInstance);
                    return executionResult;
                }
            }

            var repo = unitOfWork.Repository<IWorkflowRepository>();
            objInstance.CurrentStatus = await repo.GetStepCode(nextStepId);

            if (objInstance.IsInitialStep == 1)
                await repo.AddAsync(objInstance);
            else
                await repo.UpdateAsync(objInstance);

            // Trigger after activity
            if (activityInstance != null)
            {
                var activityAfterExecutionContext = new ActivityAfterExecutionContext(serviceProvider, workflowInstance, workflow, variables);
                var afterExecutionResult = await activityInstance.AfterExecuteAsync(activityAfterExecutionContext);
            }

            objInstance.ExecutionStatus = Convert.ToInt16(WorkflowStatus.Complete);

            // Prepare transition history details
            var workflowHistory = new WorkflowHistory()
            {
                WorkflowHistoryId = Guid.NewGuid(),
                WorkflowInstanceId = objInstance.WorkflowInstanceId,
                FromStepId = objInstance.PreviousStepId,
                ToStepId = objInstance.CurrentStepId,
                Activity = activity,
                Comments = (string?)variables.Get("comment"),
                ExecutionStatus = objInstance.ExecutionStatus,
                CreatedById = objInstance.CreatedById,
                CreatedDate = DateTime.UtcNow,
                JsonData = objInstance.WorkflowData
            };

            await unitOfWork.Repository<IWorkflowHistoryRepository>().AddAsync(workflowHistory);

            await unitOfWork.SaveChangesAsync();

            executionResult.WorkflowInstance = mapper.Map<WorkflowInstanceDTO>(objInstance);
            return executionResult;
        }

    }
}
