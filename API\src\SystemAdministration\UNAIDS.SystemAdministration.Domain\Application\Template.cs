﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace UNAIDS.SystemAdministration.Domain
{
    [Table(name: "templates")]
    public class Template
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int TemplatesId { get; set; }

        [MaxLength(100)]
        [Display(Name = "Name")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Dash board")]
        public string DashboardJson { get; set; } = string.Empty;

        [Display(Name = "Active")]
        public short? Active { get; set; }

        public Guid? TenantId { get; set; }
    }
}
