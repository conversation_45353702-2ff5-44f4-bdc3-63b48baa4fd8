namespace UNAIDS.SystemAdministration.Application
{
    public class FormHeaderDTO
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int SchemaId { get; set; }
        public int Version { get; set; }
        public string? WorkflowInstanceId { get; set;}
        public int WorkflowStepId { get; set; }
        public int TenantId { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
