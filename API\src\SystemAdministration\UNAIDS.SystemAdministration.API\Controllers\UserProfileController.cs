using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-profile")]
    [ApiController]
    public class UserProfileController(IUserProfileService<UserProfileDTO> userProfileService, IValidator<ChangePasswordDetailsDTO> validator) : ControllerBase
    {
        #region GetUserDetail
        //  <summary>
        //  Get user detail
        //  </summary>
        //  <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserDetail()
        {
            var user = await userProfileService.GetUserDetail();

            return Ok(user);

        }
        #endregion

        #region ChangePassword...
        /// <summary>
        /// Method:ChangePassword
        /// Description:To change password by user itself...
        /// </summary>
        [HttpPut("change-password")]
        public async Task<IActionResult> ChangePassword([FromForm] ChangePasswordDetailsDTO changePasswordDetails)
        {
            // Validate the DTO
            var validationResult = await validator.ValidateAsync(changePasswordDetails);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await userProfileService.ChangePassword(changePasswordDetails);
            return Ok();
        }
        #endregion

    }
}