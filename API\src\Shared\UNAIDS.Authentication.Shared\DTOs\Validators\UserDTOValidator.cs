using FluentValidation;

namespace UNAIDS.Authentication.Shared
{
    public class UserDTOValidator : AbstractValidator<UserDTO>
    {
        public UserDTOValidator()
        {
            // UserName: Required and max length of 100
            RuleFor(x => x.UserName)
                .NotEmpty().WithMessage("The UserName is required.")
                .MaximumLength(100).WithMessage("The UserName must be at most 100 characters long.");

            // LoginName: Required and max length of 100
            RuleFor(x => x.LoginName)
                .NotEmpty().WithMessage("The LoginName is required.")
                .MaximumLength(100).WithMessage("The LoginName must be at most 100 characters long.");

            // Email: Required, valid email format, and max length of 100
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("The Email is required.")
                .EmailAddress().WithMessage("Enter a valid email address.")
                .MaximumLength(100).WithMessage("The Email must be at most 100 characters long.");

            // EffectiveFrom: Required and must be a past or present date
            RuleFor(x => x.EffectiveFrom)
                .NotEmpty().WithMessage("The EffectiveFrom date is required.")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("The EffectiveFrom date must be in the past or present.");

            // EffectiveTo: Must be a future date if provided
            RuleFor(x => x.EffectiveTo)
                .GreaterThan(x => x.EffectiveFrom).WithMessage("The EffectiveTo date must be later than the EffectiveFrom date.")
                .When(x => x.EffectiveTo.HasValue);

        }
    }
}
