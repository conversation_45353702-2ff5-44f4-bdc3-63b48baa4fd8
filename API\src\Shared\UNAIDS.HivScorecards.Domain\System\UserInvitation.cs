﻿
namespace UNAIDS.HivScorecards.Domain.System
{
    public class UserInvitation: BaseEntity
    {
        public int UserId { get; set; }
        public User? User { get; set; }
        public string? Email { get; set; } 
        public string InvitationURL { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public DateTime ExpireOn { get; set; }
        public int StatusId { get; set; }
    }
}
