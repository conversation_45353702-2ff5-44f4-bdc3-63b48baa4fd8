using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserGroupsService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
                                     IMapper mapper,
                                     ISequenceRepository sequenceRepository,
        IUserContext userContext) : 
        BaseAPIService<UserGroups, SystemAdminDbContext, UserGroupsDTO, IUserGroupsRepository>(unitOfWork, mapper, sequenceRepository, userContext), 
        IUserGroupsService<UserGroupsDTO>
    {
        // Add any other methods 
    }
}
