﻿using AutoFixture;
using FluentValidation;
using Moq;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Application;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using FluentValidation.Results;
using UNAIDS.Authorization.Shared;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class RoleMenuMappingControllerTests
    {
        private readonly Mock<IRoleMenuMappingService<RoleMenuMappingDTO>> _mockService;
        private readonly Mock<IValidator<RoleMenuMappingDTO>> _mockValidator;
        private readonly RoleMenuMappingController _controller;
        private readonly Fixture _fixture;

        public RoleMenuMappingControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<IRoleMenuMappingService<RoleMenuMappingDTO>>();
            _mockValidator = new Mock<IValidator<RoleMenuMappingDTO>>();
            _controller = new RoleMenuMappingController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_WithValidFilter_ReturnsRoleMenuMapping()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedData = _fixture.CreateMany<RoleMenuMappingDTO>(5).ToList();
            var expectedMenuType = _fixture.Create<List<CommonLookUp>>();
            var expectedMenuConfig = _fixture.Create<List<CommonLookUp>>();
            var expectedUserRights = "R";

            _mockService.Setup(s => s.GetRoleMenus(filter.Id)).ReturnsAsync(expectedData);
            _mockService.Setup(s => s.GetMenuConfig()).ReturnsAsync(expectedMenuConfig);
            _mockService.Setup(s => s.GetLookUp("ScreenType", false)).ReturnsAsync(expectedMenuType);
            _mockService.Setup(s => s.AccessRights(SubjectTypes.ROLE)).Returns(expectedUserRights);

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            _mockService.Verify(service => service.GetRoleMenus(filter.Id), Times.Once);
            _mockService.Verify(service => service.GetMenuConfig(), Times.Once);
            _mockService.Verify(service => service.GetLookUp("ScreenType", false), Times.Once);
            _mockService.Verify(s => s.AccessRights(SubjectTypes.ROLE), Times.Once);


            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);            
            var menuType = returnValue.GetType().GetProperty("MenuType")?.GetValue(returnValue);
            var menuConfig = returnValue.GetType().GetProperty("ParentMenuId")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedData, recordValue); 
            Assert.Equal(expectedMenuType, menuType);
            Assert.Equal(expectedMenuConfig, menuConfig);
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {
            //Act
            var result = await _controller.Get(0);

            //Assert
            Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Get_ReturnsOKObjectResult_WhenIdIsValid()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMappingDTO>();

            _mockService.Setup(s => s.GetById(expectedData.Id)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.Get(expectedData.Id);

            //Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            dynamic returnValue = okResult.Value;
            var record = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            Assert.Equal(expectedData, record);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenRoleMenuDataIsNull()
        {
            //Act
            var result = await _controller.Create(null);

            //Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {

            // Arrange
            var expectedData = _fixture.Create<RoleMenuMappingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMappingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Create(expectedData);

            //Asert 
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenRoleMenyMappingIsValid()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMappingDTO>();

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMappingDTO>(), default)).ReturnsAsync(new ValidationResult());
            _mockService.Setup(service => service.Create(expectedData)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.Create(expectedData);

            //Assert
            Assert.IsType<CreatedAtActionResult>(result);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMappingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "MenuId";
                failure.ErrorMessage = "MenuId must be a positive integer.";
            }
            _mockService.Setup(service => service.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMappingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Update(expectedData);

            //Asert 
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenUpdateIsSuccessful()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMappingDTO>();
            _mockService.Setup(service => service.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMappingDTO>(), default)).ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update(expectedData);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task BulkUpdate_ReturnsOkResult_WhenRoleMenuMappingIsValid() {
            // Arrange
            var roleMenuMappings = _fixture.CreateMany<RoleMenuMappingDTO>(4).ToList();

            _mockService.Setup(s => s.BulkUpdate(roleMenuMappings)).ReturnsAsync(roleMenuMappings);

            // Act
            var result = await _controller.BulkUpdate(roleMenuMappings);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }
    

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsInvalid()
        {
            // Arrange
            int tempId = 0;

            // Act
            var result = await _controller.Delete(tempId);

            // Assert
            var okResult = Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Delete_ReturnsOk_WhenDeleteIsSuccessful()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            _mockService.Setup(s => s.Delete(expectedData.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(expectedData.Id);

            // Assert
            Assert.IsType<OkResult>(result);

        }

    }
}
