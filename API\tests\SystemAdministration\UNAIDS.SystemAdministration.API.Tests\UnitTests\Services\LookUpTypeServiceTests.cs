using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class LookUpTypeServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<ILookupTypeRepository> _mockLookupTypeRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly LookUpTypeService _lookUpTypeService;
        private readonly Fixture _fixture;

        public LookUpTypeServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockLookupTypeRepository = new Mock<ILookupTypeRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<ILookupTypeRepository>())
                           .Returns(_mockLookupTypeRepository.Object);

            
            _lookUpTypeService = new LookUpTypeService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetModules_ReturnsListOfModules()
        {
            // Arrange          
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockLookupTypeRepository.Setup(repo => repo.GetModules())
                                        .ReturnsAsync(expectedModules);

            // Act
            var result = await _lookUpTypeService.GetModules();

            // Assert
            Assert.Equal(expectedModules, result);
            _mockLookupTypeRepository.Verify(repo => repo.GetModules(), Times.Once); 
        }

        [Fact]
        public async Task GetModules_ReturnsListOfBusinessUnits()
        {
            // Arrange          
            var expectedBusinessUnit = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockLookupTypeRepository.Setup(repo => repo.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnit);

            // Act
            var result = await _lookUpTypeService.GetBusinessUnits();

            // Assert
            Assert.Equal(expectedBusinessUnit, result);
            _mockLookupTypeRepository.Verify(repo => repo.GetBusinessUnits(), Times.Once); 
        }     

    }
}
