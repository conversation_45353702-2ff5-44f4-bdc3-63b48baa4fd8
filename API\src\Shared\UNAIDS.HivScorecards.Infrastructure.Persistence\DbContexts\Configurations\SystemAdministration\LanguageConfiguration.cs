﻿

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class LanguageConfiguration : IEntityTypeConfiguration<Language>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<Language> builder)
        {
            builder.ToTable("languages");
            builder.<PERSON><PERSON>ey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.HasIndex(e => e.Code).IsUnique();
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(10);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(50);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new LanguageConfiguration());
        }
    }
}
