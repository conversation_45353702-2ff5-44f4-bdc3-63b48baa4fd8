using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/settings")]
    [ApiController]
    public class SettingController(ISettingService<SettingDTO> settingService, IValidator<SettingDTO> validator) : ControllerBase
    {
        /// <summary>
        /// Get all Settings
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.SETTING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var data = await settingService.GetAll(filter);
            return Ok(new
            {
                Record = data,
                BusinessUnitId = await settingService.GetBusinessUnits(),
                ModuleId = await settingService.GetModules(),
                DataTypeId = await settingService.GetDataTypes(),
                UserRigts = settingService.AccessRights(SubjectTypes.SETTING)
            });
        }

        /// <summary>
        /// Get a specific Setting by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.SETTING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var data = await settingService.GetById(id);
            return Ok(new
            {
                Record = data,
                BusinessUnitId = await settingService.GetBusinessUnits(),
                ModuleId = await settingService.GetModules(),
                DataTypeId = await settingService.GetDataTypes(),
                UserRights = settingService.AccessRights(SubjectTypes.SETTING)
            });
        }

        /// <summary>
        /// Create a new Setting
        /// </summary>
        /// <param name="setting"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.SETTING}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] SettingDTO setting)
        {
            if (setting == null)
                return BadRequest();

            // Set ModuleId to 1 if it's 0
            if (setting.ModuleId == 0)
            {
                setting.ModuleId = 1; 
            }

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(setting);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await settingService.Create(setting);
            return CreatedAtAction(nameof(Create), new { id = setting.Id }, result);
        }

        /// <summary>
        /// Update a Setting
        /// </summary>
        /// <param name="setting"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.SETTING}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] SettingDTO setting)
        {
            var validationResult = await validator.ValidateAsync(setting);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await settingService.Update(setting);
            return Ok(setting);
        }

        /// <summary>
        /// Delete a Setting
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [Authorize($"{SubjectTypes.SETTING}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await settingService.Delete(id);
            return Ok();
        }
    }
}
