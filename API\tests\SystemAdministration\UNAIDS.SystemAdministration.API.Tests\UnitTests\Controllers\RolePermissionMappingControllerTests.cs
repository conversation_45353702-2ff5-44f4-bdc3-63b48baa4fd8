using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class RolePermissionMappingControllerTests {
        private readonly Mock<IRolePermissionMappingService<RolePermissionMappingDTO>> _mockService;
        private readonly Mock<IValidator<RolePermissionMappingDTO>> _mockValidator;
        private readonly RolePermissionMappingController _controller;
        private readonly Fixture _fixture;

        public RolePermissionMappingControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IRolePermissionMappingService<RolePermissionMappingDTO>>();
            _mockValidator = new Mock<IValidator<RolePermissionMappingDTO>>();
            _controller = new RolePermissionMappingController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedReturnData = _fixture.CreateMany<RolePermissionMappingDTO>(3).ToList();
            var expectedUserRights = "AEB";
            
            
            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.ROLEPERMISSIONMAPPING));

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }


        [Fact]
        public async Task Get_ReturnsBadRequest_WithZeroId() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedReturnData = _fixture.Create<RolePermissionMappingDTO>();
            var expectedUserRights = "AEB";  
       
            _mockService.Setup(service => service.GetById(expectedReturnData.Id)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.ROLEPERMISSIONMAPPING));

                 
            // Act
            var result = await _controller.Get(expectedReturnData.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenRolePermissionMappingIsNull() {
            // Arrange
            RolePermissionMappingDTO? rolePermissionMapping = null;

            // Act
            var result = await _controller.Create(rolePermissionMapping);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<RolePermissionMappingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RolePermissionMappingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenRolePermissionMappingIsValid() {
            // Arrange
            var rolePermissionMapping = _fixture.Create<RolePermissionMappingDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(rolePermissionMapping, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(rolePermissionMapping)).ReturnsAsync(rolePermissionMapping);

            // Act
            var result = await _controller.Create(rolePermissionMapping);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(rolePermissionMapping, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<RolePermissionMappingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RolePermissionMappingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenRolePermissionMappingIsValid() {
            // Arrange
            var rolePermissionMapping = _fixture.Create<RolePermissionMappingDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(rolePermissionMapping, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(rolePermissionMapping)).ReturnsAsync(rolePermissionMapping);

            // Act
            var result = await _controller.Update(rolePermissionMapping);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var rolePermissionMapping = _fixture.Create<RolePermissionMappingDTO>();
           _mockService.Setup(s => s.Delete(rolePermissionMapping.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(rolePermissionMapping.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}
