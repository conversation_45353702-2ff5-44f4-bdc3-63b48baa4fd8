﻿using UNAIDS.HivScorecards.Domain.System;
using System.Text.Json;

namespace UNAIDS.HivScorecards.Domain
{
    public class WorkflowInstance
    {
        public Guid WorkflowInstanceId { get; set; }
        public int WorkflowDefinitionId { get; set; }
        public virtual WorkflowDefinition? WorkflowDefinition { get; set; }
        public Guid? CorrelationId { get; set; }
        public int? PreviousStepId { get; set; }
        public int? CurrentStepId { get; set; }
        public short? ExecutionStatus { get; set; } 
        public short? IsInitialStep { get; set;}
        public short? IsCompleted { get; set; }
        public short? IsReturned { get; set; }
        public int? RevisionNo { get; set; }
        public string? CurrentStatus { get; set; }
        public JsonDocument? WorkflowData { get; set; }
        public void Dispose() => WorkflowData?.Dispose();
        public int? CreatedById { get; set; }
        public DateTime? CreatedOn { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedOn { get; set; }

    }
}
