using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class RoleMenuMappingServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IRoleMenuMappingRepository> _mockRoleMenuMappingRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly RoleMenuMappingService _roleMenuMappingService;
        private readonly Fixture _fixture;

        public RoleMenuMappingServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockRoleMenuMappingRepository = new Mock<IRoleMenuMappingRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>())
                           .Returns(_mockRoleMenuMappingRepository.Object);

            
            _roleMenuMappingService = new RoleMenuMappingService(
                _mockUnitOfWork.Object,
                _mockMapper.Object,
                _mockSequenceRepository.Object,
                _mockUserContext.Object
            );

            _fixture = new Fixture();
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        }

        [Fact]
        public async Task Update_PerformsAddAsync_WithNewRoleMenuMapping()
        {
            // Arrange
            var roleMenuMappingDTO = _fixture.Build<RoleMenuMappingDTO>().With(r => r.Id, 0).Create();
            var roleMenuMapping = _fixture.Build<RoleMenuMapping>().With(r => r.Id, 0).Create();

            _mockMapper.Setup(m => m.Map<RoleMenuMapping>(roleMenuMappingDTO)).Returns(roleMenuMapping);
            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>().AddAsync(roleMenuMapping)).ReturnsAsync(roleMenuMapping);
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).ReturnsAsync(1);
            _mockMapper.Setup(m => m.Map<RoleMenuMappingDTO>(roleMenuMapping)).Returns(roleMenuMappingDTO);

            // Act
            var result = await _roleMenuMappingService.Update(roleMenuMappingDTO);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(roleMenuMappingDTO, result);

            _mockUnitOfWork.Verify(u => u.Repository<IRoleMenuMappingRepository>().AddAsync(roleMenuMapping), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<RoleMenuMapping>(roleMenuMappingDTO), Times.Once);
            _mockMapper.Verify(m => m.Map<RoleMenuMappingDTO>(roleMenuMapping), Times.Once);
        }

        [Fact]
        public async Task Update_PerformsUpdateAsync_WithExistingRoleMenuMappingg()
        {
            // Arrange
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            var roleMenuMappingDTO = _fixture.Build<RoleMenuMappingDTO>().With(r => r.Id, 1).Create();
            var roleMenuMapping = _fixture.Build<RoleMenuMapping>().With(r => r.Id, 1).Create();

            _mockMapper.Setup(m => m.Map<RoleMenuMapping>(roleMenuMappingDTO)).Returns(roleMenuMapping);
            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>().UpdateAsync(roleMenuMapping)).ReturnsAsync(roleMenuMapping);
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).ReturnsAsync(1);
            _mockMapper.Setup(m => m.Map<RoleMenuMappingDTO>(roleMenuMapping)).Returns(roleMenuMappingDTO);

            // Act
            var result = await _roleMenuMappingService.Update(roleMenuMappingDTO);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(roleMenuMappingDTO, result);

            _mockUnitOfWork.Verify(u => u.Repository<IRoleMenuMappingRepository>().UpdateAsync(roleMenuMapping), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<RoleMenuMapping>(roleMenuMappingDTO), Times.Once);
            _mockMapper.Verify(m => m.Map<RoleMenuMappingDTO>(roleMenuMapping), Times.Once);
        }

        [Fact]
        public async Task BulkUpdate_WithNoPermissions_ReturnsEmptyList()
        {
            // Arrange
            var permissions = new List<RoleMenuMappingDTO>();

            // Act
            var result = await _roleMenuMappingService.BulkUpdate(permissions);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            _mockUnitOfWork.Verify(u => u.Repository<IRoleMenuMappingRepository>().GetExistingPermissions(It.IsAny<int>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Never);
        }


        [Fact]
        public async Task BulkUpdate_NoExistingPermissions_AddsNewPermissions()
        {
            // Arrange
            var permissions = new List<RoleMenuMappingDTO>
            {
                new() { Id = 2, RoleMenuMasterId = 1, MenuId = 101 },
                new() { Id = 3, RoleMenuMasterId = 1, MenuId = 102 },
                new() { Id = 4, RoleMenuMasterId = 1, MenuId = 103 }
            };
            var existingPermissions = new List<RoleMenuMapping>
            {
                new() { Id = 1, RoleMenuMasterId = 1, MenuId = 101 }
            };

            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>().GetExistingPermissions(It.IsAny<int>()))
                        .ReturnsAsync(permissions); 

            _mockMapper.Setup(m => m.Map<RoleMenuMapping>(It.IsAny<RoleMenuMappingDTO>()))
                .Returns((RoleMenuMappingDTO dto) => new RoleMenuMapping
                {
                    RoleMenuMasterId = dto.RoleMenuMasterId ?? 0,
                    MenuId = dto.MenuId
                });

            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>().AddManyAsync(It.IsAny<List<RoleMenuMapping>>()));

            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMappingRepository>().UpdateAsync(It.IsAny<RoleMenuMapping>()));

            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).ReturnsAsync(1);

            _mockMapper.Setup(m => m.Map<RoleMenuMappingDTO>(It.IsAny<RoleMenuMapping>()))
                .Returns((RoleMenuMapping entity) => new RoleMenuMappingDTO
                {
                    RoleMenuMasterId = entity.RoleMenuMasterId,
                    MenuId = entity.MenuId
                });

            // Act
            var result = await _roleMenuMappingService.BulkUpdate(permissions);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(permissions.Count, result.Count);
        }

        [Fact]
        public async Task GetRoleMenus_ReturnsListOfRoleMenus()
        {
            // Arrange   
            var rolemenuId = _fixture.Create<int>();
            var expectedRoleMenus = _fixture.CreateMany<RoleMenuMappingDTO>(1).ToList();
           
            _mockUnitOfWork.Setup(uow => uow.Repository<IRoleMenuMappingRepository>().GetRoleMenus(rolemenuId)).ReturnsAsync(expectedRoleMenus);

            // Act
            var result = await _roleMenuMappingService.GetRoleMenus(rolemenuId);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IRoleMenuMappingRepository>().GetRoleMenus(rolemenuId), Times.Once);

            Assert.Equal(expectedRoleMenus, result);
        }
        
        [Fact]
        public async Task GetMenuConfig_ReturnsListOfMenuConfigs()
        {
            // Arrange   
            var expectedMenuConfigs = _fixture.CreateMany<CommonLookUp>(1).ToList();
           
            _mockUnitOfWork.Setup(uow => uow.Repository<IRoleMenuMappingRepository>().GetMenuConfig()).ReturnsAsync(expectedMenuConfigs);

            // Act
            var result = await _roleMenuMappingService.GetMenuConfig();

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IRoleMenuMappingRepository>().GetMenuConfig(), Times.Once);

            Assert.Equal(expectedMenuConfigs, result);
        }
        
        [Fact]
        public async Task GetLookUp_ReturnsListOfLookUps()
        {
            // Arrange   
            var filedName = _fixture.Create<string>();
            bool flag = false;
            var expectedLookups = _fixture.CreateMany<CommonLookUp>(1).ToList();
           
            _mockUnitOfWork.Setup(uow => uow.Repository<IRoleMenuMappingRepository>().GetLookUp(filedName, flag)).ReturnsAsync(expectedLookups);

            // Act
            var result = await _roleMenuMappingService.GetLookUp(filedName, flag);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IRoleMenuMappingRepository>().GetLookUp(filedName, flag), Times.Once);

            Assert.Equal(expectedLookups, result);
        }

        [Fact]
        public async Task SavePermission_ReturnsEmptyList()
        {
            // Act
            var result = await _roleMenuMappingService.SavePermission();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

    }
}
