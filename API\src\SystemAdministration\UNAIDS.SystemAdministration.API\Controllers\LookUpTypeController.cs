﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/lookup-types")]
    [ApiController]
    public class LookUpTypeController(ILookUpTypeService<LookupTypeDTO> lookUpTypeService, IValidator<LookupTypeDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list lookuptype
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUPTYPE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var lookupTypes = await lookUpTypeService.GetAll(filter);
            return Ok(new
            {
                Record = lookupTypes,
                BusinessUnitId = await lookUpTypeService.GetBusinessUnits(),
                ModuleId = await lookUpTypeService.GetModules(),
                UserRigts =  lookUpTypeService.AccessRights(SubjectTypes.LOOKUPTYPE)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// get the lookup type by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUPTYPE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if(id == 0)
                return BadRequest();

            var lookuptype = await lookUpTypeService.GetById(id);
            return Ok(new {
                Record = lookuptype,
                BusinessUnitId = await lookUpTypeService.GetBusinessUnits(),
                ModuleId = await lookUpTypeService.GetModules(),
                UserRigts = lookUpTypeService.AccessRights(SubjectTypes.LOOKUPTYPE)
            });            
        }
        #endregion

        /// <summary>
        /// Create a new Setting
        /// </summary>
        /// <param name="lookuptype"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.LOOKUPTYPE}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] LookupTypeDTO lookuptype)
        {
            if (lookuptype == null)
                return BadRequest();

            var validationResult = await validator.ValidateAsync(lookuptype);
            if (!validationResult.IsValid)
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));

            var result = await lookUpTypeService.Create(lookuptype);
            return CreatedAtAction(nameof(Create), new { id = lookuptype.Id }, result);
        }

        /// <summary>
        /// Update a lookup type
        /// </summary>
        /// <param name="lookuptype"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.LOOKUPTYPE}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] LookupTypeDTO lookuptype)
        {
            var validationResult = await validator.ValidateAsync(lookuptype);
            if (!validationResult.IsValid)
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));

            await lookUpTypeService.Update(lookuptype);
            return Ok(lookuptype);
        }

        /// <summary>
        /// delete lookuptype
        /// </summary>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.LOOKUPTYPE}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if(id==0)
                return BadRequest();

            await lookUpTypeService.Delete(id);
            return Ok();
        }

    }
}
