namespace UNAIDS.HivScorecards.Domain.System
{
    public class BusinessUnit : BaseEntity
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int OBSSettingId { get; set; }
        public virtual OBSSetting? OBSSetting { get; set; }
        public string? Address { get; set; }
        public string? PinCode { get; set; }
        public string? Phone { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? WebSite { get; set; }
        public int? ParentBusinessUnitId { get; set; }
        public int? AccOBSBunitId { get; set; }
        public int? Tenant { get; set; }
        public short Active { get; set; }
        public string? Remarks { get; set; }
        public int Level { get; set; }
        public string? Hierarchy { get; set; }
        public string? GeoCoordinates { get; set; }
        public string? SettingDetail { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Logo { get; set; }
    }
}
