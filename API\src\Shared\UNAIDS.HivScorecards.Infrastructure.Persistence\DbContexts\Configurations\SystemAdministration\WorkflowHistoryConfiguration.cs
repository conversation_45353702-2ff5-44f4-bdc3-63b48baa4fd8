﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class WorkflowHistoryConfiguration : IEntityTypeConfiguration<WorkflowHistory>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowHistory> builder)
        {
            builder.ToTable("workflow_histories");
            builder.HasKey(e => e.WorkflowHistoryId);
            builder.Property(e => e.WorkflowHistoryId).ValueGeneratedNever();
            builder.Property(e => e.Activity).IsRequired(false).HasMaxLength(200);
            builder.Property(e => e.IPAddress).IsRequired(false).HasMaxLength(100);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowHistoryConfiguration());
        }
    }
}
