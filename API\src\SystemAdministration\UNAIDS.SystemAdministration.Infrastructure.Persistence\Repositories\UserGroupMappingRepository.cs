using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class UserGroupMappingRepository(SystemAdminDbContext dbContext) : BaseRepository<UserGroupMapping>(dbContext), IUserGroupMappingRepository
    {
        public async Task<List<CommonLookUp>> GetUser()
        {
            return await dbContext.Users.AsNoTracking()
                .Select(user => new CommonLookUp
                {
                    DisplayText = user.UserName,
                    Value = user.Id,
                    Active = 1
                })
            .OrderBy(c => c.DisplayText)
            .ToListAsync();
        }
    }
}
