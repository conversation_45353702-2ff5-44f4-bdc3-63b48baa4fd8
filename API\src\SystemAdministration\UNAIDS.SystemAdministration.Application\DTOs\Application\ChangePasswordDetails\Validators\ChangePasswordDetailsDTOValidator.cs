using System.Text.RegularExpressions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Shared;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.Core.Base.Contracts; 

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class ChangePasswordDetailsDTOValidator : AbstractValidator<ChangePasswordDetailsDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        private readonly IUserContext _userContext;

        public ChangePasswordDetailsDTOValidator(SystemAdminDbContext dbContext, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;

            RuleFor(x => x.CurrentPassword)
                .NotEmpty().WithMessage("Current password is required.")
                .MustAsync(async (currentPassword, cancellation) =>
                    await IsCurrentPasswordValid(currentPassword))
                .WithMessage("Current password is incorrect.");

            RuleFor(x => x.NewPassword)
                .NotEmpty().WithMessage("New password is required.")
                .MustAsync(async (newPassword, cancellation) =>
                    await IsNewPasswordValid(newPassword))
                .WithMessage("New password cannot be the same as the current password.")
                .MinimumLength(8).WithMessage("Password must be at least 8 characters long.")
                .MaximumLength(20).WithMessage("Password must not exceed 20 characters.")
                .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+.])[A-Za-z\d!@#$%^&*()_+.]+$")
                .WithMessage("Password must contain at least one letter, one number, and one special character.")
                .Must(x => !x.Contains(" ")).WithMessage("Password cannot contain blank spaces.")
                .MustAsync(async (newPassword, cancellation) =>
                    await MeetsDistinctCharacterRequirement(newPassword))
                .WithMessage(x => GetDistinctCharacterMessage());

            RuleFor(x => x.ConfirmPassword)
                .NotEmpty().WithMessage("Confirm password is required.")
                .Equal(x => x.NewPassword).WithMessage("New password and confirm password must match.");
        }

        private async Task<bool> IsCurrentPasswordValid(string currentPassword)
        {
            var user = await _dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == _userContext.Id);
            return user != null && PasswordUtility.VerifyPassword(currentPassword, user.PasswordHash);
        }

        private async Task<bool> IsNewPasswordValid(string newPassword)
        {
            var user = await _dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == _userContext.Id);
            return user == null || !PasswordUtility.VerifyPassword(newPassword, user.PasswordHash);
        }

        private async Task<bool> MeetsDistinctCharacterRequirement(string newPassword)
        {
            var settings = await _dbContext.Settings.AsNoTracking()
                .Where(s => s.Code == AppConstants.DistinctPasswordCharacterLength)
                .ToDictionaryAsync(s => s.Code, s => s.Value);

            int distinctCharLength = Convert.ToInt32(settings.GetValueOrDefault(AppConstants.DistinctPasswordCharacterLength, "0"));

            return distinctCharLength == 0 || newPassword.Distinct().Count() >= distinctCharLength;
        }

        private string GetDistinctCharacterMessage()
        {
            return $"Password should contain at least {AppConstants.DistinctPasswordCharacterLength} distinct characters.";
        }
    }
}

