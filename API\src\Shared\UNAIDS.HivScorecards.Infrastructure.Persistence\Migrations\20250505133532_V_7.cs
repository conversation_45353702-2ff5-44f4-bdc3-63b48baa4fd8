﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class V_7 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_applications_call_for_grants_call_id",
                schema: "pan_gms",
                table: "applications");

            migrationBuilder.DropForeignKey(
                name: "fk_opportunities_call_for_grants_call_for_grants_id",
                schema: "pan_gms",
                table: "opportunities");

            migrationBuilder.DropColumn(
                name: "call_id",
                schema: "pan_gms",
                table: "closure");

            migrationBuilder.DropColumn(
                name: "call_id",
                schema: "pan_gms",
                table: "amendments");

            migrationBuilder.RenameColumn(
                name: "call_id",
                schema: "pan_gms",
                table: "performance_reports",
                newName: "model_id");

            migrationBuilder.RenameColumn(
                name: "call_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "model_id");

            migrationBuilder.RenameColumn(
                name: "call_for_grants_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "model_header_id");

            migrationBuilder.RenameIndex(
                name: "ix_opportunities_call_for_grants_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "ix_opportunities_model_header_id");

            migrationBuilder.RenameColumn(
                name: "call_id",
                schema: "pan_gms",
                table: "applications",
                newName: "model_id");

            migrationBuilder.RenameIndex(
                name: "ix_applications_call_id",
                schema: "pan_gms",
                table: "applications",
                newName: "ix_applications_model_id");

            migrationBuilder.AddColumn<int>(
                name: "model_id",
                schema: "pan_gms",
                table: "closure",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "model_id",
                schema: "pan_gms",
                table: "amendments",
                type: "integer",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "fk_applications_model_headers_model_id",
                schema: "pan_gms",
                table: "applications",
                column: "model_id",
                principalSchema: "pan_gms",
                principalTable: "model_headers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_opportunities_model_headers_model_header_id",
                schema: "pan_gms",
                table: "opportunities",
                column: "model_header_id",
                principalSchema: "pan_gms",
                principalTable: "model_headers",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_applications_model_headers_model_id",
                schema: "pan_gms",
                table: "applications");

            migrationBuilder.DropForeignKey(
                name: "fk_opportunities_model_headers_model_header_id",
                schema: "pan_gms",
                table: "opportunities");

            migrationBuilder.DropColumn(
                name: "model_id",
                schema: "pan_gms",
                table: "closure");

            migrationBuilder.DropColumn(
                name: "model_id",
                schema: "pan_gms",
                table: "amendments");

            migrationBuilder.RenameColumn(
                name: "model_id",
                schema: "pan_gms",
                table: "performance_reports",
                newName: "call_id");

            migrationBuilder.RenameColumn(
                name: "model_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "call_id");

            migrationBuilder.RenameColumn(
                name: "model_header_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "call_for_grants_id");

            migrationBuilder.RenameIndex(
                name: "ix_opportunities_model_header_id",
                schema: "pan_gms",
                table: "opportunities",
                newName: "ix_opportunities_call_for_grants_id");

            migrationBuilder.RenameColumn(
                name: "model_id",
                schema: "pan_gms",
                table: "applications",
                newName: "call_id");

            migrationBuilder.RenameIndex(
                name: "ix_applications_model_id",
                schema: "pan_gms",
                table: "applications",
                newName: "ix_applications_call_id");

            migrationBuilder.AddColumn<int>(
                name: "call_id",
                schema: "pan_gms",
                table: "closure",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "call_id",
                schema: "pan_gms",
                table: "amendments",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "fk_applications_call_for_grants_call_id",
                schema: "pan_gms",
                table: "applications",
                column: "call_id",
                principalSchema: "pan_gms",
                principalTable: "call_for_grants",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_opportunities_call_for_grants_call_for_grants_id",
                schema: "pan_gms",
                table: "opportunities",
                column: "call_for_grants_id",
                principalSchema: "pan_gms",
                principalTable: "call_for_grants",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
