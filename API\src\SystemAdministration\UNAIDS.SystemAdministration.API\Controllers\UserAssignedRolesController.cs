using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Authentication.Shared;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-assigned-roles")]
    [ApiController]
    public class UserAssignedRolesController(IUserAssignedRolesService<UserDTO> userAssignedRolesService) : ControllerBase
    {
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.VIEW}")]
        [HttpGet("get-user-assigned-roles")]
        public async Task<IActionResult> GetUserAssignedRoles([FromQuery] Filter filter)
        {
            var roles = await userAssignedRolesService.GetUserAssignedRoles(filter);
            return Ok(new
            {
                Record = roles,
                BusinessUnitId = await userAssignedRolesService.GetBusinessUnits(),
                LogedInUserNameId = await userAssignedRolesService.GetUserNames(),
                LoginNameId = await userAssignedRolesService.GetLoginNames(),
                Title = "",
                UserRights = userAssignedRolesService.AccessRights(SubjectTypes.USER)
            });
        }
    }
}


