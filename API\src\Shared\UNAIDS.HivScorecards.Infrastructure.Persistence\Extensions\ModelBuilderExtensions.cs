﻿using IdentityModel;
using Microsoft.EntityFrameworkCore;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public static class ModelBuilderExtensions
    {
        public static void AddShadowProperties(this ModelBuilder modelBuilder)
        {
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Add TenantId shadow property
                entityType.AddProperty("TenantId", typeof(int?));
                modelBuilder.Entity(entityType.ClrType).HasIndex("TenantId");

            }
        }
    }
}
