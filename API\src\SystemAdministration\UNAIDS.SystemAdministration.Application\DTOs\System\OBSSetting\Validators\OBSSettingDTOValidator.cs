using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class OBSSettingDTOValidator : AbstractValidator<OBSSettingDTO>
    {
        public OBSSettingDTOValidator()
        {
            // Validate Code: cannot be null or empty
            RuleFor(o => o.Code)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(20).WithMessage("{PropertyName} must not exceed 20 characters.");

            // Validate Name: cannot be null or empty
            RuleFor(o => o.Name)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(100).WithMessage("{PropertyName} must not exceed 100 characters.");

            // Validate OBSTypeId: must be greater than 0
            RuleFor(o => o.OBSTypeId)
                .GreaterThan(0).WithMessage("{PropertyName} must be greater than 0.");

            // Validate IsAccountingUnit: must be either 0 or 1
            RuleFor(o => o.IsAccountingUnit)
                .InclusiveBetween((short)0, (short)1).WithMessage("{PropertyName} must be either 0 or 1.");

            // Validate Level: must be greater than or equal to 0
            RuleFor(o => o.Level)
                .GreaterThanOrEqualTo(0).WithMessage("{PropertyName} must be greater than or equal to 0.");

            // Optionally, you can add more validations for other fields like Hierarchy, Active, etc.
        }
    }
}
