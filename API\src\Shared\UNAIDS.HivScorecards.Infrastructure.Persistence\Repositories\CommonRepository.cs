﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Base.DTOs;
using UNAIDS.HivScorecards.Domain;
using System.Text.Json;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class CommonRepository(CommonDbContext dbContext, ITenantProvider tenantProvider, IUserContext userContext) : ICommonRepository
    {
        /// <summary>
        /// Get the user-dropdown options based on the selected role
        /// </summary>
        /// <param name="roleCode"></param>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetUsers(string roleCode)
        {
            return await (from usr in dbContext.Users.AsNoTracking()
                          join perm in dbContext.UserPermissions.AsNoTracking()
                          on usr.Id equals perm.UserId
                          join role in dbContext.Roles.AsNoTracking()
                          on perm.RoleId equals role.Id
                          where role.Code == roleCode && perm.BusinessUnitId == tenantProvider.TenantId
                          select new CommonLookUp()
                          {
                              Value = usr.Id,
                              Active = 1,
                              DisplayText = usr.UserName
                          }).ToListAsync();
        }

        /// <summary>
        /// Get the list of tenants...
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetTenants()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Where(x => x.Id > 0 && userContext.Tenants.Contains(x.Id))
                .Select(x => new CommonLookUp() { 
                    Value = x.Id,
                    Active = 1,
                    DisplayText = x.Name
                })
                .ToListAsync();
        }

        public Task<List<CommonLookUp>> GetCalls()
        {
            throw new NotImplementedException();
        }

        public Task<List<FormTemplateDTO>?> GetModelFormTemplate(int modelId)
        {
            throw new NotImplementedException();
        }

        public Task<int?> GetStageWorkflow(int callId, string stageCode)
        {
            throw new NotImplementedException();
        }

        public Task<string?> GetStageDocFormat(int callId, string stageCode)
        {
            throw new NotImplementedException();
        }

        public Task<List<CommonLookUp>> GetDynamicModels(string entity)
        {
            throw new NotImplementedException();
        }
    }
}
