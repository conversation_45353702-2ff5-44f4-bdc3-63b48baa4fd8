﻿using System.Text.Json;

namespace UNAIDS.HivScorecards.Domain
{
    public class WorkflowHistory
    {
        public Guid WorkflowHistoryId { get; set; }
        public Guid WorkflowInstanceId { get; set; }
        public int? FromStepId { get; set; }
        public int? ToStepId { get; set; }
        public string? Activity { get; set; }
        public short? ExecutionStatus { get; set; }
        public string? IPAddress { get; set; }
        public string? Comments { get; set; }
        public JsonDocument? JsonData { get; set; }
        public void Dispose() => JsonData?.Dispose();
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
    }
}
