﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace UNAIDS.Authentication.Shared
{
    public static class DependencyInjection
    {
        public static void AddOAuthProviderAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var provider = configuration.GetSection("OAuthProvider").Get<OAuthProviderSettings>();

            services.AddAuthentication()
                .AddJwtBearer(provider.Name, options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = provider.Issuer,
                        ValidAudience = provider.Audience,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(provider.SecretKey))
                    };
                });
        }

    }
}
