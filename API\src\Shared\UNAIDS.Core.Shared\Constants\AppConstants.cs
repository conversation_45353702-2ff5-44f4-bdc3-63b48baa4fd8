﻿
namespace UNAIDS.Core.Shared
{
    public class AppConstants
    {
        public const string DistinctPasswordCharacterLength = "DistinctPasswordCharacterLength";
        public const string PreviousPasswordCount = "PreviousPasswordCount";
        public const string MinimumPasswordLength = "MinimumPasswordLength";
        public const string MaximumPasswordLength = "MaximumPasswordLength";
        public enum BusinessUnitTypes
        {
            System = 0,
            Program = 2
        }

        public enum UserStatus
        {
            Invited = 10,
            Active = 11
        }

        public enum MenuType
        {
            Container = 1543,
            Master = 1540
        }

        public static string LKP_TYPE_OBSTYPE = "OBSType";

        public static string LKP_TYPE_GRANTSTAGE = "GrantStage";
    }
}
