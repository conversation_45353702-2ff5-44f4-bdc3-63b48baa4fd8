﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>cf8e3afb-0139-4690-b327-274431db6f76</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <PropertyGroup>
    <IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\OBSSettingController.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\UNAIDS.Authorization.Shared\UNAIDS.Authorization.Shared.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Application\UNAIDS.SystemAdministration.Application.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Infrastructure.Persistence\UNAIDS.SystemAdministration.Infrastructure.Persistence.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Infrastructure\UNAIDS.SystemAdministration.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>
	
  <Target Name="DeletePDBFiles" AfterTargets="Publish">
    <ItemGroup>
      <FilesToDelete Include="$(PublishDir)*.pdb" />
    </ItemGroup>
    <Delete Files="@(FilesToDelete)">
      <Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
    </Delete>
    <Message Text="Deleted PDB files: @(FilesDeleted)" Importance="high" />
  </Target>
  
  <Target Name="DeleteAppSettingsFiles" AfterTargets="Publish">
    <ItemGroup>
      <FilesToDelete Include="$(PublishDir)appsettings*.json" Exclude="$(PublishDir)appsettings.json;$(PublishDir)appsettings.$(Mode).json" />
    </ItemGroup>
    <Delete Files="@(FilesToDelete)">
      <Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
    </Delete>
    <Message Text="Deleted appsettings files: @(FilesDeleted)" Importance="high" />
  </Target>

</Project>
