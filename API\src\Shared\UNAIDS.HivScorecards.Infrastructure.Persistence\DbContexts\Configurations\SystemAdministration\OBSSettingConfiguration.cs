using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class OBSSettingConfiguration : IEntityTypeConfiguration<OBSSetting>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<OBSSetting> builder)
        {
            builder.ToTable("obs_settings");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.Code).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Name).IsRequired().HasMaxLength(200);

            builder.Property(e => e.IsAccountingUnit).IsRequired();
            builder.Property(e => e.IsExpenseTrackingUnit).IsRequired(false);
            builder.Property(e => e.Level).IsRequired();
            builder.Property(e => e.Hierarchy).IsRequired().HasMaxLength(200);
            builder.Property(e => e.Active).IsRequired();

            // OBSType relationship
            builder.Property(e => e.OBSTypeId).IsRequired();
            builder
                .HasOne(e => e.OBSType)
                .WithMany()
                .HasForeignKey(e => e.OBSTypeId)
                .HasPrincipalKey(e => e.Id);

            // ParentOBSType relationship
            builder.Property(e => e.ParentOBSTypeId).IsRequired(false);
            builder
                .HasOne(e => e.ParentOBSType)
                .WithMany()
                .HasForeignKey(e => e.ParentOBSTypeId)
                .HasPrincipalKey(e => e.Id);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new OBSSettingConfiguration());
        }
    }
}
