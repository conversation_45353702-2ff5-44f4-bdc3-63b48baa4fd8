using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class UserPermissionServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IUserPermissionRepository> _mockUserPermissionRepository;
        private Mock<IUserContext> _mockUserContext;
        private Mock<ICommonRepository> _mockCommonRepository;
        private readonly UserPermissionService _userPermissionService;
        private readonly Fixture _fixture;

        public UserPermissionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockUserPermissionRepository = new Mock<IUserPermissionRepository>();
            _mockCommonRepository = new Mock<ICommonRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IUserPermissionRepository>())
                           .Returns(_mockUserPermissionRepository.Object);

            
            _userPermissionService = new UserPermissionService(
                    _mockUnitOfWork.Object,
                    _mockMapper.Object,
                    _mockSequenceRepository.Object,
                    _mockUserContext.Object,
                    _mockCommonRepository.Object
                );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsUserPermissions()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();

            var userPermissions = _fixture.CreateMany<UserPermission>().ToList();
            var userPermissionDTO = _fixture.CreateMany<UserPermissionDTO>(userPermissions.Count).ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserPermissionRepository>().GetPermissions(filter.Id)).ReturnsAsync(userPermissions);

            _mockMapper.Setup(m => m.Map<List<UserPermissionDTO>>(userPermissions))
                    .Returns(userPermissionDTO);

            // Act
            var result = await _userPermissionService.GetAll(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userPermissionDTO, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserPermissionRepository>().GetPermissions(filter.Id), Times.Once);

            _mockMapper.Verify(m => m.Map<List<UserPermissionDTO>>(userPermissions), Times.Once);
        }
        
        [Fact]
        public async Task GetUser_ReturnsListOfUser()
        {
            // Arrange   
            var expectedUser = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockUserPermissionRepository.Setup(repo => repo.GetUser()).ReturnsAsync(expectedUser);

            // Act
            var result = await _userPermissionService.GetUser();

            // Assert
            Assert.Equal(expectedUser, result);

            _mockUserPermissionRepository.Verify(repo => repo.GetUser(), Times.Once); 
        }

        [Fact]
        public async Task GetRole_ReturnsListOfRole()
        {
            // Arrange   
            var expectedRole = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockUserPermissionRepository.Setup(repo => repo.GetRole()).ReturnsAsync(expectedRole);

            // Act
            var result = await _userPermissionService.GetRole();

            // Assert
            Assert.Equal(expectedRole, result);

            _mockUserPermissionRepository.Verify(repo => repo.GetRole(), Times.Once); 
        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfCommonLookUp()
        {
            // Arrange   
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockCommonRepository.Setup(repo => repo.GetTenants()).ReturnsAsync(expectedBusinessUnits);

            // Act
            var result = await _userPermissionService.GetBusinessUnits();

            // Assert
            Assert.Equal(expectedBusinessUnits, result);

            _mockCommonRepository.Verify(repo => repo.GetTenants(), Times.Once); 
        }

    }
}
