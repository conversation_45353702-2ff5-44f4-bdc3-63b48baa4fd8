﻿CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

CREATE TABLE "Concepts" (
    "Id" integer NOT NULL,
    "Name" character varying(50) NOT NULL,
    "Synonym" character varying(100),
    "Description" character varying(200),
    "SubjectToChange" smallint,
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_Concepts" PRIMARY KEY ("Id")
);

CREATE TABLE "ModuleGroups" (
    "Id" integer NOT NULL,
    "Code" character varying(10),
    "Name" character varying(100) NOT NULL,
    "Description" character varying(200),
    "SortOrder" integer,
    "Icon" character varying(50),
    "Color" character varying(50),
    "DashboardURL" character varying(2000),
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_ModuleGroups" PRIMARY KEY ("Id")
);

CREATE TABLE "Programs" (
    "Id" integer NOT NULL,
    "Code" character varying(10),
    "Name" character varying(100) NOT NULL,
    "Description" character varying(200),
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_Programs" PRIMARY KEY ("Id")
);

CREATE TABLE "UserInfo" (
    "Id" integer NOT NULL,
    "UserName" character varying(100) NOT NULL,
    "LoginName" character varying(100) NOT NULL,
    "EffectiveFrom" timestamp with time zone NOT NULL,
    "EffectiveTo" timestamp with time zone,
    "Email" character varying(100) NOT NULL,
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_UserInfo" PRIMARY KEY ("Id")
);

CREATE TABLE "Modules" (
    "Id" integer NOT NULL,
    "Code" character varying(10),
    "Name" character varying(50) NOT NULL,
    "Description" character varying(200),
    "ModuleGroupId" integer NOT NULL,
    "SortOrder" integer,
    "Icon" character varying(50),
    "Color" character varying(50),
    "DashboardURL" character varying(2000),
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_Modules" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Modules_ModuleGroups_ModuleGroupId" FOREIGN KEY ("ModuleGroupId") REFERENCES "ModuleGroups" ("Id") ON DELETE RESTRICT
);

CREATE TABLE "ProgramSetup" (
    "Id" integer NOT NULL,
    "ProgramId" integer NOT NULL,
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    "Features" jsonb,
    CONSTRAINT "PK_ProgramSetup" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ProgramSetup_Programs_ProgramId" FOREIGN KEY ("ProgramId") REFERENCES "Programs" ("Id") ON DELETE RESTRICT
);

CREATE TABLE "ProgramStage" (
    "Id" integer NOT NULL,
    "Name" character varying(500) NOT NULL,
    "ProgramId" integer NOT NULL,
    "SortOrder" integer,
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    "StageMenus" jsonb,
    CONSTRAINT "PK_ProgramStage" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ProgramStage_Programs_ProgramId" FOREIGN KEY ("ProgramId") REFERENCES "Programs" ("Id") ON DELETE RESTRICT
);

CREATE TABLE "MenuConfig" (
    "Id" integer NOT NULL,
    "ParentMenuId" integer,
    "Title" character varying(100) NOT NULL,
    "ComponentName" character varying(100),
    "ModuleId" integer,
    "MenuType" integer NOT NULL,
    "URL" character varying(2000),
    "SortOrder" integer,
    "Remarks" character varying(500),
    "IsHidden" smallint,
    "Icon" character varying(50),
    "Color" character varying(50),
    "Path" character varying(2000),
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_MenuConfig" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_MenuConfig_MenuConfig_ParentMenuId" FOREIGN KEY ("ParentMenuId") REFERENCES "MenuConfig" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_MenuConfig_Modules_ModuleId" FOREIGN KEY ("ModuleId") REFERENCES "Modules" ("Id") ON DELETE RESTRICT
);

CREATE TABLE "StandardSchemas" (
    "Id" integer NOT NULL,
    "MenuId" integer,
    "Schema" json,
    "CreatedById" integer,
    "CreatedAt" timestamp with time zone,
    "UpdatedById" integer,
    "UpdatedAt" timestamp with time zone,
    CONSTRAINT "PK_StandardSchemas" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_StandardSchemas_MenuConfig_MenuId" FOREIGN KEY ("MenuId") REFERENCES "MenuConfig" ("Id") ON DELETE RESTRICT
);

CREATE INDEX "IX_MenuConfig_ModuleId" ON "MenuConfig" ("ModuleId");

CREATE INDEX "IX_MenuConfig_ParentMenuId" ON "MenuConfig" ("ParentMenuId");

CREATE INDEX "IX_Modules_ModuleGroupId" ON "Modules" ("ModuleGroupId");

CREATE INDEX "IX_ProgramSetup_ProgramId" ON "ProgramSetup" ("ProgramId");

CREATE INDEX "IX_ProgramStage_ProgramId" ON "ProgramStage" ("ProgramId");

CREATE INDEX "IX_StandardSchemas_MenuId" ON "StandardSchemas" ("MenuId");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20240905061654_SA_1', '8.0.8');

COMMIT;

