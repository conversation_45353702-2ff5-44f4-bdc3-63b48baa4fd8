using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class WorkflowStepDTO : BaseEntity
    {
        public int WorkflowDefinitionId { get; set; }
        public int StepNo { get; set; }
        public string StepCode { get; set; } = string.Empty;
        public string StepName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string? Action { get; set; }
        public int SortOrder { get; set; }
        public short? IsForwardFlow { get; set; }
        public short? LockStatus { get; set; }
        public short? TriggerService { get; set; }
        public string WorkFlowService { get; set; } = string.Empty;
        public int? WorkFlowServiceId { get; set; }
        public short? Published { get; set; }
        public int? DueDate { get; set; }
    }
}