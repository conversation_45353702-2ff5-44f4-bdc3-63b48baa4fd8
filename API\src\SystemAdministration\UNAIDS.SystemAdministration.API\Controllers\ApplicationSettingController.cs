﻿using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/application-settings")]
    [ApiController]
    public class ApplicationSettingController(IApplicationSettingService service): ControllerBase
    {
        #region get template....
        /// <summary>
        /// Get the menu template
        /// </summary>
        /// <returns></returns>
        [HttpGet("templates/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTemplate(int id)
        {
            var data = await service.GetTemplate(id);

            return Ok(data);
        }
        #endregion

        #region get menu...
        /// <summary>
        /// Get the menus
        /// </summary>
        /// <returns></returns>
        [HttpGet("menus")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMenus()
        {
            var data = await service.GetMenu();

            return Ok(data);
        }
        #endregion

        #region user profile
        /// <summary>
        /// Get the menus
        /// </summary>
        /// <returns></returns>
        [HttpGet("user-profile")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserProfile()
        {
            var userProfile = await service.GetUserProfile();
            var bus = await service.GetBusinessUnits();

            return Ok(new { User = userProfile, BusinessUnits = bus, Date = DateTime.Now  });
        }
        #endregion
    }
}
