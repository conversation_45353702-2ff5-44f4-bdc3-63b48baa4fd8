using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface ILookUpInfoService
    {
        Task<Result<List<LookUpInfoDTO>>> GetAll();
        Task<Result<LookUpInfoDTO>> GetById(int id);
        Task<Result<LookUpInfoDTO>> Create(LookUpInfoDTO lookUpInfo);
        Task<Result<LookUpInfoDTO>> Update(int id, LookUpInfoDTO lookUpInfo);
        Task<Result> Delete(int id);
    }
}
