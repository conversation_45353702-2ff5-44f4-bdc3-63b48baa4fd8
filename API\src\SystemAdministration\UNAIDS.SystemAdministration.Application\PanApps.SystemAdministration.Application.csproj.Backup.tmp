﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\Repositories\IUnitOfWork.cs" />
    <Compile Remove="Contracts\Services\TenantManagement\ILookUpInfoService.cs" />
    <Compile Remove="Contracts\Services\TenantManagement\ILookupTypeService.cs" />
    <Compile Remove="Contracts\Services\TenantManagement\IOBSSettingService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\UNAIDS.Authentication.Shared\UNAIDS.Authentication.Shared.csproj" />
    <ProjectReference Include="..\..\Shared\UNAIDS.Core.Base\UNAIDS.Core.Base.csproj" />
    <ProjectReference Include="..\..\Shared\UNAIDS.HivScorecards.Domain\UNAIDS.HivScorecards.Domain.csproj" />
    <ProjectReference Include="..\..\Shared\UNAIDS.HivScorecards.Infrastructure.Persistence\UNAIDS.HivScorecards.Infrastructure.Persistence.csproj" />
  </ItemGroup>

</Project>
