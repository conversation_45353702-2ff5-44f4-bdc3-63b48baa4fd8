﻿namespace UNAIDS.SystemAdministration.Application
{
    public class TemplateResponseDto
    {
        public string[]? Component { get; set; }
        public string[]? Caption { get; set; }
        public string[]? Tooltip { get; set; }
        public int? ParentID { get; set; }
        public string? Template { get; set; }
        public int UserId { get; set; }
        public string[]? Tabular { get; set; }
        public string? SearchURL { get; set; }
        public string? WorkspaceConfig { get; set; }
        public bool Strapi { get; set; } = false;
        public string[]? Schema { get; set; }
        public string[]? DefaultSchema { get; set; }
        public int[]? MenuConfigId { get; set; }
        public string[]? ServiceParamJson { get; set; }
        public string[]? ServiceSchemaCode { get; set; }
        public string[]? MenuSchema { get; set; }
        public bool Child { get; set; } = false;

    }
}
