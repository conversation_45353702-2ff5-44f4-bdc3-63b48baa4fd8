using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Application
{
    public class LookupInfoDTOValidator : AbstractValidator<LookupInfoDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public LookupInfoDTOValidator(SystemAdminDbContext dbContext)
        {
           _dbContext = dbContext;
            // Validate Value: cannot be null or empty
            RuleFor(l => l.Name)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.");

            // Validate SortOrder: must be greater than or equal to 0
            RuleFor(l => l.SortOrder)
                .GreaterThanOrEqualTo(0).WithMessage("{PropertyName} must be greater than or equal to 0.");

            // Validate Code: must be unique for the combination of LookUpTypeId
            RuleFor(l => l.Code)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MustAsync(async (entity, value, c) => await UniqueLookUp(entity.Id, entity.LookUpTypeId, value))
                .WithMessage("The LookUp with this Code already exists.");

        }
        private async Task<bool> UniqueLookUp(int id, int lookUpTypeId, string code)
        {
            if (await _dbContext.LookUpInfo.AsNoTracking().AnyAsync(x =>
                x.Code.Trim().ToUpper() == code.Trim().ToUpper() &&
                x.LookUpTypeId == lookUpTypeId &&
                x.Id != id))
            {
                return false;
            }
            return true;
        }
    }
}
