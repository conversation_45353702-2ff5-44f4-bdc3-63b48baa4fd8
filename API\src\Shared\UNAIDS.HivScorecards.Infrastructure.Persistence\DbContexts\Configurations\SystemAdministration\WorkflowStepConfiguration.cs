﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain;
using System.Text.Json;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class WorkflowStepConfiguration : IEntityTypeConfiguration<WorkflowStep>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<WorkflowStep> builder)
        {
            builder.ToTable("workflow_steps");
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.Property(e => e.WorkflowDefinitionId).IsRequired();
            builder
                .HasOne(e => e.WorkflowDefinition)
                .WithMany()
                .HasForeignKey(e => e.WorkflowDefinitionId)
                .HasPrincipalKey(e => e.Id);

            builder.Property(e => e.StepNo).IsRequired();
            builder.Property(e => e.StepCode).IsRequired().HasMaxLength(30);
            builder.Property(e => e.StepName).IsRequired().HasMaxLength(100);
            builder.Property(e => e.DisplayName).IsRequired().HasMaxLength(200);
            builder.Property(e => e.Action).IsRequired().HasMaxLength(50);
            builder.Property(e => e.ReturnStatus).IsRequired(false).HasMaxLength(500);
            builder.Property(w => w.Properties).HasColumnType("jsonb");
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new WorkflowStepConfiguration());
        }
    }
}
