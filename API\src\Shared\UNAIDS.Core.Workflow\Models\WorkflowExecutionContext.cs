﻿
using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.Core.Workflow
{
    public class WorkflowExecutionContext
    {
        public IServiceProvider ServiceProvider { get; }
        public WorkflowInstanceDTO WorkflowInstance { get; }
        public WorkflowPermissionMapping Workflow { get; }
        public Variables Variables { get; }

        public WorkflowExecutionContext(
            IServiceProvider serviceProvider,
            WorkflowInstanceDTO workflowInstance,
            WorkflowPermissionMapping workflow,
            Variables variables
        )
        {
            ServiceProvider = serviceProvider;
            WorkflowInstance = workflowInstance;
            Workflow = workflow;
            Variables = variables;
        }

    }
}
