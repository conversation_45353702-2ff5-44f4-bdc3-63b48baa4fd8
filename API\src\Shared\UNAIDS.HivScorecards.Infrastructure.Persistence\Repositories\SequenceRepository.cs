﻿
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using System.Linq;
using System.Text.RegularExpressions;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public class SequenceRepository(CommonDbContext dbContext): ISequenceRepository
    {
        public async Task<int> GetSequenceNumber(string tableName)
        {
            Int32 nxtVal = 0;
            if (tableName != null)
            {
                nxtVal = await GetNextSequence(tableName);
            }
            return nxtVal;
        }

        public async Task<int> GetSequenceNumber(string tableName, int itemCnt)
        {
            Int32 nxtVal = 0;
            if (tableName != null)
            {
                nxtVal = await GetNextSequence(tableName, itemCnt);
            }
            return nxtVal;
        }

        public async Task<string> GetNextDocNumber(string baseEntity, string entity, int transactionId, string docFormat)
        {
            MatchCollection matches = Regex.Matches(docFormat, @"{{\w+}}");

            foreach(Match match in matches)
            {
                string strMatch = match.ToString();

                switch(strMatch)
                {
                    case "{{YY}}":
                        docFormat = docFormat.Replace(strMatch, DateTime.Now.ToString("yy"));
                        break;
                    case "{{YYYY}}":
                        docFormat = docFormat.Replace(strMatch, DateTime.Now.ToString("yyyy"));
                        break;
                    case "{{MTH}}":
                        docFormat = docFormat.Replace(strMatch, DateTime.Now.ToString("MM"));
                        break;
                    case "{{NN}}":
                    case "{{NNN}}":
                    case "{{NNNN}}":
                    case "{{NNNNN}}":
                    case "{{NNNNNN}}":
                        int cnt = strMatch.Replace("{{", "").Replace("}}", "").Length;
                        //var nextId = await GetNextDocNo(baseEntity, entity, transactionId);
                        //docFormat = docFormat.Replace(strMatch, nextId.ToString().PadLeft(cnt, '0'));
                        break;
                }
            }

            return docFormat;
        }

        private async Task<int> GetNextSequence(string EntityName)
        {
            int intCurrentNo = 0;
            int intNextNo = 0;

            new Domain.System.Sequence();
            Domain.System.Sequence? sequence = await dbContext.Sequences.Where((Domain.System.Sequence n) => n.TableName == EntityName).FirstOrDefaultAsync();
            
            if (sequence == null)
            {
                sequence = new Domain.System.Sequence
                {
                    TableName = EntityName,
                    MinimumValue = 1,
                    MaximumValue = int.MaxValue,
                    IncrementBy = 1,
                    NextValue = 1
                };
                dbContext.Sequences.Add(sequence);

                intCurrentNo = Convert.ToInt32(sequence.NextValue);
                intNextNo = (sequence.NextValue += sequence.IncrementBy);
                if (intNextNo < sequence.MinimumValue || intNextNo > sequence.MaximumValue)
                {
                    throw new Exception("Next value not between the Minimum and Maximum value");
                }
                await dbContext.SaveChangesAsync();
                return Convert.ToInt32(intCurrentNo);
            }

            intCurrentNo = Convert.ToInt32(sequence.NextValue);
            intNextNo = (sequence.NextValue += sequence.IncrementBy);
            if (intNextNo < sequence.MinimumValue || intNextNo > sequence.MaximumValue)
            {
                throw new Exception("Next value not between the Minimum and Maximum value");
            }

            dbContext.Entry(sequence).State = EntityState.Modified;
            await dbContext.SaveChangesAsync();
            return Convert.ToInt32(intCurrentNo);
        }

        private async Task<int> GetNextSequence(string EntityName, int itemCnt)
        {
            int newSeqId = 0;
            int intCurrentNo = 0;
            int intNextNo = 0;

            new Domain.System.Sequence();
            Domain.System.Sequence? sequence = await dbContext.Sequences.Where((Domain.System.Sequence n) => n.TableName == EntityName).FirstOrDefaultAsync();
            if (sequence == null)
            {
                sequence = new Domain.System.Sequence
                {
                    TableName = EntityName,
                    MinimumValue = 1,
                    MaximumValue = int.MaxValue,
                    IncrementBy = 1,
                    NextValue = 1
                };
                dbContext.Sequences.Add(sequence);
                intCurrentNo = Convert.ToInt32(sequence.NextValue);
                intNextNo = (sequence.NextValue += sequence.IncrementBy);
                if (intNextNo < sequence.MinimumValue || intNextNo > sequence.MaximumValue)
                {
                    throw new Exception("Next value not between the Minimum and Maximum value");
                }
                await dbContext.SaveChangesAsync();
                return Convert.ToInt32(intCurrentNo);
            }

            intCurrentNo = Convert.ToInt32(sequence.NextValue);
            intNextNo = (sequence.NextValue += sequence.IncrementBy * itemCnt);
            if (intNextNo < sequence.MinimumValue || intNextNo > sequence.MaximumValue)
            {
                throw new Exception("Next value not between the Minimum and Maximum value");
            }

            dbContext.Entry(sequence).State = EntityState.Modified;
            await dbContext.SaveChangesAsync();
            return Convert.ToInt32(intCurrentNo);
        }

    }
}
