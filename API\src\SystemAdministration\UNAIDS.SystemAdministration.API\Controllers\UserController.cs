using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Authentication.Shared;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using System.Threading.Tasks;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/users")]
    [ApiController]
    public class UserController(IUserService<UserDTO> userService, IValidator<UserDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list users
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var users = await userService.GetAll(filter);
            return Ok(new
            {
                Record = users,
                RoleId = await userService.GetRole(),
                BusinessUnitId = await userService.GetBusinessUnits(),
                UserRights = userService.AccessRights(SubjectTypes.USER)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get the user by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var user = await userService.GetById(id);
            return Ok(new
            {
                Record = user,
                RoleId = await userService.GetRole(),
                BusinessUnitId = await userService.GetBusinessUnits(),
                UserRights = userService.AccessRights(SubjectTypes.USER)
            });
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] UserDTO user)
        {
            if (user == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(user);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await userService.Create(user);
            return CreatedAtAction(nameof(Create), new { id = user.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a user
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update(int id, [FromBody] UserDTO user)
        {
            var validationResult = await validator.ValidateAsync(user);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }
            await userService.Update(user);
            return Ok(user);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a user by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await userService.Delete(id);
            return Ok();
        }
        #endregion

        #region List...
        /// <summary>
        /// Apply filter and list user
        /// </summary>
        /// <returns></returns> 
        [HttpGet("List")]
        [Authorize($"{SubjectTypes.USER}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> List([FromQuery] Filter filter)
        {
            var users = await userService.GetAll(filter);
            return Ok(new
            {
                Record = users,
                BusinessUnitId = await userService.GetBusinessUnits(),
                UserRights = "AED"
            });
        }
        #endregion

    }
}
