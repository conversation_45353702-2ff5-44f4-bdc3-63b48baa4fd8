﻿// <auto-generated />
using System;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

#nullable disable

namespace UNAIDS.HivScorecards.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(MigrationDbContext))]
    [Migration("20250505133532_V_7")]
    partial class V_7
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("pan_gms")
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UNAIDS.Model.Administration.RoleMenuMaster", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("business_unit_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("EqualRoleLevelFlag")
                        .HasColumnType("boolean")
                        .HasColumnName("equal_role_level_flag");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer")
                        .HasColumnName("module_id");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer")
                        .HasColumnName("role_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_role_menu_masters");

                    b.HasIndex("ModuleId")
                        .HasDatabaseName("ix_role_menu_masters_module_id");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_role_menu_masters_role_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_role_menu_masters_tenant_id");

                    b.ToTable("role_menu_masters", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.UserGroupMapping", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<short>("DefaultUserGroup")
                        .HasColumnType("smallint")
                        .HasColumnName("default_user_group");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("UserGroupsId")
                        .HasColumnType("integer")
                        .HasColumnName("user_groups_id");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_group_mapping");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_group_mapping_tenant_id");

                    b.HasIndex("UserGroupsId")
                        .HasDatabaseName("ix_user_group_mapping_user_groups_id");

                    b.ToTable("user_group_mapping", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.UserGroups", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("email");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_user_groups");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_groups_tenant_id");

                    b.ToTable("user_groups", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.CallForGrants", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("ActivityTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("activity_type_id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("start_date");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_call_for_grants");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_call_for_grants_tenant_id");

                    b.ToTable("call_for_grants", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.CallProgramStage", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<int>("CallId")
                        .HasColumnType("integer")
                        .HasColumnName("call_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DocumentNumberFormat")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("document_number_format");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_date");

                    b.Property<int?>("FormTemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("form_template_id");

                    b.Property<DateTime?>("FromDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("from_date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramStageId")
                        .HasColumnType("integer")
                        .HasColumnName("program_stage_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int?>("WorkflowId")
                        .HasColumnType("integer")
                        .HasColumnName("workflow_id");

                    b.HasKey("Id")
                        .HasName("pk_call_program_stages");

                    b.HasIndex("CallId")
                        .HasDatabaseName("ix_call_program_stages_call_id");

                    b.HasIndex("FormTemplateId")
                        .HasDatabaseName("ix_call_program_stages_form_template_id");

                    b.HasIndex("ProgramStageId")
                        .HasDatabaseName("ix_call_program_stages_program_stage_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_call_program_stages_tenant_id");

                    b.HasIndex("WorkflowId")
                        .HasDatabaseName("ix_call_program_stages_workflow_id");

                    b.ToTable("call_program_stages", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.DocumentSequenceInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BaseEntity")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("base_entity");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Entity")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("entity");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("NextSequenceNumber")
                        .HasColumnType("integer")
                        .HasColumnName("next_sequence_number");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TransactionId")
                        .HasColumnType("integer")
                        .HasColumnName("transaction_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_document_sequence_info");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_document_sequence_info_tenant_id");

                    b.ToTable("document_sequence_info", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.DocumentStore", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("business_unit_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer")
                        .HasColumnName("module_id");

                    b.Property<string>("ReferenceNo")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("reference_no");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("remarks");

                    b.Property<int?>("ServiceId")
                        .HasColumnType("integer")
                        .HasColumnName("service_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("TransactionId")
                        .HasColumnType("integer")
                        .HasColumnName("transaction_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_document_store");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_document_store_tenant_id");

                    b.ToTable("document_store", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.DocumentStoreDetails", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("DocumentStoreId")
                        .HasColumnType("integer")
                        .HasColumnName("document_store_id");

                    b.Property<string>("FileName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("file_name");

                    b.Property<int?>("FileSize")
                        .HasColumnType("integer")
                        .HasColumnName("file_size");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("remarks");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<DateTime?>("UploadedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("uploaded_date");

                    b.Property<string>("UploadedName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("uploaded_name");

                    b.Property<string>("UploadedPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("uploaded_path");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_document_store_details");

                    b.HasIndex("DocumentStoreId")
                        .HasDatabaseName("ix_document_store_details_document_store_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_document_store_details_tenant_id");

                    b.ToTable("document_store_details", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.Indicator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CollectionAgentName")
                        .HasColumnType("text")
                        .HasColumnName("collection_agent_name");

                    b.Property<int?>("CollectionAgentTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_agent_type_id");

                    b.Property<int?>("CollectionMethodId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_method_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("DataTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("data_type_id");

                    b.Property<int?>("FormatId")
                        .HasColumnType("integer")
                        .HasColumnName("format_id");

                    b.Property<int?>("FrequencyId")
                        .HasColumnType("integer")
                        .HasColumnName("frequency_id");

                    b.Property<int?>("IndicatorTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("indicator_type_id");

                    b.Property<short?>("IsCalculated")
                        .HasColumnType("smallint")
                        .HasColumnName("is_calculated");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("MeasurementTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("measurement_type_id");

                    b.Property<int?[]>("Options")
                        .HasColumnType("integer[]")
                        .HasColumnName("options");

                    b.Property<int?>("ParentIndicatorId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_indicator_id");

                    b.Property<int?>("ReportingTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("reporting_type_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<string>("VizTypes")
                        .HasColumnType("text")
                        .HasColumnName("viz_types");

                    b.HasKey("Id")
                        .HasName("pk_indicators");

                    b.HasIndex("CollectionAgentTypeId")
                        .HasDatabaseName("ix_indicators_collection_agent_type_id");

                    b.HasIndex("CollectionMethodId")
                        .HasDatabaseName("ix_indicators_collection_method_id");

                    b.HasIndex("DataTypeId")
                        .HasDatabaseName("ix_indicators_data_type_id");

                    b.HasIndex("FormatId")
                        .HasDatabaseName("ix_indicators_format_id");

                    b.HasIndex("IndicatorTypeId")
                        .HasDatabaseName("ix_indicators_indicator_type_id");

                    b.HasIndex("MeasurementTypeId")
                        .HasDatabaseName("ix_indicators_measurement_type_id");

                    b.HasIndex("ParentIndicatorId")
                        .HasDatabaseName("ix_indicators_parent_indicator_id");

                    b.HasIndex("ReportingTypeId")
                        .HasDatabaseName("ix_indicators_reporting_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_indicators_tenant_id");

                    b.ToTable("indicators", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.Model", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<string>("Slug")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("slug");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_models");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_models_tenant_id");

                    b.ToTable("models", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ModelDataElement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("DataElementId")
                        .HasColumnType("integer")
                        .HasColumnName("data_element_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short>("IsGlobal")
                        .HasColumnType("smallint")
                        .HasColumnName("is_global");

                    b.Property<int>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<int?>("ModelId1")
                        .HasColumnType("integer")
                        .HasColumnName("model_id1");

                    b.Property<int?>("NavigationModelId")
                        .HasColumnType("integer")
                        .HasColumnName("navigation_model_id");

                    b.Property<string>("Options")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("options");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_model_data_elements");

                    b.HasIndex("ModelId")
                        .HasDatabaseName("ix_model_data_elements_model_id");

                    b.HasIndex("ModelId1")
                        .HasDatabaseName("ix_model_data_elements_model_id1");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_model_data_elements_tenant_id");

                    b.ToTable("model_data_elements", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.Amendment", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("AMRDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("amr_date");

                    b.Property<string>("AMRNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("amr_number");

                    b.Property<int>("ApplicationId")
                        .HasColumnType("integer")
                        .HasColumnName("application_id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<decimal?>("RevisedCost")
                        .HasColumnType("numeric")
                        .HasColumnName("revised_cost");

                    b.Property<DateTime?>("RevisedEndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("revised_end_date");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_amendments");

                    b.HasIndex("ApplicationId")
                        .HasDatabaseName("ix_amendments_application_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_amendments_tenant_id");

                    b.ToTable("amendments", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.AmendmentData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_amendment_data");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_amendment_data_tenant_id");

                    b.ToTable("amendment_data", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.Closure", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("ACRDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("acr_date");

                    b.Property<int>("ApplicationId")
                        .HasColumnType("integer")
                        .HasColumnName("application_id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("MUNumber")
                        .HasColumnType("integer")
                        .HasColumnName("mu_number");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_closure");

                    b.HasIndex("ApplicationId")
                        .HasDatabaseName("ix_closure_application_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_closure_tenant_id");

                    b.ToTable("closure", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.ClosureData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_closure_data");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_closure_data_tenant_id");

                    b.ToTable("closure_data", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.PerformanceReport", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("ApplicationId")
                        .HasColumnType("integer")
                        .HasColumnName("application_id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<DateTime?>("PIRDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("pir_date");

                    b.Property<string>("PIRNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("pir_number");

                    b.Property<string>("PIRPeriod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("pir_period");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_performance_reports");

                    b.HasIndex("ApplicationId")
                        .HasDatabaseName("ix_performance_reports_application_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_performance_reports_tenant_id");

                    b.ToTable("performance_reports", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.PerformanceReportData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_performance_report_data");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_performance_report_data_tenant_id");

                    b.ToTable("performance_report_data", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("integer")
                        .HasColumnName("currency_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<int?>("OpportunityId")
                        .HasColumnType("integer")
                        .HasColumnName("opportunity_id");

                    b.Property<int?[]>("ReviewerIds")
                        .IsRequired()
                        .HasColumnType("integer[]")
                        .HasColumnName("reviewer_ids");

                    b.Property<int?>("TTLId")
                        .HasColumnType("integer")
                        .HasColumnName("ttl_id");

                    b.Property<int?[]>("TeamMemberIds")
                        .IsRequired()
                        .HasColumnType("integer[]")
                        .HasColumnName("team_member_ids");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_applications");

                    b.HasIndex("CurrencyId")
                        .HasDatabaseName("ix_applications_currency_id");

                    b.HasIndex("ModelId")
                        .HasDatabaseName("ix_applications_model_id");

                    b.HasIndex("OpportunityId")
                        .HasDatabaseName("ix_applications_opportunity_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_applications_tenant_id");

                    b.ToTable("applications", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.ApplicationData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_applicationdata");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_applicationdata_tenant_id");

                    b.ToTable("applicationdata", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Opportunity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ModelHeaderId")
                        .HasColumnType("integer")
                        .HasColumnName("model_header_id");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<int?>("TTLId")
                        .HasColumnType("integer")
                        .HasColumnName("ttl_id");

                    b.Property<int?[]>("TeamMemberIds")
                        .IsRequired()
                        .HasColumnType("integer[]")
                        .HasColumnName("team_member_ids");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_opportunities");

                    b.HasIndex("ModelHeaderId")
                        .HasDatabaseName("ix_opportunities_model_header_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_opportunities_tenant_id");

                    b.ToTable("opportunities", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.OpportunityData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_opportunitydata");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_opportunitydata_tenant_id");

                    b.ToTable("opportunitydata", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Review", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("integer")
                        .HasColumnName("application_id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ReviewerId")
                        .HasColumnType("integer")
                        .HasColumnName("reviewer_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_reviews");

                    b.HasIndex("ApplicationId")
                        .HasDatabaseName("ix_reviews_application_id");

                    b.HasIndex("ReviewerId")
                        .HasDatabaseName("ix_reviews_reviewer_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_reviews_tenant_id");

                    b.ToTable("reviews", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.ReviewData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Comments")
                        .HasColumnType("text")
                        .HasColumnName("comments");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("FormData")
                        .HasColumnType("jsonb")
                        .HasColumnName("form_data");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_review_data");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_review_data_tenant_id");

                    b.ToTable("review_data", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Category", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int?>("CategoryTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("category_type_id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("CustomSchemaId")
                        .HasColumnType("integer")
                        .HasColumnName("custom_schema_id");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_category_id");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_categories");

                    b.HasIndex("CategoryTypeId")
                        .HasDatabaseName("ix_categories_category_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_categories_tenant_id");

                    b.ToTable("categories", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.CategoryOption", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("category_id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentOptionId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_option_id");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_category_options");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_category_options_category_id");

                    b.HasIndex("ParentOptionId")
                        .HasDatabaseName("ix_category_options_parent_option_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_category_options_tenant_id");

                    b.ToTable("category_options", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("CustomSchemaId")
                        .HasColumnType("integer")
                        .HasColumnName("custom_schema_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<string>("Slug")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("slug");

                    b.Property<short?>("SubjectToChange")
                        .HasColumnType("smallint")
                        .HasColumnName("subject_to_change");

                    b.Property<string>("Synonym")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("synonym");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_concepts");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_concepts_tenant_id");

                    b.ToTable("concepts", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Country", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_countries");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_countries_tenant_id");

                    b.ToTable("countries", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.CountryDetail", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("CountryId")
                        .HasColumnType("integer")
                        .HasColumnName("country_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("FCSId")
                        .HasColumnType("integer")
                        .HasColumnName("fcs_id");

                    b.Property<int>("FiscalYearId")
                        .HasColumnType("integer")
                        .HasColumnName("fiscal_year_id");

                    b.Property<int?>("IncomeGroupId")
                        .HasColumnType("integer")
                        .HasColumnName("income_group_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("LendingCategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("lending_category_id");

                    b.Property<int?>("RegionId")
                        .HasColumnType("integer")
                        .HasColumnName("region_id");

                    b.Property<int?>("SIDSId")
                        .HasColumnType("integer")
                        .HasColumnName("sids_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_country_details");

                    b.HasIndex("CountryId")
                        .HasDatabaseName("ix_country_details_country_id");

                    b.HasIndex("FCSId")
                        .HasDatabaseName("ix_country_details_fcs_id");

                    b.HasIndex("FiscalYearId")
                        .HasDatabaseName("ix_country_details_fiscal_year_id");

                    b.HasIndex("IncomeGroupId")
                        .HasDatabaseName("ix_country_details_income_group_id");

                    b.HasIndex("LendingCategoryId")
                        .HasDatabaseName("ix_country_details_lending_category_id");

                    b.HasIndex("RegionId")
                        .HasDatabaseName("ix_country_details_region_id");

                    b.HasIndex("SIDSId")
                        .HasDatabaseName("ix_country_details_sids_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_country_details_tenant_id");

                    b.ToTable("country_details", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.DataElement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<int?>("ConceptId")
                        .HasColumnType("integer")
                        .HasColumnName("concept_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DataGroupName")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("data_group_name");

                    b.Property<int?>("DataTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("data_type_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<string>("Format")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("format");

                    b.Property<Guid>("InternalId")
                        .HasColumnType("uuid")
                        .HasColumnName("internal_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsRequired")
                        .HasColumnType("smallint")
                        .HasColumnName("is_required");

                    b.Property<int?>("MaxLength")
                        .HasColumnType("integer")
                        .HasColumnName("max_length");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<string>("Slug")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("slug");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_data_elements");

                    b.HasIndex("ConceptId")
                        .HasDatabaseName("ix_data_elements_concept_id");

                    b.HasIndex("DataTypeId")
                        .HasDatabaseName("ix_data_elements_data_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_data_elements_tenant_id");

                    b.ToTable("data_elements", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Events", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<int>("EventTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("event_type_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_events");

                    b.HasIndex("EventTypeId")
                        .HasDatabaseName("ix_events_event_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_events_tenant_id");

                    b.ToTable("events", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.FiscalYear", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_date");

                    b.Property<string>("FiscalYearCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("fiscal_year_code");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("start_date");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_fiscal_years");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_fiscal_years_tenant_id");

                    b.ToTable("fiscal_years", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Framework", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("FrameworkTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("framework_type_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_frameworks");

                    b.HasIndex("FrameworkTypeId")
                        .HasDatabaseName("ix_frameworks_framework_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_frameworks_tenant_id");

                    b.ToTable("frameworks", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.FrameworkData", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<JsonDocument>("Data")
                        .HasColumnType("jsonb")
                        .HasColumnName("data");

                    b.Property<int>("FrameworkId")
                        .HasColumnType("integer")
                        .HasColumnName("framework_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_framework_data");

                    b.HasIndex("FrameworkId")
                        .HasDatabaseName("ix_framework_data_framework_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_framework_data_tenant_id");

                    b.ToTable("framework_data", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.FrameworkHierarchy", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("ConceptId")
                        .HasColumnType("integer")
                        .HasColumnName("concept_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("FrameworkId")
                        .HasColumnType("integer")
                        .HasColumnName("framework_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsMeasurable")
                        .HasColumnType("smallint")
                        .HasColumnName("is_measurable");

                    b.Property<int?>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("level");

                    b.Property<int?>("ParentConceptId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_concept_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_framework_hierarchy");

                    b.HasIndex("ConceptId")
                        .HasDatabaseName("ix_framework_hierarchy_concept_id");

                    b.HasIndex("FrameworkId")
                        .HasDatabaseName("ix_framework_hierarchy_framework_id");

                    b.HasIndex("ParentConceptId")
                        .HasDatabaseName("ix_framework_hierarchy_parent_concept_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_framework_hierarchy_tenant_id");

                    b.ToTable("framework_hierarchy", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.GrantType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_grant_types");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_grant_types_tenant_id");

                    b.ToTable("grant_types", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.LocalDataElement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("Attributes")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<int?>("ConceptId")
                        .HasColumnType("integer")
                        .HasColumnName("concept_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DataGroupName")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("data_group_name");

                    b.Property<int?>("DataTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("data_type_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<string>("Format")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("format");

                    b.Property<Guid>("InternalId")
                        .HasColumnType("uuid")
                        .HasColumnName("internal_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsRequired")
                        .HasColumnType("smallint")
                        .HasColumnName("is_required");

                    b.Property<int?>("MaxLength")
                        .HasColumnType("integer")
                        .HasColumnName("max_length");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<int?>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<string>("Slug")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("slug");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_local_data_elements");

                    b.HasIndex("ConceptId")
                        .HasDatabaseName("ix_local_data_elements_concept_id");

                    b.HasIndex("DataTypeId")
                        .HasDatabaseName("ix_local_data_elements_data_type_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_local_data_elements_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_local_data_elements_tenant_id");

                    b.ToTable("local_data_elements", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<int?>("EntityId")
                        .HasColumnType("integer")
                        .HasColumnName("entity_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("SchemaId")
                        .HasColumnType("integer")
                        .HasColumnName("schema_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_model_headers");

                    b.HasIndex("EntityId")
                        .HasDatabaseName("ix_model_headers_entity_id");

                    b.HasIndex("SchemaId")
                        .HasDatabaseName("ix_model_headers_schema_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_model_headers_tenant_id");

                    b.ToTable("model_headers", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelSchema", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<JsonDocument>("BaseSchema")
                        .HasColumnType("json")
                        .HasColumnName("base_schema");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_model_schemas");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_model_schemas_tenant_id");

                    b.ToTable("model_schemas", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelTemplate", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Language")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("language");

                    b.Property<int>("ModelHeaderId")
                        .HasColumnType("integer")
                        .HasColumnName("model_header_id");

                    b.Property<JsonDocument>("Template")
                        .HasColumnType("json")
                        .HasColumnName("template");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_model_templates");

                    b.HasIndex("ModelHeaderId")
                        .HasDatabaseName("ix_model_templates_model_header_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_model_templates_tenant_id");

                    b.ToTable("model_templates", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelWorkflow", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ModelHeaderId")
                        .HasColumnType("integer")
                        .HasColumnName("model_header_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int?>("WorkflowId")
                        .HasColumnType("integer")
                        .HasColumnName("workflow_id");

                    b.Property<JsonDocument>("WorkflowPermissions")
                        .HasColumnType("json")
                        .HasColumnName("workflow_permissions");

                    b.HasKey("Id")
                        .HasName("pk_model_workflows");

                    b.HasIndex("ModelHeaderId")
                        .HasDatabaseName("ix_model_workflows_model_header_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_model_workflows_tenant_id");

                    b.HasIndex("WorkflowId")
                        .HasDatabaseName("ix_model_workflows_workflow_id");

                    b.ToTable("model_workflows", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramCategory", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("category_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_categories");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_program_categories_category_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_categories_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_categories_tenant_id");

                    b.ToTable("program_categories", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramCategoryOption", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("CategoryOptionId")
                        .HasColumnType("integer")
                        .HasColumnName("category_option_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_category_options");

                    b.HasIndex("CategoryOptionId")
                        .HasDatabaseName("ix_program_category_options_category_option_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_category_options_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_category_options_tenant_id");

                    b.ToTable("program_category_options", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramDataElement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("DataElementId")
                        .HasColumnType("integer")
                        .HasColumnName("data_element_id");

                    b.Property<string>("DataGroupName")
                        .HasColumnType("text")
                        .HasColumnName("data_group_name");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_data_elements");

                    b.HasIndex("DataElementId")
                        .HasDatabaseName("ix_program_data_elements_data_element_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_data_elements_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_data_elements_tenant_id");

                    b.ToTable("program_data_elements", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchy", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("category_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("ElementId")
                        .HasColumnType("integer")
                        .HasColumnName("element_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("level");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_hierarchy");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_program_hierarchy_category_id");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_program_hierarchy_parent_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_hierarchy_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_hierarchy_tenant_id");

                    b.ToTable("program_hierarchy", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyCategory", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("category_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text")
                        .HasColumnName("display_name");

                    b.Property<string>("Entity")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("entity");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_hierarchy_categories");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_hierarchy_categories_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_hierarchy_categories_tenant_id");

                    b.ToTable("program_hierarchy_categories", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyLevel", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("level");

                    b.Property<int?>("ParentLevelId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_level_id");

                    b.Property<int>("ProgramHierarchyElementId")
                        .HasColumnType("integer")
                        .HasColumnName("program_hierarchy_element_id");

                    b.Property<int?>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_hierarchy_levels");

                    b.HasIndex("ParentLevelId")
                        .HasDatabaseName("ix_program_hierarchy_levels_parent_level_id");

                    b.HasIndex("ProgramHierarchyElementId")
                        .HasDatabaseName("ix_program_hierarchy_levels_program_hierarchy_element_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_hierarchy_levels_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_hierarchy_levels_tenant_id");

                    b.ToTable("program_hierarchy_levels", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramConcept", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int>("ConceptId")
                        .HasColumnType("integer")
                        .HasColumnName("concept_id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_concepts");

                    b.HasIndex("ConceptId")
                        .HasDatabaseName("ix_program_concepts_concept_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_concepts_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_concepts_tenant_id");

                    b.ToTable("program_concepts", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramModel", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ModelId")
                        .HasColumnType("integer")
                        .HasColumnName("model_id");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_models");

                    b.HasIndex("ModelId")
                        .HasDatabaseName("ix_program_models_model_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_models_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_models_tenant_id");

                    b.ToTable("program_models", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PublishedModelTemplate", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Language")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("language");

                    b.Property<int>("ModelHeaderId")
                        .HasColumnType("integer")
                        .HasColumnName("model_header_id");

                    b.Property<JsonDocument>("Template")
                        .HasColumnType("json")
                        .HasColumnName("template");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_published_model_templates");

                    b.HasIndex("ModelHeaderId")
                        .HasDatabaseName("ix_published_model_templates_model_header_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_published_model_templates_tenant_id");

                    b.ToTable("published_model_templates", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.BusinessUnit", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int?>("AccOBSBunitId")
                        .HasColumnType("integer")
                        .HasColumnName("acc_obs_bunit_id");

                    b.Property<short>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("address");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<string>("Fax")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("fax");

                    b.Property<string>("GeoCoordinates")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("geo_coordinates");

                    b.Property<string>("Hierarchy")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("hierarchy");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("level");

                    b.Property<string>("Logo")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("logo");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("OBSSettingId")
                        .HasColumnType("integer")
                        .HasColumnName("obs_setting_id");

                    b.Property<int?>("ParentBusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_business_unit_id");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("phone");

                    b.Property<string>("PinCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("pin_code");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("remarks");

                    b.Property<string>("SettingDetail")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("setting_detail");

                    b.Property<int?>("Tenant")
                        .HasColumnType("integer")
                        .HasColumnName("tenant");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_date");

                    b.Property<string>("WebSite")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("web_site");

                    b.HasKey("Id")
                        .HasName("pk_business_units");

                    b.HasIndex("OBSSettingId")
                        .HasDatabaseName("ix_business_units_obs_setting_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_business_units_tenant_id");

                    b.ToTable("business_units", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Currency", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<string>("Symbol")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("symbol");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_currencies");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_currencies_code");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_currencies_tenant_id");

                    b.ToTable("currencies", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Language", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_languages");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_languages_code");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_languages_tenant_id");

                    b.ToTable("languages", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.LookUpInfo", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int>("LookUpTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("look_up_type_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<decimal?>("SortOrder")
                        .HasColumnType("numeric")
                        .HasColumnName("sort_order");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_look_up_info");

                    b.HasIndex("LookUpTypeId")
                        .HasDatabaseName("ix_look_up_info_look_up_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_look_up_info_tenant_id");

                    b.ToTable("look_up_info", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.LookUpType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("field_name");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<Guid?>("TaxonomyId")
                        .HasColumnType("uuid")
                        .HasColumnName("taxonomy_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_look_up_types");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_look_up_types_tenant_id");

                    b.ToTable("look_up_types", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Menu", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Action")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("action");

                    b.Property<string>("Area")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("area");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("color");

                    b.Property<string>("ComponentName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("component_name");

                    b.Property<string>("ConfigurationSettings")
                        .HasColumnType("json")
                        .HasColumnName("configuration_settings");

                    b.Property<string>("Controller")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("controller");

                    b.Property<string>("ControllerType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("controller_type");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsHidden")
                        .HasColumnType("smallint")
                        .HasColumnName("is_hidden");

                    b.Property<int>("MenuType")
                        .HasColumnType("integer")
                        .HasColumnName("menu_type");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer")
                        .HasColumnName("module_id");

                    b.Property<int?>("ObjectType")
                        .HasColumnType("integer")
                        .HasColumnName("object_type");

                    b.Property<int?>("ParentMenuId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_menu_id");

                    b.Property<string>("Path")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("path");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("remarks");

                    b.Property<int?>("ServiceComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("service_component_id");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("TemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("template_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("title");

                    b.Property<string>("URL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("url");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_menu_config");

                    b.HasIndex("ModuleId")
                        .HasDatabaseName("ix_menu_config_module_id");

                    b.HasIndex("ParentMenuId")
                        .HasDatabaseName("ix_menu_config_parent_menu_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_menu_config_tenant_id");

                    b.ToTable("menu_config", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.MenuDetail", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int?>("MenuAdd")
                        .HasColumnType("integer")
                        .HasColumnName("menu_add");

                    b.Property<int>("MenuApprove")
                        .HasColumnType("integer")
                        .HasColumnName("menu_approve");

                    b.Property<int?>("MenuBulkImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_import");

                    b.Property<int?>("MenuComments")
                        .HasColumnType("integer")
                        .HasColumnName("menu_comments");

                    b.Property<int>("MenuConfigId")
                        .HasColumnType("integer")
                        .HasColumnName("menu_config_id");

                    b.Property<int?>("MenuCorrect")
                        .HasColumnType("integer")
                        .HasColumnName("menu_correct");

                    b.Property<int>("MenuDelete")
                        .HasColumnType("integer")
                        .HasColumnName("menu_delete");

                    b.Property<int>("MenuExport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export");

                    b.Property<int?>("MenuExportRecord")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export_record");

                    b.Property<int>("MenuImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_import");

                    b.Property<int?>("MenuJSONEdit")
                        .HasColumnType("integer")
                        .HasColumnName("menu_json_edit");

                    b.Property<int?>("MenuModify")
                        .HasColumnType("integer")
                        .HasColumnName("menu_modify");

                    b.Property<int>("MenuPreDatedEntry")
                        .HasColumnType("integer")
                        .HasColumnName("menu_pre_dated_entry");

                    b.Property<int?>("MenuPrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_print");

                    b.Property<int?>("MenuProcess")
                        .HasColumnType("integer")
                        .HasColumnName("menu_process");

                    b.Property<int?>("MenuRePrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_re_print");

                    b.Property<int?>("MenuRestrictedView")
                        .HasColumnType("integer")
                        .HasColumnName("menu_restricted_view");

                    b.Property<int?>("MenuSpecial1")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special1");

                    b.Property<int?>("MenuSpecial2")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special2");

                    b.Property<int?>("MenuSpecial3")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special3");

                    b.Property<int?>("MenuSpecial4")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special4");

                    b.Property<int?>("MenuSpecial5")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special5");

                    b.Property<int?>("MenuToolTip")
                        .HasColumnType("integer")
                        .HasColumnName("menu_tool_tip");

                    b.Property<int>("MenuValidation")
                        .HasColumnType("integer")
                        .HasColumnName("menu_validation");

                    b.Property<int?>("MenuView")
                        .HasColumnType("integer")
                        .HasColumnName("menu_view");

                    b.Property<int?>("Menucancel")
                        .HasColumnType("integer")
                        .HasColumnName("menucancel");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("model_definition_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_menu_details");

                    b.HasIndex("MenuConfigId")
                        .HasDatabaseName("ix_menu_details_menu_config_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_menu_details_tenant_id");

                    b.ToTable("menu_details", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Module", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("color");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DashboardURL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("dashboard_url");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ModuleGroupId")
                        .HasColumnType("integer")
                        .HasColumnName("module_group_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_modules");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_modules_code");

                    b.HasIndex("ModuleGroupId")
                        .HasDatabaseName("ix_modules_module_group_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_modules_tenant_id");

                    b.ToTable("modules", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.ModuleGroup", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("color");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DashboardURL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("dashboard_url");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TileMenuPos")
                        .HasColumnType("text")
                        .HasColumnName("tile_menu_pos");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_module_groups");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_module_groups_code");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_module_groups_tenant_id");

                    b.ToTable("module_groups", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.OBSSetting", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<short>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Hierarchy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("hierarchy");

                    b.Property<short>("IsAccountingUnit")
                        .HasColumnType("smallint")
                        .HasColumnName("is_accounting_unit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsExpenseTrackingUnit")
                        .HasColumnType("smallint")
                        .HasColumnName("is_expense_tracking_unit");

                    b.Property<int>("Level")
                        .HasColumnType("integer")
                        .HasColumnName("level");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<int>("OBSTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("obs_type_id");

                    b.Property<int?>("ParentOBSTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_obs_type_id");

                    b.Property<int?>("SchemaDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("schema_definition_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_obs_settings");

                    b.HasIndex("OBSTypeId")
                        .HasDatabaseName("ix_obs_settings_obs_type_id");

                    b.HasIndex("ParentOBSTypeId")
                        .HasDatabaseName("ix_obs_settings_parent_obs_type_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_obs_settings_tenant_id");

                    b.ToTable("obs_settings", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.PasswordHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("password_hash");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_password_histories");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_password_histories_tenant_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_password_histories_user_id");

                    b.ToTable("password_histories", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Role", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<decimal>("Level")
                        .HasColumnType("numeric")
                        .HasColumnName("level");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_role_info");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_role_info_tenant_id");

                    b.ToTable("role_info", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.RoleMenuMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseSerialColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int?>("MenuAdd")
                        .HasColumnType("integer")
                        .HasColumnName("menu_add");

                    b.Property<int?>("MenuApprove")
                        .HasColumnType("integer")
                        .HasColumnName("menu_approve");

                    b.Property<int?>("MenuBulkDelete")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_delete");

                    b.Property<int?>("MenuBulkImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_import");

                    b.Property<int?>("MenuBulkUpdate")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_update");

                    b.Property<int?>("MenuCancel")
                        .HasColumnType("integer")
                        .HasColumnName("menu_cancel");

                    b.Property<int?>("MenuCorrect")
                        .HasColumnType("integer")
                        .HasColumnName("menu_correct");

                    b.Property<int?>("MenuDelete")
                        .HasColumnType("integer")
                        .HasColumnName("menu_delete");

                    b.Property<int?>("MenuExport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export");

                    b.Property<int?>("MenuExportRecord")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export_record");

                    b.Property<int>("MenuId")
                        .HasColumnType("integer")
                        .HasColumnName("menu_id");

                    b.Property<int?>("MenuImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_import");

                    b.Property<int?>("MenuJSONEdit")
                        .HasColumnType("integer")
                        .HasColumnName("menu_json_edit");

                    b.Property<int?>("MenuModify")
                        .HasColumnType("integer")
                        .HasColumnName("menu_modify");

                    b.Property<int?>("MenuPreDatedEntry")
                        .HasColumnType("integer")
                        .HasColumnName("menu_pre_dated_entry");

                    b.Property<int?>("MenuPrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_print");

                    b.Property<int?>("MenuProcess")
                        .HasColumnType("integer")
                        .HasColumnName("menu_process");

                    b.Property<int?>("MenuRePrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_re_print");

                    b.Property<int?>("MenuSpecial1")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special1");

                    b.Property<int?>("MenuSpecial2")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special2");

                    b.Property<int?>("MenuSpecial3")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special3");

                    b.Property<int?>("MenuSpecial4")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special4");

                    b.Property<int?>("MenuSpecial5")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special5");

                    b.Property<int?>("MenuValidation")
                        .HasColumnType("integer")
                        .HasColumnName("menu_validation");

                    b.Property<int?>("MenuView")
                        .HasColumnType("integer")
                        .HasColumnName("menu_view");

                    b.Property<int?>("RestrictedView")
                        .HasColumnType("integer")
                        .HasColumnName("restricted_view");

                    b.Property<int?>("RoleMenuMasterId")
                        .HasColumnType("integer")
                        .HasColumnName("role_menu_master_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_role_menu_mappings");

                    b.HasIndex("MenuId")
                        .HasDatabaseName("ix_role_menu_mappings_menu_id");

                    b.HasIndex("RoleMenuMasterId")
                        .HasDatabaseName("ix_role_menu_mappings_role_menu_master_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_role_menu_mappings_tenant_id");

                    b.ToTable("role_menu_mappings", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.RolePermissionMapping", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasColumnName("active");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int>("MenuAdd")
                        .HasColumnType("integer")
                        .HasColumnName("menu_add");

                    b.Property<int>("MenuApprove")
                        .HasColumnType("integer")
                        .HasColumnName("menu_approve");

                    b.Property<int>("MenuBulkDelete")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_delete");

                    b.Property<int>("MenuBulkImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_import");

                    b.Property<int>("MenuBulkUpdate")
                        .HasColumnType("integer")
                        .HasColumnName("menu_bulk_update");

                    b.Property<int>("MenuCancel")
                        .HasColumnType("integer")
                        .HasColumnName("menu_cancel");

                    b.Property<int>("MenuCorrect")
                        .HasColumnType("integer")
                        .HasColumnName("menu_correct");

                    b.Property<int>("MenuDelete")
                        .HasColumnType("integer")
                        .HasColumnName("menu_delete");

                    b.Property<int>("MenuExport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export");

                    b.Property<int>("MenuExportRecord")
                        .HasColumnType("integer")
                        .HasColumnName("menu_export_record");

                    b.Property<int>("MenuImport")
                        .HasColumnType("integer")
                        .HasColumnName("menu_import");

                    b.Property<int>("MenuJSONEdit")
                        .HasColumnType("integer")
                        .HasColumnName("menu_json_edit");

                    b.Property<int>("MenuModify")
                        .HasColumnType("integer")
                        .HasColumnName("menu_modify");

                    b.Property<int>("MenuPreDatedEntry")
                        .HasColumnType("integer")
                        .HasColumnName("menu_pre_dated_entry");

                    b.Property<int>("MenuPrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_print");

                    b.Property<int>("MenuProcess")
                        .HasColumnType("integer")
                        .HasColumnName("menu_process");

                    b.Property<int>("MenuRePrint")
                        .HasColumnType("integer")
                        .HasColumnName("menu_re_print");

                    b.Property<int>("MenuSpecial1")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special1");

                    b.Property<int>("MenuSpecial2")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special2");

                    b.Property<int>("MenuSpecial3")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special3");

                    b.Property<int>("MenuSpecial4")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special4");

                    b.Property<int>("MenuSpecial5")
                        .HasColumnType("integer")
                        .HasColumnName("menu_special5");

                    b.Property<int>("MenuValidation")
                        .HasColumnType("integer")
                        .HasColumnName("menu_validation");

                    b.Property<int>("MenuView")
                        .HasColumnType("integer")
                        .HasColumnName("menu_view");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("model_definition_id");

                    b.Property<int>("RestrictedView")
                        .HasColumnType("integer")
                        .HasColumnName("restricted_view");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer")
                        .HasColumnName("role_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_role_permission_mappings");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_role_permission_mappings_role_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_role_permission_mappings_tenant_id");

                    b.ToTable("role_permission_mappings", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Sequence", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IncrementBy")
                        .HasColumnType("integer")
                        .HasColumnName("increment_by");

                    b.Property<int>("MaximumValue")
                        .HasColumnType("integer")
                        .HasColumnName("maximum_value");

                    b.Property<int>("MinimumValue")
                        .HasColumnType("integer")
                        .HasColumnName("minimum_value");

                    b.Property<int>("NextValue")
                        .HasColumnType("integer")
                        .HasColumnName("next_value");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("table_name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_sequence_info");

                    b.HasIndex("TableName")
                        .IsUnique()
                        .HasDatabaseName("ix_sequence_info_table_name");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_sequence_info_tenant_id");

                    b.ToTable("sequence_info", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Setting", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("BusinessRules")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("business_rules");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Criteria")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("criteria");

                    b.Property<int>("DataTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("data_type_id");

                    b.Property<string>("Group")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("group");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer")
                        .HasColumnName("module_id");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("remarks");

                    b.Property<int?>("SchemaDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("schema_definition_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_settings");

                    b.HasIndex("DataTypeId")
                        .HasDatabaseName("ix_settings_data_type_id");

                    b.HasIndex("ModuleId")
                        .HasDatabaseName("ix_settings_module_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_settings_tenant_id");

                    b.ToTable("settings", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.StandardSchema", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("MenuId")
                        .HasColumnType("integer")
                        .HasColumnName("menu_id");

                    b.Property<string>("Schema")
                        .HasColumnType("json")
                        .HasColumnName("schema");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_standardschemas");

                    b.HasIndex("MenuId")
                        .HasDatabaseName("ix_standardschemas_menu_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_standardschemas_tenant_id");

                    b.ToTable("standardschemas", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.User", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("EffectiveFrom")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("effective_from");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("effective_to");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("boolean")
                        .HasColumnName("is_locked");

                    b.Property<string>("LoginName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("login_name");

                    b.Property<string>("NormalizedEmail")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedLoginName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("normalized_login_name");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("password_hash");

                    b.Property<int?>("RoleId")
                        .HasColumnType("integer")
                        .HasColumnName("role_id");

                    b.Property<int?>("StatusId")
                        .HasColumnType("integer")
                        .HasColumnName("status_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("pk_user_info");

                    b.HasIndex("NormalizedEmail")
                        .IsUnique()
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedLoginName")
                        .IsUnique()
                        .HasDatabaseName("LoginNameIndex");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_user_info_role_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_info_tenant_id");

                    b.ToTable("user_info", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.UserInvitation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<DateTime>("ExpireOn")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("expire_on");

                    b.Property<string>("InvitationURL")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("invitation_url");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("StatusId")
                        .HasColumnType("integer")
                        .HasColumnName("status_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("token");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_invitations");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_invitations_tenant_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_invitations_user_id");

                    b.ToTable("user_invitations", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.UserPermission", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int?>("AppId")
                        .HasColumnType("integer")
                        .HasColumnName("app_id");

                    b.Property<int>("BusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("business_unit_id");

                    b.Property<string>("BusinessUnitValue")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("business_unit_value");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("EffectiveFrom")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("effective_from");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("effective_to");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint")
                        .HasColumnName("is_system_defined");

                    b.Property<int?>("ModuleGroupId")
                        .HasColumnType("integer")
                        .HasColumnName("module_group_id");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer")
                        .HasColumnName("module_id");

                    b.Property<bool>("PermissionFlag")
                        .HasColumnType("boolean")
                        .HasColumnName("permission_flag");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer")
                        .HasColumnName("role_id");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<bool>("UserSameLevelFlag")
                        .HasColumnType("boolean")
                        .HasColumnName("user_same_level_flag");

                    b.HasKey("Id")
                        .HasName("pk_user_permissions");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_user_permissions_role_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_user_permissions_tenant_id");

                    b.ToTable("user_permissions", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.TenantManagement.Program", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer")
                        .HasColumnName("business_unit_id");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_programs");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_programs_code");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_programs_tenant_id");

                    b.ToTable("programs", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.TenantManagement.ProgramStage", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int>("GrantStageId")
                        .HasColumnType("integer")
                        .HasColumnName("grant_stage_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<int>("ProgramId")
                        .HasColumnType("integer")
                        .HasColumnName("program_id");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_program_stage");

                    b.HasIndex("GrantStageId")
                        .HasDatabaseName("ix_program_stage_grant_stage_id");

                    b.HasIndex("ProgramId")
                        .HasDatabaseName("ix_program_stage_program_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_program_stage_tenant_id");

                    b.ToTable("program_stage", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowDefinition", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.HasKey("Id")
                        .HasName("pk_workflow_definitions");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_definitions_tenant_id");

                    b.ToTable("workflow_definitions", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowHistory", b =>
                {
                    b.Property<Guid>("WorkflowHistoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_history_id");

                    b.Property<string>("Activity")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("activity");

                    b.Property<string>("Comments")
                        .HasColumnType("text")
                        .HasColumnName("comments");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_date");

                    b.Property<short?>("ExecutionStatus")
                        .HasColumnType("smallint")
                        .HasColumnName("execution_status");

                    b.Property<int?>("FromStepId")
                        .HasColumnType("integer")
                        .HasColumnName("from_step_id");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("ip_address");

                    b.Property<JsonDocument>("JsonData")
                        .HasColumnType("jsonb")
                        .HasColumnName("json_data");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("ToStepId")
                        .HasColumnType("integer")
                        .HasColumnName("to_step_id");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("WorkflowHistoryId")
                        .HasName("pk_workflow_histories");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_histories_tenant_id");

                    b.ToTable("workflow_histories", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowInstance", b =>
                {
                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.Property<Guid?>("CorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("correlation_id");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_on");

                    b.Property<string>("CurrentStatus")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("current_status");

                    b.Property<int?>("CurrentStepId")
                        .HasColumnType("integer")
                        .HasColumnName("current_step_id");

                    b.Property<short?>("ExecutionStatus")
                        .HasColumnType("smallint")
                        .HasColumnName("execution_status");

                    b.Property<short?>("IsCompleted")
                        .HasColumnType("smallint")
                        .HasColumnName("is_completed");

                    b.Property<short?>("IsInitialStep")
                        .HasColumnType("smallint")
                        .HasColumnName("is_initial_step");

                    b.Property<short?>("IsReturned")
                        .HasColumnType("smallint")
                        .HasColumnName("is_returned");

                    b.Property<int?>("PreviousStepId")
                        .HasColumnType("integer")
                        .HasColumnName("previous_step_id");

                    b.Property<int?>("RevisionNo")
                        .HasColumnType("integer")
                        .HasColumnName("revision_no");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_on");

                    b.Property<JsonDocument>("WorkflowData")
                        .HasColumnType("jsonb")
                        .HasColumnName("workflow_data");

                    b.Property<int>("WorkflowDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("workflow_definition_id");

                    b.HasKey("WorkflowInstanceId")
                        .HasName("pk_workflow_instances");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_instances_tenant_id");

                    b.HasIndex("WorkflowDefinitionId")
                        .HasDatabaseName("ix_workflow_instances_workflow_definition_id");

                    b.ToTable("workflow_instances", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowPermission", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer")
                        .HasColumnName("role_id");

                    b.Property<JsonDocument>("StepTransitions")
                        .HasColumnType("jsonb")
                        .HasColumnName("step_transitions");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("WorkflowDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("workflow_definition_id");

                    b.HasKey("Id")
                        .HasName("pk_workflow_permissions");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_workflow_permissions_role_id");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_permissions_tenant_id");

                    b.HasIndex("WorkflowDefinitionId")
                        .HasDatabaseName("ix_workflow_permissions_workflow_definition_id");

                    b.ToTable("workflow_permissions", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowReturnLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasColumnType("text")
                        .HasColumnName("comment");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<int?>("CurrentStepId")
                        .HasColumnType("integer")
                        .HasColumnName("current_step_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ReturnStatus")
                        .HasColumnType("integer")
                        .HasColumnName("return_status");

                    b.Property<int?>("RevisionNo")
                        .HasColumnType("integer")
                        .HasColumnName("revision_no");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.HasKey("Id")
                        .HasName("pk_workflow_return_logs");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_return_logs_tenant_id");

                    b.ToTable("workflow_return_logs", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowStep", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("action");

                    b.Property<short?>("AllowRollback")
                        .HasColumnType("smallint")
                        .HasColumnName("allow_rollback");

                    b.Property<short?>("CanPause")
                        .HasColumnType("smallint")
                        .HasColumnName("can_pause");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer")
                        .HasColumnName("created_by_id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<short?>("IsForwardFlow")
                        .HasColumnType("smallint")
                        .HasColumnName("is_forward_flow");

                    b.Property<short?>("LockStatus")
                        .HasColumnType("smallint")
                        .HasColumnName("lock_status");

                    b.Property<string>("ReturnStatus")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("return_status");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("StepCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)")
                        .HasColumnName("step_code");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("step_name");

                    b.Property<int>("StepNo")
                        .HasColumnType("integer")
                        .HasColumnName("step_no");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer")
                        .HasColumnName("tenant_id");

                    b.Property<short?>("TriggerService")
                        .HasColumnType("smallint")
                        .HasColumnName("trigger_service");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer")
                        .HasColumnName("updated_by_id");

                    b.Property<int>("WorkflowDefinitionId")
                        .HasColumnType("integer")
                        .HasColumnName("workflow_definition_id");

                    b.HasKey("Id")
                        .HasName("pk_workflow_steps");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("ix_workflow_steps_tenant_id");

                    b.HasIndex("WorkflowDefinitionId")
                        .HasDatabaseName("ix_workflow_steps_workflow_definition_id");

                    b.ToTable("workflow_steps", "pan_gms");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.RoleMenuMaster", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_role_menu_masters_modules_module_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_role_menu_masters_roles_role_id");

                    b.Navigation("Module");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.UserGroupMapping", b =>
                {
                    b.HasOne("UNAIDS.Model.Administration.UserGroups", "UserGroups")
                        .WithMany("UserGroupMapping")
                        .HasForeignKey("UserGroupsId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_user_group_mapping_user_groups_user_groups_id");

                    b.Navigation("UserGroups");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.CallProgramStage", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.CallForGrants", "CallForGrants")
                        .WithMany()
                        .HasForeignKey("CallId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_call_program_stages_call_for_grants_call_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "FormHeader")
                        .WithMany()
                        .HasForeignKey("FormTemplateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_call_program_stages_model_headers_form_template_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.ProgramStage", "ProgramStage")
                        .WithMany()
                        .HasForeignKey("ProgramStageId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_call_program_stages_program_stages_program_stage_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_call_program_stages_workflow_definitions_workflow_id");

                    b.Navigation("CallForGrants");

                    b.Navigation("FormHeader");

                    b.Navigation("ProgramStage");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.DocumentStoreDetails", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.DocumentStore", "DocumentStore")
                        .WithMany()
                        .HasForeignKey("DocumentStoreId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_document_store_details_document_stores_document_store_id");

                    b.Navigation("DocumentStore");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.Indicator", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "CollectionAgentType")
                        .WithMany()
                        .HasForeignKey("CollectionAgentTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_collection_agent_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "CollectionMethod")
                        .WithMany()
                        .HasForeignKey("CollectionMethodId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_collection_method_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "DataType")
                        .WithMany()
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_data_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "Format")
                        .WithMany()
                        .HasForeignKey("FormatId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_format_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "IndicatorType")
                        .WithMany()
                        .HasForeignKey("IndicatorTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_indicator_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "MeasurementType")
                        .WithMany()
                        .HasForeignKey("MeasurementTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_measurement_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.DataElement", "ParentIndicator")
                        .WithMany()
                        .HasForeignKey("ParentIndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_data_elements_parent_indicator_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "ReportingType")
                        .WithMany()
                        .HasForeignKey("ReportingTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_indicators_look_up_info_reporting_type_id");

                    b.Navigation("CollectionAgentType");

                    b.Navigation("CollectionMethod");

                    b.Navigation("DataType");

                    b.Navigation("Format");

                    b.Navigation("IndicatorType");

                    b.Navigation("MeasurementType");

                    b.Navigation("ParentIndicator");

                    b.Navigation("ReportingType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ModelDataElement", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.Model", "NavigationModel")
                        .WithMany()
                        .HasForeignKey("ModelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_model_data_elements_models_model_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.Model", "Model")
                        .WithMany()
                        .HasForeignKey("ModelId1")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_model_data_elements_models_model_id1");

                    b.Navigation("Model");

                    b.Navigation("NavigationModel");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.Amendment", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_amendments_applications_application_id");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.Closure", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_closure_applications_application_id");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PostAwardManagement.PerformanceReport", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_performance_reports_applications_application_id");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Currency", "Currency")
                        .WithMany()
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_applications_currencies_currency_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "ModelHeader")
                        .WithMany()
                        .HasForeignKey("ModelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_applications_model_headers_model_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.PreAwardManagement.Opportunity", "Opportunity")
                        .WithMany()
                        .HasForeignKey("OpportunityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_applications_opportunities_opportunity_id");

                    b.Navigation("Currency");

                    b.Navigation("ModelHeader");

                    b.Navigation("Opportunity");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Opportunity", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "ModelHeader")
                        .WithMany()
                        .HasForeignKey("ModelHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_opportunities_model_headers_model_header_id");

                    b.Navigation("ModelHeader");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PreAwardManagement.Review", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.PreAwardManagement.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_reviews_applications_application_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.User", "Reviewer")
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_reviews_users_reviewer_id");

                    b.Navigation("Application");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Category", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "CategoryType")
                        .WithMany()
                        .HasForeignKey("CategoryTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_categories_look_up_info_category_type_id");

                    b.Navigation("CategoryType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.CategoryOption", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_category_options_categories_category_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.CategoryOption", "ParentOption")
                        .WithMany()
                        .HasForeignKey("ParentOptionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_category_options_category_options_parent_option_id");

                    b.Navigation("Category");

                    b.Navigation("ParentOption");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.CountryDetail", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_country_details_countries_country_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "FCS")
                        .WithMany()
                        .HasForeignKey("FCSId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_country_details_look_up_info_fcs_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_country_details_fiscal_years_fiscal_year_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "IncomeGroup")
                        .WithMany()
                        .HasForeignKey("IncomeGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_country_details_look_up_info_income_group_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "LendingCategory")
                        .WithMany()
                        .HasForeignKey("LendingCategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_country_details_look_up_info_lending_category_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "Region")
                        .WithMany()
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_country_details_look_up_info_region_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "SIDS")
                        .WithMany()
                        .HasForeignKey("SIDSId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_country_details_look_up_info_sids_id");

                    b.Navigation("Country");

                    b.Navigation("FCS");

                    b.Navigation("FiscalYear");

                    b.Navigation("IncomeGroup");

                    b.Navigation("LendingCategory");

                    b.Navigation("Region");

                    b.Navigation("SIDS");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.DataElement", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", "Concept")
                        .WithMany()
                        .HasForeignKey("ConceptId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_data_elements_concepts_concept_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "DataType")
                        .WithMany()
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_data_elements_look_up_info_data_type_id");

                    b.Navigation("Concept");

                    b.Navigation("DataType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Events", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "EventType")
                        .WithMany()
                        .HasForeignKey("EventTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_events_look_up_info_event_type_id");

                    b.Navigation("EventType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.Framework", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "FrameworkType")
                        .WithMany()
                        .HasForeignKey("FrameworkTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_frameworks_look_up_info_framework_type_id");

                    b.Navigation("FrameworkType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.FrameworkData", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Framework", "Framework")
                        .WithMany()
                        .HasForeignKey("FrameworkId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_framework_data_frameworks_framework_id");

                    b.Navigation("Framework");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.FrameworkHierarchy", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", "Concept")
                        .WithMany()
                        .HasForeignKey("ConceptId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_framework_hierarchy_concepts_concept_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Framework", "Framework")
                        .WithMany()
                        .HasForeignKey("FrameworkId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_framework_hierarchy_frameworks_framework_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", "ParentConcept")
                        .WithMany()
                        .HasForeignKey("ParentConceptId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_framework_hierarchy_concepts_parent_concept_id");

                    b.Navigation("Concept");

                    b.Navigation("Framework");

                    b.Navigation("ParentConcept");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.LocalDataElement", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", "Concept")
                        .WithMany()
                        .HasForeignKey("ConceptId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_local_data_elements_concepts_concept_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "DataType")
                        .WithMany()
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_local_data_elements_look_up_info_data_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_local_data_elements_programs_program_id");

                    b.Navigation("Concept");

                    b.Navigation("DataType");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramModel", "Entity")
                        .WithMany()
                        .HasForeignKey("EntityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_model_headers_program_models_entity_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelSchema", "FormSchema")
                        .WithMany()
                        .HasForeignKey("SchemaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_model_headers_model_schemas_schema_id");

                    b.Navigation("Entity");

                    b.Navigation("FormSchema");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelTemplate", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "ModelHeader")
                        .WithMany()
                        .HasForeignKey("ModelHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_model_templates_model_headers_model_header_id");

                    b.Navigation("ModelHeader");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelWorkflow", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "ModelHeader")
                        .WithMany()
                        .HasForeignKey("ModelHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_model_workflows_model_headers_model_header_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.WorkflowDefinition", "Workflow")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_model_workflows_workflow_definitions_workflow_id");

                    b.Navigation("ModelHeader");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramCategory", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_categories_categories_category_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_categories_programs_program_id");

                    b.Navigation("Category");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramCategoryOption", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.CategoryOption", "CategoryOption")
                        .WithMany()
                        .HasForeignKey("CategoryOptionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_category_options_category_options_category_option_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_category_options_programs_program_id");

                    b.Navigation("CategoryOption");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramDataElement", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.DataElement", "DataElement")
                        .WithMany()
                        .HasForeignKey("DataElementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_data_elements_data_elements_data_element_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_data_elements_programs_program_id");

                    b.Navigation("DataElement");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchy", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_hierarchy_program_hierarchy_categories_category_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchy", "Parent")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_program_hierarchy_program_hierarchy_parent_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_hierarchy_programs_program_id");

                    b.Navigation("Category");

                    b.Navigation("Parent");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyCategory", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_hierarchy_categories_programs_program_id");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyLevel", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyLevel", "ParentLevel")
                        .WithMany()
                        .HasForeignKey("ParentLevelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_program_hierarchy_levels_program_hierarchy_levels_parent_le");

                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ProgramHierarchyCategory", "ProgramHierarchyElement")
                        .WithMany()
                        .HasForeignKey("ProgramHierarchyElementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_hierarchy_levels_program_hierarchy_elements_program");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_program_hierarchy_levels_programs_program_id");

                    b.Navigation("ParentLevel");

                    b.Navigation("Program");

                    b.Navigation("ProgramHierarchyElement");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramConcept", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.Concept", "Concept")
                        .WithMany()
                        .HasForeignKey("ConceptId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_concepts_concepts_concept_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_concepts_programs_program_id");

                    b.Navigation("Concept");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.ProgramModel", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.Model", "Model")
                        .WithMany()
                        .HasForeignKey("ModelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_models_model_model_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_models_programs_program_id");

                    b.Navigation("Model");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.PublishedModelTemplate", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.ProgramAdministration.ModelHeader", "ModelHeader")
                        .WithMany()
                        .HasForeignKey("ModelHeaderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_published_model_templates_model_headers_model_header_id");

                    b.Navigation("ModelHeader");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.BusinessUnit", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.OBSSetting", "OBSSetting")
                        .WithMany()
                        .HasForeignKey("OBSSettingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_business_units_obs_settings_obs_setting_id");

                    b.Navigation("OBSSetting");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.LookUpInfo", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpType", "LookUpType")
                        .WithMany("LookUpInfo")
                        .HasForeignKey("LookUpTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_look_up_info_look_up_types_look_up_type_id");

                    b.Navigation("LookUpType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Menu", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_menu_config_modules_module_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Menu", "ParentMenu")
                        .WithMany()
                        .HasForeignKey("ParentMenuId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_menu_config_menu_config_parent_menu_id");

                    b.Navigation("Module");

                    b.Navigation("ParentMenu");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.MenuDetail", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Menu", "MenuConfig")
                        .WithMany()
                        .HasForeignKey("MenuConfigId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_menu_details_menus_menu_config_id");

                    b.Navigation("MenuConfig");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Module", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.ModuleGroup", "ModuleGroup")
                        .WithMany("Module")
                        .HasForeignKey("ModuleGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_modules_module_groups_module_group_id");

                    b.Navigation("ModuleGroup");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.OBSSetting", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "OBSType")
                        .WithMany()
                        .HasForeignKey("OBSTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_obs_settings_look_up_info_obs_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "ParentOBSType")
                        .WithMany()
                        .HasForeignKey("ParentOBSTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_obs_settings_look_up_info_parent_obs_type_id");

                    b.Navigation("OBSType");

                    b.Navigation("ParentOBSType");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.PasswordHistory", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.User", "User")
                        .WithMany("PasswordHistories")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_password_histories_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.RoleMenuMapping", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Menu", "Menu")
                        .WithMany()
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_role_menu_mappings_menus_menu_id");

                    b.HasOne("UNAIDS.Model.Administration.RoleMenuMaster", "RoleMenuMaster")
                        .WithMany("RoleMenuMapping")
                        .HasForeignKey("RoleMenuMasterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_role_menu_mappings_role_menu_masters_role_menu_master_id");

                    b.Navigation("Menu");

                    b.Navigation("RoleMenuMaster");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.RolePermissionMapping", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_role_permission_mappings_roles_role_id");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.Setting", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "DataType")
                        .WithMany()
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_settings_look_up_info_data_type_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_settings_modules_module_id");

                    b.Navigation("DataType");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.StandardSchema", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Menu", "Menu")
                        .WithMany()
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_standardschemas_menu_config_menu_id");

                    b.Navigation("Menu");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.User", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_user_info_role_info_role_id");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.UserInvitation", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_user_invitations_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.UserPermission", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_user_permissions_roles_role_id");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.TenantManagement.ProgramStage", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.LookUpInfo", "GrantStage")
                        .WithMany()
                        .HasForeignKey("GrantStageId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_stage_look_up_info_grant_stage_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.TenantManagement.Program", "Program")
                        .WithMany()
                        .HasForeignKey("ProgramId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_program_stage_programs_program_id");

                    b.Navigation("GrantStage");

                    b.Navigation("Program");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowInstance", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany()
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_workflow_instances_workflow_definitions_workflow_definition");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowPermission", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.System.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_workflow_permissions_role_info_role_id");

                    b.HasOne("UNAIDS.HivScorecards.Domain.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany()
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_workflow_permissions_workflow_definitions_workflow_definiti");

                    b.Navigation("Role");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.WorkflowStep", b =>
                {
                    b.HasOne("UNAIDS.HivScorecards.Domain.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany()
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_workflow_steps_workflow_definitions_workflow_definition_id");

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.RoleMenuMaster", b =>
                {
                    b.Navigation("RoleMenuMapping");
                });

            modelBuilder.Entity("UNAIDS.Model.Administration.UserGroups", b =>
                {
                    b.Navigation("UserGroupMapping");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.LookUpType", b =>
                {
                    b.Navigation("LookUpInfo");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.ModuleGroup", b =>
                {
                    b.Navigation("Module");
                });

            modelBuilder.Entity("UNAIDS.HivScorecards.Domain.System.User", b =>
                {
                    b.Navigation("PasswordHistories");
                });
#pragma warning restore 612, 618
        }
    }
}
