﻿
using AutoMapper;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Authentication.Shared
{
    public class AzureADUserManager (IUnitOfWork<CommonDbContext> _unitOfWork,
        IMapper _mapper,
        ISequenceRepository _sequenceRepository,
        ITenantProvider _tenantProvider,
        IUserContext _userContext) : BaseUserManager(
            _unitOfWork,
            _mapper,
            _sequenceRepository,
            _tenantProvider,
            _userContext), IUserManager
    {

    }
}
