using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IUserAssignedRolesRepository : IBaseRepository<User>
    {
        Task<List<CommonLookUp>> GetUserAssignedRoles(Filter filter);
        Task<List<CommonLookUp>> GetBusinessUnits();
        Task<List<CommonLookUp>> GetUserNames();
        Task<List<CommonLookUp>> GetLoginNames();
    }
}
