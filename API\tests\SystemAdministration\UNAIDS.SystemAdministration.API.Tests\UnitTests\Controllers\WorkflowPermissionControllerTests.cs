using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class WorkflowPermissionControllerTests {
        private readonly Mock<IWorkflowPermissionService<WorkflowPermissionDTO>> _mockService;
        private readonly Mock<IValidator<WorkflowPermissionDTO>> _mockValidator;
        private readonly WorkflowPermissionController _controller;
        private readonly Fixture _fixture;

        public WorkflowPermissionControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IWorkflowPermissionService<WorkflowPermissionDTO>>();
            _mockValidator = new Mock<IValidator<WorkflowPermissionDTO>>();
            _controller = new WorkflowPermissionController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();

            // Customizing the WorkflowPermissionDTO to include a specific JSON structure in StepTransitions, This ensures that the DTO's StepTransitions property is properly initialized for testing purposes.
            _fixture.Customize<WorkflowPermissionDTO>(c => c.With(dto => dto.StepTransitions, JsonDocument.Parse("{}")));
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedReturnData = _fixture.CreateMany<WorkflowPermissionDTO>(3).ToList();
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AEB";
            
            
            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetUserRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWPERMISSION)).Returns(expectedUserRights);

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var roles = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedRoles, roles);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }


        [Fact]
        public async Task Get_ReturnsNotFound_WhenDataIsNull() {
            // Arrange
            var filter = _fixture.Create<Filter>();

            // Act
            var result = await _controller.Get(filter.Id);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedReturnData = _fixture.Create<WorkflowPermissionDTO>();
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AEB";  
       
            _mockService.Setup(service => service.GetById(expectedReturnData.Id)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetUserRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWPERMISSION));
            
            // Act
            var result = await _controller.Get(expectedReturnData.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var roles = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedRoles, roles);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenWorkflowPermissionIsNull() {
            // Arrange
            WorkflowPermissionDTO? workflowPermission = null;

            // Act
            var result = await _controller.Create(workflowPermission);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowPermissionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowPermissionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenWorkflowPermissionIsValid() {
            // Arrange
            var workflowPermission = _fixture.Create<WorkflowPermissionDTO>();
            var validationResult = new ValidationResult();

            _mockValidator.Setup(v => v.ValidateAsync(workflowPermission, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(workflowPermission)).ReturnsAsync(workflowPermission);

            // Act
            var result = await _controller.Create(workflowPermission);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(workflowPermission, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowPermissionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowPermissionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenWorkflowPermissionIsValid() {
            // Arrange
            var workflowPermission = _fixture.Create<WorkflowPermissionDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(workflowPermission, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(workflowPermission)).ReturnsAsync(workflowPermission);

            // Act
            var result = await _controller.Update(workflowPermission);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task UpdateMapping_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var workflowPermission = _fixture.Create<WorkflowPermissionDTO>();

            _mockService.Setup(service => service.UpdateTransitions(workflowPermission.Id, workflowPermission.StepTransitions)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateMapping(workflowPermission.Id, workflowPermission.StepTransitions);

            // Assert

            // verfiy the service method was called with correct parameters
            _mockService.Verify(service => service.UpdateTransitions(workflowPermission.Id, workflowPermission.StepTransitions), Times.Once);
            Assert.IsType<OkResult>(result);
            
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var workflowPermission = _fixture.Create<WorkflowPermissionDTO>();
           _mockService.Setup(s => s.Delete(workflowPermission.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(workflowPermission.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

        [Fact]
        public async Task GetTransitions_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var workflowPermission = _fixture.Create<WorkflowPermissionDTO>();
            var expectedPermissions = JsonDocument.Parse("{}");
            var expectedSteps = _fixture.CreateMany<CommonLookUp>(2).ToList();


            _mockService.Setup(service => service.GetPermissions(workflowPermission.Id)).ReturnsAsync(expectedPermissions);
            _mockService.Setup(service => service.GetWorkflowSteps(workflowPermission.Id)).ReturnsAsync(expectedSteps);

            // Act
            var result = await _controller.GetTransitions(workflowPermission.Id);

            // Assert

            // verfiy the service method was called with correct parameters
            _mockService.Verify(service => service.GetPermissions(workflowPermission.Id), Times.Once);
            _mockService.Verify(service => service.GetWorkflowSteps(workflowPermission.Id), Times.Once);

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var permissions = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var steps = returnValue.GetType().GetProperty("Steps")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(permissions, expectedPermissions);
            Assert.Equal(steps, expectedSteps);

        }

    }
}
