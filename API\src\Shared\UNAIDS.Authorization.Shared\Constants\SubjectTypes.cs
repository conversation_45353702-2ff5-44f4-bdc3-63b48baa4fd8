﻿namespace UNAIDS.Authorization.Shared
{
    public class SubjectTypes
    {
        //SYSTEM ADMINISTRATION
        public const string ROLE = "Role";
        public const string USER = "User";
        public const string ROLEPERMISSIONMAPPING = "RolePermissionMapping";
        public const string SETTING = "Setting";
        public const string USERGROUPMAPPING = "UserGroupMapping";
        public const string USERGROUPS = "UserGroups";
        public const string USERPERMISSIONS = "UserPermissions";
        public const string WORKFLOWDEFINITION = "WorkFlowDefinition";
        public const string WORKFLOWPERMISSION = "WorkFlowPermission";
        public const string WORKFLOWSTEP = "WorkFlowStep";
        public const string BUSINESSUNIT = "BusinessUnit";
        public const string LOOKUP = "LookUp";
        public const string LOOKUPTYPE = "LookUpType";
        public const string MENUCONFIG = "MenuConfig";
        public const string MENUDETAIL = "MenuDetail";
        public const string PERMISSION = "Permission";

        //PROGRAM ADMINISTRATION
        public const string CATEGORY = "Category";
        public const string CATEGORYOPTION = "CategoryOption";
        public const string COUNTRY = "Country";
        public const string COUNTRYDETAILCHILD = "CountryDetailChild";
        public const string EVENTS = "Events";
        public const string FISCALYEAR = "FiscalYear";
        public const string FRAMEWORK = "Framework";
        public const string FRAMEWORKHIERARCHY = "FrameworkHierarchy";

        //PRE AWARD MANANGEMENT
        public const string APPLICATIONS = "Applications";
        public const string APPLICATIONREVIEW = "ApplicationReview";
        public const string LEAD = "Lead";

        //POST AWARD MANANGEMENT
        public const string ACTIVITYCLOSURE = "ActivityClosure";
        public const string ACTIVITYMODIFICATIONREQUEST = "ActivityModificationRequest";
        public const string PROJECTIMPLEMENTATION = "ProjectImplementation";
    }
}
