﻿using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;

namespace UNAIDS.Core.DocumentRepository
{
    [Route("api/documents")]
    [ApiController]
    public class DocumentController(IDocumentRepository docRepository, ITenantProvider tenantProvider): ControllerBase
    {
        #region Add new document...
        /// <summary>
        /// Add new document
        /// </summary>
        /// <param name="document"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromForm] DocumentRequestDto document)
        {
            if (Request.Form.Files != null && Request.Form.Files.Count > 0)
            {
                document.Folder = Convert.ToString($"{tenantProvider.TenantName}");
                var docs = await docRepository.Upload(Request.Form.Files, document);
                return Ok(docs);
            }
            return BadRequest("No files found");
        }
        #endregion

        #region Download the document...
        /// <summary>
        /// Download the document...
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("{fileName}")]
        public async Task<IActionResult> Download(string fileName)
        {
            var doc = await docRepository.Download(fileName);

            return File(doc.FileContent, doc.ContentType, doc.FileName);
        }
        #endregion

        #region Delete the document...
        /// <summary>
        /// Download the document...
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await docRepository.Delete(id);

            return Ok();
        }
        #endregion
    }
}
