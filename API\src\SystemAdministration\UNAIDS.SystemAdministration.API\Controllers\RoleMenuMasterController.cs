using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/role-menu-masters")]
    [ApiController]
    public class RoleMenuMasterController(IRoleMenuMasterService<RoleMenuMasterDTO> roleMenuMasterService, IValidator<RoleMenuMasterDTO> validator) : ControllerBase
    {
        /// <summary>
        /// Get all RoleMenuMasters
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var roleMenuMasters = await roleMenuMasterService.GetAll(filter);
            return Ok(new
            {
                Record = roleMenuMasters,
                RoleId = await roleMenuMasterService.GetRoles(),
                ModuleId = await roleMenuMasterService.Getmodules(),
                BusinessUnitId = await roleMenuMasterService.GetBusinessUnits(),
                UserRights = roleMenuMasterService.AccessRights(SubjectTypes.ROLE)
            });
        }

        /// <summary>
        /// Get a specific RoleMenuMaster by id
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var roleMenuMaster = await roleMenuMasterService.GetById(id);
            return Ok(new
            {
                Record = roleMenuMaster,
                RoleId = await roleMenuMasterService.GetRoles(),
                ModuleId = await roleMenuMasterService.Getmodules(),
                BusinessUnitId = await roleMenuMasterService.GetBusinessUnits(),
                UserRights = roleMenuMasterService.AccessRights(SubjectTypes.ROLE)
            });
        }

        /// <summary>
        /// Create a new RoleMenuMaster
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] RoleMenuMasterDTO roleMenuMaster)
        {
            if (roleMenuMaster == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(roleMenuMaster);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var createdRoleMenuMaster = await roleMenuMasterService.Create(roleMenuMaster);
            return CreatedAtAction(nameof(Get), new { id = createdRoleMenuMaster.RoleId }, createdRoleMenuMaster); 
        }

        /// <summary>
        /// Update an existing RoleMenuMaster
        /// </summary>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] RoleMenuMasterDTO roleMenuMaster)
        {
            var validationResult = await validator.ValidateAsync(roleMenuMaster);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await roleMenuMasterService.Update(roleMenuMaster);
            return Ok();
        }

        /// <summary>
        /// Delete a RoleMenuMaster by id
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await roleMenuMasterService.Delete(id);
            return Ok();
        }

    }
}
