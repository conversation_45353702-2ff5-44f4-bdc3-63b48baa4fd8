using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-group-mapping")]
    [ApiController]
    public class UserGroupMappingController(IUserGroupMappingService<UserGroupMappingDTO> userGroupMappingService,IValidator<UserGroupMappingDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list user group mappings
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPMAPPING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var userGroupMappings = await userGroupMappingService.GetAll(filter);
            return Ok(new
            {
                Record = userGroupMappings,
                UserId = await userGroupMappingService.GetUser(),
                UserRights = userGroupMappingService.AccessRights(SubjectTypes.USERGROUPMAPPING)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get the user group mapping by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPMAPPING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var userGroupMapping = await userGroupMappingService.GetById(id);
            return Ok(new
            {
                Record = userGroupMapping,
                UserRights = userGroupMappingService.AccessRights(SubjectTypes.USERGROUPMAPPING)
            });
        }
        #endregion

        /// <summary>
        /// Create a new User Group Mapping
        /// </summary>
        /// <param name="userGroupMapping"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.USERGROUPMAPPING}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] UserGroupMappingDTO userGroupMapping)
        {
            if (userGroupMapping == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(userGroupMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await userGroupMappingService.Create(userGroupMapping);
            return CreatedAtAction(nameof(Create), new { id = userGroupMapping.Id }, result);
        }

        /// <summary>
        /// Update a user group mapping
        /// </summary>
        /// <param name="userGroupMapping"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.USERGROUPMAPPING}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] UserGroupMappingDTO userGroupMapping)
        {
            var validationResult = await validator.ValidateAsync(userGroupMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await userGroupMappingService.Update(userGroupMapping);
            return Ok(userGroupMapping);
        }

        /// <summary>
        /// Delete user group mapping
        /// </summary>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPMAPPING}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await userGroupMappingService.Delete(id);
            return Ok();
        }
    }
}
