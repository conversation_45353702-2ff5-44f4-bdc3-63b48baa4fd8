using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;


namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class MenuDetailControllerTests {
        private readonly Mock<IMenuDetailService<MenuDetailDTO>> _mockService;
        private readonly Mock<IValidator<MenuDetailDTO>> _mockValidator;
        private readonly MenuDetailController _controller;
        private readonly Fixture _fixture;

        public MenuDetailControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IMenuDetailService<MenuDetailDTO>>();
            _mockValidator = new Mock<IValidator<MenuDetailDTO>>();
            _controller = new MenuDetailController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedMenuDetails = _fixture.CreateMany<MenuDetailDTO>(3).ToList();
            var expectedMenuConfig = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AEB";

            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedMenuDetails);
            _mockService.Setup(service => service.GetMenuConfig()).ReturnsAsync(expectedMenuConfig);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.MENUDETAIL)).Returns(expectedUserRights);

            

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var menuConfigValue = returnValue.GetType().GetProperty("MenuConfigId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRigts")?.GetValue(returnValue);

            Assert.NotNull(returnValue);
            Assert.Equal(expectedMenuDetails, recordValue);
            Assert.Equal(expectedMenuConfig, menuConfigValue);
            Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WithZeroId() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedMenuDetail = _fixture.Create<MenuDetailDTO>();   
            var expectedMenuConfig = _fixture.Create<List<CommonLookUp>>();  
            var expectedUserRights = "AEB";

            _mockService.Setup(service => service.GetById(expectedMenuDetail.Id)).ReturnsAsync(expectedMenuDetail);
            _mockService.Setup(service => service.GetMenuConfig()).ReturnsAsync(expectedMenuConfig);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.MENUDETAIL)).Returns(expectedUserRights);

     
            // Act
            var result = await _controller.Get(expectedMenuDetail.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var menuConfigValue = returnValue.GetType().GetProperty("MenuConfigId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(returnValue);
            Assert.Equal(expectedMenuDetail, recordValue);
            Assert.Equal(expectedMenuConfig, menuConfigValue);
            Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenMenuDetailIsNull() {
            // Arrange
            MenuDetailDTO? menuDetail = null;

            // Act
            var result = await _controller.Create(menuDetail);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<MenuDetailDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<MenuDetailDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenMenuDetailIsValid() {
            // Arrange
            var menuDetail = _fixture.Create<MenuDetailDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(menuDetail, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(menuDetail)).ReturnsAsync(menuDetail);

            // Act
            var result = await _controller.Create(menuDetail);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(menuDetail, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<MenuDetailDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<MenuDetailDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenMenuDetailIsValid() {
            // Arrange
            var menuDetail = _fixture.Create<MenuDetailDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(menuDetail, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(menuDetail)).ReturnsAsync(menuDetail);

            // Act
            var result = await _controller.Update(menuDetail);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var menuDetail = _fixture.Create<MenuDetailDTO>();
           _mockService.Setup(s => s.Delete(menuDetail.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(menuDetail.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}