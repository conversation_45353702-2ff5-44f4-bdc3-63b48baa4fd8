﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Base;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddMultitenancy(this IServiceCollection services, IConfiguration configuration)
            => services
                .AddScoped<ITenantProvider, TenantProvider>()
                .AddScoped<TenantSaveChangesInterceptor>();
    }
}
