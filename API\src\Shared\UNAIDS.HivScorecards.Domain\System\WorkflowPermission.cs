﻿using UNAIDS.HivScorecards.Domain.System;
using System.Text.Json;

namespace UNAIDS.HivScorecards.Domain
{
    public class WorkflowPermission: BaseEntity
    {
        public int WorkflowDefinitionId { get; set; }
        public virtual WorkflowDefinition? WorkflowDefinition { get; set; }
        public int RoleId { get; set; }
        public virtual Role? Role { get; set; }
        public JsonDocument? StepTransitions { get; set; }
        public void Dispose() => StepTransitions?.Dispose();
    }
}
