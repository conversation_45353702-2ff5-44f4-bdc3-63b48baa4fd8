﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace UNAIDS.Core.Shared
{
    public static class DependencyInjection
    {
        /// <summary>
        /// Add logger to the logging pipeline
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static WebApplicationBuilder AddLogger(this WebApplicationBuilder builder)
        {
            var logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration)
                .CreateLogger();

            // Clear providers 
            builder.Logging.ClearProviders();
            // Add the serilog provider
            builder.Logging.AddSerilog(logger);
            // Adds signleton service
            builder.Services.AddSingleton(logger);

            return builder;
        }

        public static IServiceCollection AddSwaggerConfig(this IServiceCollection services, IConfiguration configuration)
            => services
            .AddSwaggerConfiguration(configuration);

        /// <summary>
        /// Add core services to the DI container
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddCoreSharedServices(this IServiceCollection services, IConfiguration configuration)
            => services
                //.AddHealthCheck()
                .AddMemoryCaching()
                .AddAuthentication(configuration)
                .AddAuthorizationService()
                .AddEmailService(configuration);

        /// <summary>
        /// Adds Cache service to the DI container
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddMemoryCaching(this IServiceCollection services)
            => services
                .AddMemoryCache()
                .AddScoped<ICacheService, CacheService>();

        /// <summary>
        /// Adds healthcheck service to the DI container
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddHealthCheck(this IServiceCollection services)
        {
            services.AddHealthChecks()
                .AddCheck<MemoryHealthCheck>($"Memory Check", failureStatus: HealthStatus.Unhealthy, tags: new[] { "<API Name>" });
            //.AddSqlServer(configuration["ConnectionStrings:DefaultConnection"], healthQuery: "select 1", name: "SQL Server", failureStatus: HealthStatus.Unhealthy, tags: new[] { "Feedback", "Database" });

            services.AddHealthChecksUI(opt =>
            {
                opt.SetEvaluationTimeInSeconds(10);
                opt.MaximumHistoryEntriesPerEndpoint(60);
                opt.SetApiMaxActiveRequests(1);
                opt.AddHealthCheckEndpoint("Health status", "/health");
            }).AddInMemoryStorage();

            return services;
        }

        private static IServiceCollection AddAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var provider = configuration.GetSection("Jwt").Get<JwtProviderSettings>();

            services.AddAuthentication()
                .AddJwtBearer(provider.Name, options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = provider.Issuer,
                        ValidAudience = provider.Audience,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(provider.SecretKey))
                    };
                });

            return services;
        }

        #region add authorization services...
        /// <summary>
        /// Add authorization services
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddAuthorizationService(this IServiceCollection services)
        {
            services.AddAuthorization(authorize =>
            {
                authorize.AddPolicy("Bearer", new AuthorizationPolicyBuilder()
                    .AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme‌​)
                    .RequireAuthenticatedUser().Build());
                authorize.FallbackPolicy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .Build();
            });

            return services;
        }
        #endregion

        #region e-mail services...
        /// <summary>
        /// add email services
        /// </summary>
        /// <param name="services"></param>
        /// <param name="settings"></param>
        /// <returns></returns>
        public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)
        {
            // Get e-mail settings
            var emailConfig = new EmailConfig();
            configuration.GetSection("Email").Bind(emailConfig);

            services.AddSingleton(emailConfig);

            services.AddTransient<IEmailService, EmailService>();

            return services;
        }
        #endregion

        private static IServiceCollection AddSwaggerConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme."

                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                    {
                        {
                              new OpenApiSecurityScheme
                              {
                                  Reference = new OpenApiReference
                                  {
                                      Type = ReferenceType.SecurityScheme,
                                      Id = "Bearer"
                                  }
                              },
                             new string[] {}
                        }
                });
                c.OperationFilter<AddSwaggerHeaderParameter>();
            });

            return services;
        }
    }
}
