using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/workflow-step")]
    [ApiController]
    public class WorkflowStepController(IWorkflowStepService<WorkflowStepDTO> workflowStepService,IValidator<WorkflowStepDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list workflow steps
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWSTEP}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var returnData = await workflowStepService.GetAll(filter);

            return Ok(new
            {
                Record = returnData,
                SysSchemaId = await workflowStepService.GetSchemaDefinition(),
                CusSchemaId = await workflowStepService.GetSchemaDefinition(),
                WorkFlowServiceId = await workflowStepService.GetWorkflowServiceName(filter.Id),
                UserRights = workflowStepService.AccessRights(SubjectTypes.WORKFLOWSTEP)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get workflow step details
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWSTEP}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var workflowStep = await workflowStepService.GetById(id);
            return Ok(new
            {
                Record = workflowStep,
                SysSchemaId = await workflowStepService.GetSchemaDefinition(),
                CusSchemaId = await workflowStepService.GetSchemaDefinition(),
                WorkFlowServiceId = await workflowStepService.GetWorkflowServiceName(id),
                UserRights = workflowStepService.AccessRights(SubjectTypes.WORKFLOWSTEP)
            });
        }
        #endregion

        #region Post...
        /// <summary>
        /// Create a new workflow step
        /// </summary>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.WORKFLOWSTEP}.{ActionAliases.ADD}")]
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] WorkflowStepDTO workflowStep)
        {
            if (workflowStep == null)
                return BadRequest();

            var validationResult = await validator.ValidateAsync(workflowStep);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await workflowStepService.Create(workflowStep);
            return CreatedAtAction(nameof(Create), new { id = workflowStep.Id }, workflowStep);
        }
        #endregion

        #region Put...
        /// <summary>
        /// Update a workflow step
        /// </summary>
        /// <returns></returns> 
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
       [Authorize($"{SubjectTypes.WORKFLOWSTEP}.{ActionAliases.EDIT}")]
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] WorkflowStepDTO workflowStep)
        {
            var validationResult = await validator.ValidateAsync(workflowStep);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await workflowStepService.Update(workflowStep);
            return Ok(workflowStep);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a workflow step
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.WORKFLOWSTEP}.{ActionAliases.DELETE}")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await workflowStepService.Delete(id);
            return NoContent();
        }
        #endregion
    }
}