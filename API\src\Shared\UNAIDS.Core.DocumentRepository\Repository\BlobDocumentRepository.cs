﻿using AutoMapper;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Core.DocumentRepository
{
    public class BlobDocumentRepository(IUnitOfWork<CommonDbContext> unitOfWork, IMapper mapper, ISequenceRepository sequenceRepository, BlobServiceClient blobServiceClient, IConfiguration configuration)
        : BaseDocumentRepository(unitOfWork, mapper, sequenceRepository), IDocumentRepository
    {
        /// <summary>
        /// read the file
        /// </summary>
        /// <param name="documentStoreDetail"></param>
        /// <returns></returns>
        public override async Task<MemoryStream> ReadFile(DocumentStoreDetails documentStoreDetail)
        {
            var container = configuration.GetSection("Storage:Container").Value;
            var client = blobServiceClient.GetBlobContainerClient(container);

            var blob = client.GetBlobClient(documentStoreDetail.UploadedPath);
            var memory = new MemoryStream();
            await blob.DownloadToAsync(memory);
            memory.Position = 0;

            return memory;
        }

        /// <summary>
        /// Write file to the blob storage...
        /// </summary>
        /// <param name="file"></param>
        /// <param name="documentInfo"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public override async Task<DocumentStoreDetails> WriteFile(Stream file, DocumentRequestDto documentInfo, string fileName)
        {
            // #. get the blob client...
            var container = configuration.GetSection("Storage:Container").Value;
            var client = blobServiceClient.GetBlobContainerClient(container);

            var path = fileName;
            if (!string.IsNullOrEmpty(documentInfo.Folder))
                path = Path.Join(documentInfo.Folder, fileName);

            var blob = client.GetBlobClient(path);

            file.Position = 0;
            await blob.UploadAsync(file);

            var docDetail = mapper.Map<DocumentRequestDto, DocumentStoreDetails>(documentInfo);
            docDetail.UploadedName = fileName;
            docDetail.UploadedPath = path;

            return docDetail;
        }

        /// <summary>
        /// Delete the file
        /// </summary>
        /// <param name="documentStoreDetail"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public override async Task DeleteFile(DocumentStoreDetails documentStoreDetail)
        {
            // #. get the blob client...
            var container = configuration.GetSection("Storage:Container").Value;
            var client = blobServiceClient.GetBlobContainerClient(container);

            var blob = client.GetBlobClient(documentStoreDetail.UploadedPath);
            await blob.DeleteIfExistsAsync();
        }
    }
}
