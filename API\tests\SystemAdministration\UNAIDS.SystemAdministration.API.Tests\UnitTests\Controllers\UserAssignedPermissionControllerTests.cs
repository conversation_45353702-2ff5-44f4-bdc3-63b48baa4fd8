using Moq;
using AutoFixture;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Core.Base;
using UNAIDS.Authentication.Shared;
using UNAIDS.Authorization.Shared;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class UserAssignedPermissionControllerTests
    {
        private readonly Mock<IUserAssignedPermissionService<UserDTO>> _mockService;
        private readonly UserAssignedPermissionController _controller;
        private readonly Fixture _fixture;

        public UserAssignedPermissionControllerTests()
        {
            _mockService = new Mock<IUserAssignedPermissionService<UserDTO>>();
            _controller = new UserAssignedPermissionController(_mockService.Object);
            _fixture = new Fixture();
        }
  
        [Fact]
        public async Task GetUserAssignedPermission_ReturnsOkObjectResult_WithExpectedData()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedPermissions = _fixture.Create<List<CommonLookUp>>();
            var expectedBusinessUnits = _fixture.Create<List<CommonLookUp>>();
            var expectedUserNames = _fixture.Create<List<CommonLookUp>>();
            var expectedScreenName = _fixture.Create<List<CommonLookUp>>();
            var expectedModule = _fixture.Create<List<CommonLookUp>>();
            var expectedRole = _fixture.Create<List<CommonLookUp>>();
            var expectedUserRights = "Read,Write";

            _mockService.Setup(s => s.GetUserAssignedPermissions(filter)).ReturnsAsync(expectedPermissions);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.GetUserNames()).ReturnsAsync(expectedUserNames);
            _mockService.Setup(s => s.GetScreenName()).ReturnsAsync(expectedScreenName);
            _mockService.Setup(s => s.GetModule()).ReturnsAsync(expectedModule);
            _mockService.Setup(s => s.GetRole()).ReturnsAsync(expectedRole);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.USER)).Returns(expectedUserRights);

            // Act
            var result = await _controller.GetUserAssignedPermission(filter);

            // Assert
            _mockService.Verify(service => service.GetUserAssignedPermissions(filter), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);
            _mockService.Verify(service => service.GetUserNames(), Times.Once);
            _mockService.Verify(service => service.GetScreenName(), Times.Once);
            _mockService.Verify(service => service.GetModule(), Times.Once);
            _mockService.Verify(service => service.GetRole(), Times.Once);
            _mockService.Verify(service => service.AccessRights(SubjectTypes.USER), Times.Once);
       
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);            
            var businessUnitId = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);            
            var user = returnValue.GetType().GetProperty("UserId")?.GetValue(returnValue);
            var screeName = returnValue.GetType().GetProperty("ScreenName")?.GetValue(returnValue);
            var module = returnValue.GetType().GetProperty("Module")?.GetValue(returnValue);
            var role = returnValue.GetType().GetProperty("Role")?.GetValue(returnValue);
            var title = returnValue.GetType().GetProperty("Title")?.GetValue(returnValue);
            var userRights = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedPermissions, recordValue); 
            Assert.Equal(expectedBusinessUnits, businessUnitId); 
            Assert.Equal(expectedUserNames, user);
            Assert.Equal(expectedScreenName, screeName);
            Assert.Equal(expectedModule, module);
            Assert.Equal(expectedRole, role);
            Assert.Equal("", title);
            Assert.Equal(expectedUserRights, userRights);

        }

  
    }
}
