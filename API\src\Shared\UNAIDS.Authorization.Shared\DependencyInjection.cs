﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Authorization.Shared
{
    public static class DependencyInjection
    {
        /// <summary>
        /// Add application service to specified IServiceCollection
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddAuthorizationServices(this IServiceCollection services)
            => services
            .AddSingleton<IClaimCache, ClaimCache>()
            .AddSingleton<IAuthorizationPolicyProvider,  AuthorizationPolicyProvider>()
            .AddTransient<IClaimRepository, ClaimRepository>()
            .AddTransient<IClaimsTransformation, CustomClaimTransformation>();
    }
}
