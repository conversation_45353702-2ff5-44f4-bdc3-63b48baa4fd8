// using Moq;
// using FluentValidation;
// using UNAIDS.SystemAdministration.Application;
// using UNAIDS.SystemAdministration.Application.DTOs;
// using UNAIDS.SystemAdministration.API.Controllers;
// using Microsoft.AspNetCore.Mvc;
// using Xunit;
// using FluentValidation.Results;
// using Microsoft.AspNetCore.Http;
// using AutoFixture;
// using UNAIDS.Core.Base;


// namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
// {
//     // gives an error: GMS-Api\UNAIDS\API\tests\SystemAdministration\UNAIDS.SystemAdministration.API.Tests\UnitTests\Controllers\OBSSettingControllerTests.cs(19,26): error CS0246: The type or namespace name 'OBSSettingController' could not be found (are you missing a using directive or an assembly reference?) 
//     public class OBSSettingControllerTests {
//         private readonly Mock<IOBSSettingService> _mockService;
//         private readonly Mock<IValidator<OBSSettingDTO>> _mockValidator;
//         private readonly OBSSettingController _controller;
//         private readonly Fixture _fixture;

//         public OBSSettingControllerTests() {
//             //Initialize the mock services and controller
//             _mockService = new Mock<IOBSSettingService>();
//             _mockValidator = new Mock<IValidator<OBSSettingDTO>>();
//             _controller = new OBSSettingController(_mockService.Object, _mockValidator.Object);
//             _fixture = new Fixture();
//         }


//         [Fact]
//         public async Task GetAll_ReturnsOkResult_WithCorrectData() {
//             // Arrange
//             var filter = _fixture.Create<Filter>();
//             var expectedOBSSettings = _fixture.CreateMany<OBSSettingDTO>(3).ToList();

//             _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedOBSSettings);

//             // Act
//             var result = await _controller.GetAll(filter);

//             // Assert
//             var actionResult = Assert.IsType<OkObjectResult>(result); 
//             var returnValue = actionResult.Value; 

//             var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);

//             Assert.NotNull(returnValue);
//             Assert.Equal(expectedOBSSettings, recordValue);
//         }

//         [Fact]
//         public async Task Get_ReturnsNotFound_WithNullData() {
//             // Arrange
//             var id = null;

//             // Act
//             var result = await _controller.Get(id);

//             // Assert
//             Assert.IsType<NotFoundResult>(result);
//         }

//         [Fact]
//         public async Task Get_ReturnsOkResult_WithValidId() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();    

//             _mockService.Setup(service => service.GetById(obsSetting.Id)).ReturnsAsync(obeSetting);
     
//             // Act
//             var result = await _controller.Get(obsSetting.Id);

//             // Assert
//             var actionResult = Assert.IsType<OkObjectResult>(result); 
//             var returnValue = actionResult.Value; 

//             var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);

//             Assert.NotNull(returnValue);
//             Assert.Equal(obsSetting, recordValue);
//         }

//         [Fact]
//         public async Task Create_ReturnsBadRequest_WhenOBSSettingIsNull() {
//             // Arrange
//             MenuDetailDTO? menuDetail = null;

//             // Act
//             var result = await _controller.Create(menuDetail);

//             // Assert
//             Assert.IsType<BadRequestResult>(result);
//         }

//         [Fact]
//         public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
//             // Arrange
//             var invalidProgram = _fixture.Create<OBSSettingDTO>();
//             var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
//             validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
//             validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
//             var validationResult = new ValidationResult(validationFailures);
//             _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<OBSSettingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

//             // Act
//             var result = await _controller.Create(invalidProgram);

//             // Assert
//             Assert.IsType<BadRequestObjectResult>(result);
//         }

//         [Fact]
//         public async Task Create_ReturnsCreateAtAction_WhenOBSSettingIsValid() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();
//             var validationResult = new ValidationResult();
//             _mockValidator.Setup(v => v.ValidateAsync(obsSetting, default)).ReturnsAsync(validationResult);
//             _mockService.Setup(s => s.Create(obsSetting)).ReturnsAsync(obsSetting);

//             // Act
//             var result = await _controller.Create(obsSetting);

//             // Assert
//              var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
//             Assert.Equal(obsSetting, createdAtActionResult.Value);
//         }

//         [Fact]
//         public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
//             // Arrange
//             var invalidProgram = _fixture.Create<OBSSettingDTO>();
//             var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
//             validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
//             validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
//             var validationResult = new ValidationResult(validationFailures);
//             _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<OBSSettingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

//             // Act
//             var result = await _controller.Update(invalidProgram.Id,invalidProgram);

//             // Assert
//             Assert.IsType<BadRequestObjectResult>(result);

//         }

//         [Fact]
//         public async Task Update_ReturnsOkResult_WhenOBSSettingIsValid() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();
//             var validationResult = new ValidationResult();
//             _mockValidator.Setup(v => v.ValidateAsync(obsSetting, default)).ReturnsAsync(validationResult);
//             _mockService.Setup(s => s.Update(obsSetting.Id,obsSetting)).ReturnsAsync(obsSetting);

//             // Act
//             var result = await _controller.Update(obsSetting.Id,obsSetting);

//             // Assert
//             Assert.IsType<OkObjectResult>(result);
//         }

//         [Fact]
//         public async Task Update_ReturnsBadRequest_WhenIdIsEqual() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();
//             var validationResult = new ValidationResult();

//             // Act
//             var result = await _controller.Update(0,obsSetting);

//             // Assert
//             Assert.IsType<BadRequstResult>(result);
//         }

//         [Fact]
//         public async Task Update_ReturnsNotFound_WhenOBSSettingIsNotFound() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();
//             var validationResult = new ValidationResult();
//             _mockValidator.Setup(v => v.ValidateAsync(obsSetting, default)).ReturnsAsync(validationResult);
//             _mockService.Setup(s => s.Update(obsSetting.Id,obsSetting)).ReturnsAsync(null(OBSSettingDTO));

//             // Act
//             var result = await _controller.Update(obsSetting.Id,obsSetting);

//             // Assert
//             Assert.IsType<NotFoundResult>(result);
//         }

//         [Fact]
//         public async Task Delete_ReturnsNotFound_WhenIdIsZero() {
//             // Arrange
//             int id = 0;
//             _mockService.Setup(s => s.Delete(id)).ReturnsAsync(null(OBSSettingDTO));

//             // Act
//             var result = await _controller.Delete(id);

//             // Assert
//             Assert.IsType<NotFoundResult>(result);
//         }

//         [Fact]
//         public async Task Delete_ReturnsNoContent_WhenIdIsValid() {
//             // Arrange
//             var obsSetting = _fixture.Create<OBSSettingDTO>();
//            _mockService.Setup(s => s.Delete(obsSetting.Id)).Returns(Task.CompletedTask);

//             // Act
//             var result = await _controller.Delete(obsSetting.Id);

//             // Assert
//             Assert.IsType<OkResult>(result);
//         }

//     }
// }