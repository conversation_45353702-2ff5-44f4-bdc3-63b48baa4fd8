﻿using System.Text;

namespace UNAIDS.Core.Workflow
{
    public static class StringFunctions
    {
        public static string ConvertToActivityName(this string s)
        {
            StringBuilder sb = new StringBuilder();
            foreach (char c in s)
            {
                if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || c == '_')
                {
                    sb.Append(c);
                }
            }
            return $"{sb.ToString()}Activity";
        }
    }
}
