using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class RoleMenuMasterServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IRoleMenuMasterRepository> _mockRoleMenuMasterRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly RoleMenuMasterService _roleMenuMasterService;
        private readonly Fixture _fixture;

        public RoleMenuMasterServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockRoleMenuMasterRepository = new Mock<IRoleMenuMasterRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IRoleMenuMasterRepository>())
                           .Returns(_mockRoleMenuMasterRepository.Object);

            
            _roleMenuMasterService = new RoleMenuMasterService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfBusinessUnits()
        {
            // Arrange          
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockRoleMenuMasterRepository.Setup(repo => repo.GetBusinessUnits())
                                        .ReturnsAsync(expectedBusinessUnits);

            // Act
            var result = await _roleMenuMasterService.GetBusinessUnits();

            // Assert
            Assert.Equal(expectedBusinessUnits, result);
            _mockRoleMenuMasterRepository.Verify(repo => repo.GetBusinessUnits(), Times.Once); 
        }

        [Fact]
        public async Task GetRoles_ReturnsListOfRoles()
        {
            // Arrange          
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockRoleMenuMasterRepository.Setup(repo => repo.GetRoles())
                                        .ReturnsAsync(expectedRoles);

            // Act
            var result = await _roleMenuMasterService.GetRoles();

            // Assert
            Assert.Equal(expectedRoles, result);
            _mockRoleMenuMasterRepository.Verify(repo => repo.GetRoles(), Times.Once); 
        }

        [Fact]
        public async Task Getmodules_ReturnsListOfModules()
        {
            // Arrange          
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockRoleMenuMasterRepository.Setup(repo => repo.Getmodules())
                                        .ReturnsAsync(expectedModules);

            // Act
            var result = await _roleMenuMasterService.Getmodules();

            // Assert
            Assert.Equal(expectedModules, result);
            _mockRoleMenuMasterRepository.Verify(repo => repo.Getmodules(), Times.Once); 
        }

    }
}
