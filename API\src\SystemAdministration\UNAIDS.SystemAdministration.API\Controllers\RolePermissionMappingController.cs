using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/role-permission-mapping")]
    [ApiController]
    public class RolePermissionMappingController(IRolePermissionMappingService<RolePermissionMappingDTO> rolePermissionMappingService, IValidator<RolePermissionMappingDTO> validator) : ControllerBase
    {
        /// <summary>
        /// Get all rolePermissionMapping
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLEPERMISSIONMAPPING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var rolePermissionMapping = await rolePermissionMappingService.GetAll(filter);
            return Ok(new
            {
                Record = rolePermissionMapping,
                UserRights = rolePermissionMappingService.AccessRights(SubjectTypes.ROLEPERMISSIONMAPPING)
            });
        }

        /// <summary>
        /// Get a specific rolePermissionMapping by id
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLEPERMISSIONMAPPING}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var rolePermissionMapping = await rolePermissionMappingService.GetById(id);
            return Ok(new
            {
                Record = rolePermissionMapping,
                UserRights = rolePermissionMappingService.AccessRights(SubjectTypes.ROLEPERMISSIONMAPPING)
            });
        }

        /// <summary>
        /// Create a new rolePermissionMapping
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [Authorize($"{SubjectTypes.ROLEPERMISSIONMAPPING}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] RolePermissionMappingDTO rolePermissionMapping)
        {
            if (rolePermissionMapping == null)
                return BadRequest("rolePermissionMapping data is null");

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(rolePermissionMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var createdRolePermissionMapping = await rolePermissionMappingService.Create(rolePermissionMapping);
            return CreatedAtAction(nameof(Create), new { id = createdRolePermissionMapping.Id }, createdRolePermissionMapping);
        }

        /// <summary>
        /// Update an existing rolePermissionMapping
        /// </summary>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLEPERMISSIONMAPPING}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] RolePermissionMappingDTO rolePermissionMapping)
        {
            var validationResult = await validator.ValidateAsync(rolePermissionMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var updatedRolePermissionMapping = await rolePermissionMappingService.Update(rolePermissionMapping);
            return Ok(updatedRolePermissionMapping);
        }

        /// <summary>
        /// Delete a rolePermissionMapping by id
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLEPERMISSIONMAPPING}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await rolePermissionMappingService.Delete(id);
            return Ok();
        }

    }
}