﻿namespace UNAIDS.Core.Workflow
{
    public interface IWorkflowActivity
    {
        /// <summary>
        /// The type name of this activity
        /// </summary>
        string Type { get; }

        /// <summary>
        /// Executes the specified activity
        /// </summary>
        Task<ActivityExecutionResult> BeforeExecuteAsync(ActivityBeforeExecutionContext context);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<ActivityExecutionResult> AfterExecuteAsync(ActivityAfterExecutionContext context);
    }
}
