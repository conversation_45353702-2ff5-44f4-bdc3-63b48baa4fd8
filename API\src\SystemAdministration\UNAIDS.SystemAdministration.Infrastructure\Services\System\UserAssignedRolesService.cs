using AutoMapper;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class UserAssignedRolesService(
        IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
    ) : BaseAPIService<User, SystemAdminDbContext, UserDTO, IUserAssignedRolesRepository>(
            unitOfWork, mapper, sequenceRepository, userContext
        ),
        IUserAssignedRolesService<UserDTO>
    {
        public async Task<List<CommonLookUp>> GetUserAssignedRoles(Filter filter)
        {
            return await unitOfWork.Repository<IUserAssignedRolesRepository>().GetUserAssignedRoles(filter);
        }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await unitOfWork.Repository<IUserAssignedRolesRepository>().GetBusinessUnits();
        }

        public async Task<List<CommonLookUp>> GetUserNames()
        {
            return await unitOfWork.Repository<IUserAssignedRolesRepository>().GetUserNames();
        }

        public async Task<List<CommonLookUp>> GetLoginNames()
        {
            return await unitOfWork.Repository<IUserAssignedRolesRepository>().GetLoginNames();
        }
    }
}
