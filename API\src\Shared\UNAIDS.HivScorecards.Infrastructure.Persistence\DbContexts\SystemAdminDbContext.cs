﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Domain.TenantManagement;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class SystemAdminDbContext(DbContextOptions<SystemAdminDbContext> options,
        ITenantProvider tenantProvider,
        IUserContext userContext
        ) : DbContext(options)
    {
        public DbSet<User> Users { get; set; }
        public DbSet<UserGroups> UserGroups { get; set; }
        public DbSet<UserGroupMapping> UserGroupMappings { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<Menu> Menus { get; set; }
        public DbSet<MenuDetail> MenuDetails { get; set; }
        public DbSet<Module> Modules { get; set; }
        public DbSet<ModuleGroup> ModuleGroups { get; set; }
        public DbSet<StandardSchema> StandardSchemas { get; set; }
        public DbSet<Program> Programs { get; set; }
        public DbSet<ProgramStage> ProgramStages { get; set; }
        public DbSet<LookUpInfo> LookUpInfo { get; set; }
        public DbSet<LookUpType> LookUpTypes { get; set; }
        public DbSet<OBSSetting> OBSSettings { get; set; }
        public DbSet<BusinessUnit> BusinessUnits { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<RoleMenuMapping> RoleMenuMappings { get; set; }
        public DbSet<RoleMenuMaster> RoleMenuMasters { get; set; }
        public DbSet<RolePermissionMapping> RolePermissionMappings { get; set; }
        public DbSet<WorkflowDefinition> WorkflowDefinitions { get; set; }
        public DbSet<WorkflowStep> WorkflowSteps { get; set; }
        public DbSet<WorkflowTransition> WorkflowTransitions { get; set; }
        //public DbSet<WorkflowPermission> WorkflowPermissions { get; set; }
        public DbSet<WorkflowHistory> WorkflowHistories { get; set; }
        public DbSet<WorkflowReturnLog> WorkflowReturnLogs { get; set; }
        public DbSet<WorkflowInstance> WorkflowInstances { get; set; }
        public DbSet<AppEvent> AppEvents { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.RegisterModels();

            // add shadow properties
            builder.AddShadowProperties();

            // Applies the query filter globally to all entities derived from BaseEntity
            builder.ApplyGlobalFilters<BaseEntity>(e => !e.IsDeleted && (EF.Property<int?>(e, "TenantId") == tenantProvider.TenantId || EF.Property<int?>(e, "TenantId") == 0));

            // Make referential delete behaviour restrict instead of cascade for everything
            foreach (var relationship in builder.Model.GetEntityTypes()
                         .SelectMany(x => x.GetForeignKeys()))
            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;
            }

            base.OnModelCreating(builder);
        }
    }
}
