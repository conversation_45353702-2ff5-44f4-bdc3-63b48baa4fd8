using FluentValidation;
using System.Collections.Generic;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class RoleMenuMappingDTOValidator : AbstractValidator<RoleMenuMappingDTO>
    {
        public RoleMenuMappingDTOValidator()
        {
            // Validate MenuId: must be greater than 0
            RuleFor(r => r.MenuId)
                .GreaterThan(0).WithMessage("{PropertyName} must be a positive integer.");

            // Validate RoleMenuMasterId: can be null or must be greater than 0
            RuleFor(r => r.RoleMenuMasterId)
                .Must(value => value == null || value > 0)
                .WithMessage("{PropertyName} must be a positive integer or null.");

            // Validate IsSystemDefined: can be null or 0 or 1
            RuleFor(r => r.IsSystemDefined)
                .Must(value => value == null || (value >= 0 && value <= 1))
                .WithMessage("{PropertyName} must be either null or 0 (false) / 1 (true).");

            // Validate MenuAdd: can be null or must be greater than or equal to 0
            RuleFor(r => r.MenuAdd)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            // Repeat similar validation for other Menu fields (MenuModify, MenuCancel, etc.)
            RuleFor(r => r.MenuModify)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuCancel)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuView)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuPrint)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuRePrint)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuDelete)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuProcess)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuApprove)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuPreDatedEntry)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuImport)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuExport)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuValidation)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuCorrect)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuBulkImport)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuBulkUpdate)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuBulkDelete)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuExportRecord)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.RestrictedView)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuJSONEdit)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuSpecial1)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuSpecial2)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuSpecial3)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuSpecial4)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuSpecial5)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");

            RuleFor(r => r.MenuType)
                .Must(value => value == null || value >= 0)
                .WithMessage("{PropertyName} must be either null or a non-negative integer.");
        }
    }
}
