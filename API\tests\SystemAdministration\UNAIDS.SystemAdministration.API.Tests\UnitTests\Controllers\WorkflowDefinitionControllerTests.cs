using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class WorkflowDefinitionControllerTests {
        private readonly Mock<IWorkflowDefinitionService<WorkflowDefinitionDTO>> _mockService;
        private readonly Mock<IValidator<WorkflowDefinitionDTO>> _mockValidator;
        private readonly WorkflowDefinitionController _controller;
        private readonly Fixture _fixture;

        public WorkflowDefinitionControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IWorkflowDefinitionService<WorkflowDefinitionDTO>>();
            _mockValidator = new Mock<IValidator<WorkflowDefinitionDTO>>();
            _controller = new WorkflowDefinitionController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedReturnData = _fixture.CreateMany<WorkflowDefinitionDTO>(3).ToList();
            var expectedModuleGroups = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AEB";
            
            
            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetModuleGroups()).ReturnsAsync(expectedModuleGroups);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWDEFINITION));

            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var moduleGroups = returnValue.GetType().GetProperty("ModuleGroupId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedModuleGroups, moduleGroups);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }


        [Fact]
        public async Task Get_ReturnsBadRequest_WithZeroId() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedReturnData = _fixture.Create<WorkflowDefinitionDTO>();
            var expectedModuleGroups = _fixture.CreateMany<CommonLookUp>(2).ToList();
            var expectedUserRights = "AEB";  
       
            _mockService.Setup(service => service.GetById(expectedReturnData.Id)).ReturnsAsync(expectedReturnData);
            _mockService.Setup(service => service.GetModuleGroups()).ReturnsAsync(expectedModuleGroups);
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWDEFINITION));

                 
            // Act
            var result = await _controller.Get(expectedReturnData.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var moduleGroups = returnValue.GetType().GetProperty("ModuleGroupId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue);
            Assert.Equal(expectedModuleGroups, moduleGroups);
            // Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenWorkflowDefinitionIsNull() {
            // Arrange
            WorkflowDefinitionDTO? workflowDefinition = null;

            // Act
            var result = await _controller.Create(workflowDefinition);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowDefinitionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowDefinitionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenWorkflowDefinitionIsValid() {
            // Arrange
            var workflowDefinition = _fixture.Create<WorkflowDefinitionDTO>();
            var validationResult = new ValidationResult();

            _mockValidator.Setup(v => v.ValidateAsync(workflowDefinition, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(workflowDefinition)).ReturnsAsync(workflowDefinition);

            // Act
            var result = await _controller.Create(workflowDefinition);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(workflowDefinition, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowDefinitionDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowDefinitionDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenWorkflowDefinitionIsValid() {
            // Arrange
            var workflowDefinition = _fixture.Create<WorkflowDefinitionDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(workflowDefinition, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(workflowDefinition)).ReturnsAsync(workflowDefinition);

            // Act
            var result = await _controller.Update(workflowDefinition);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var workflowDefinition = _fixture.Create<WorkflowDefinitionDTO>();
           _mockService.Setup(s => s.Delete(workflowDefinition.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(workflowDefinition.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}
