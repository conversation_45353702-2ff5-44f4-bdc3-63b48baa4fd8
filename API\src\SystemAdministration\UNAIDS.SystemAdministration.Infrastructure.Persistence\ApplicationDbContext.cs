using Microsoft.EntityFrameworkCore;
using UNAIDS.SystemAdministration.Domain;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options)
    {
        public DbSet<Template> Templates { get; set; }
        public DbSet<ServiceComponent> ServiceComponents { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.HasDefaultSchema("pan_apps");

            base.OnModelCreating(builder);            
        }

    }
}
