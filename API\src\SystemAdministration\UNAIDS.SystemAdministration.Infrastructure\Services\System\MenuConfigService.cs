using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class MenuConfigService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<Menu, SystemAdminDbContext, MenuConfigDTO, IMenuConfigRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IMenuConfigService<MenuConfigDTO>
    {
         public async Task<List<MCHierarchyDTO>> GetMenuHierarchy()
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetMenuHierarchy();
        }
         /// <summary>
        /// Get MenuConfig
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetMenuConfig();
        }

         /// <summary>
        /// GetModule 
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetModule()
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetModule();
        }

         /// <summary>
        /// GetLookUp
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false)
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetLookUp(fieldName, false);
        }

         /// <summary>
        /// GetObjectTypes
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetObjectTypes(string fieldName)
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetObjectTypes(fieldName);
        } 
        
        /// <summary>
        /// GetMenuConfigDetails
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetMenuConfigDetails()
        {
            return await unitOfWork.Repository<IMenuConfigRepository>().GetMenuConfigDetails();
        }
    }
}