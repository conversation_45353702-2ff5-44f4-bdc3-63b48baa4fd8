﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace UNAIDS.Core.Base
{
    public abstract class BaseRepository<TEntity> : IBaseRepository<TEntity> where TEntity : class
    {
        protected readonly DbContext _dbContext;
        protected readonly DbSet<TEntity> _dbSet;

        public BaseRepository(DbContext dbContext)
        {
            _dbContext = dbContext;
            _dbSet = dbContext.Set<TEntity>();
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {

            await _dbSet.AddAsync(entity);

            return entity;
        }

        public virtual Task<TEntity> UpdateAsync(TEntity entity)
        {

            _dbSet.Update(entity);

            return Task.FromResult(entity);
        }

        public virtual async Task<IEnumerable<TEntity>> AddManyAsync(IEnumerable<TEntity> entities)
        {
            await _dbSet.AddRangeAsync(entities);
            return entities;
        }

        public virtual Task DeleteAsync(int id)
        {
            dynamic entity = _dbSet.Find(id);

            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            entity.IsDeleted = true;

            _dbSet.Update(entity);

            return Task.CompletedTask;
        }


        public virtual Task DeleteAsync(TEntity entity)
        {
            _dbSet.Remove(entity);

            return Task.CompletedTask;
        }

        public virtual Task DeleteManyAsync(Expression<Func<TEntity, bool>> filter)
        {
            var entities = _dbSet.Where(filter);

            _dbSet.RemoveRange(entities);

            return Task.CompletedTask;
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            return await _dbSet.ToListAsync();
        }

        public virtual async Task<TEntity> GetByIdAsync(int id)
        {
            return await _dbSet.FindAsync(id);
        }

        public virtual async Task<IEnumerable<TEntity>> GetManyAsync(
            Expression<Func<TEntity, bool>>? filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? orderBy = null,
            int? top = null,
            int? skip = null,
            params string[] includeProperties)
        {
            IQueryable<TEntity> query = _dbSet;

            if (filter != null)
            {
                query = query.Where(filter);
            }

            if (includeProperties.Length > 0)
            {
                query = includeProperties.Aggregate(query, (theQuery, theInclude) => theQuery.Include(theInclude));
            }

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            if (skip.HasValue)
            {
                query = query.Skip(skip.Value);
            }

            if (top.HasValue)
            {
                query = query.Take(top.Value);
            }

            return await query.ToListAsync();
        }

        public string GetTableName()
        {
            var entityName = _dbContext.Model.FindEntityType(typeof(TEntity))?.GetAnnotation("Relational:TableName");

            return entityName?.Value.ToString() ?? "";
        }
    }
}
