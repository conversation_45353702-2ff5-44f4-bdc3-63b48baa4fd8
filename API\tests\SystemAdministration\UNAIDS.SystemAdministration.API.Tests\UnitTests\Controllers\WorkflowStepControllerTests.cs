using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Authorization.Shared;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class WorkflowStepControllerTests {
        private readonly Mock<IWorkflowStepService<WorkflowStepDTO>> _mockService;
        private readonly Mock<IValidator<WorkflowStepDTO>> _mockValidator;
        private readonly WorkflowStepController _controller;
        private readonly Fixture _fixture;

        public WorkflowStepControllerTests() {
            //Initialize the mock services and controller
            _mockService = new Mock<IWorkflowStepService<WorkflowStepDTO>>();
            _mockValidator = new Mock<IValidator<WorkflowStepDTO>>();
            _controller = new WorkflowStepController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();

        }


        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData() {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedReturnData = _fixture.CreateMany<WorkflowStepDTO>(3).ToList();
            var expectedSysSchema = _fixture.CreateMany<CommonLookUp>().ToList();
            var expectedWorkflowServiceName = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedUserRights = "AEB";
            
            _mockService.Setup(service => service.GetAll(filter)).ReturnsAsync(expectedReturnData); 
            _mockService.Setup(service => service.GetSchemaDefinition()).ReturnsAsync(expectedSysSchema); 
            _mockService.Setup(service => service.GetWorkflowServiceName(filter.Id)).ReturnsAsync(expectedWorkflowServiceName); 
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWSTEP)).Returns(expectedUserRights); 

             // Act
            var result = await _controller.GetAll(filter); 

            // Assert
            _mockService.Verify(service => service.GetAll(filter), Times.Once);
            _mockService.Verify(service => service.GetSchemaDefinition(), Times.Exactly(2));
            _mockService.Verify(service => service.GetWorkflowServiceName(filter.Id), Times.Once);
            _mockService.Verify(service => service.AccessRights(SubjectTypes.WORKFLOWSTEP), Times.Once);


            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var sysSchemaId = returnValue.GetType().GetProperty("SysSchemaId")?.GetValue(returnValue);
            var workFlowServiceId = returnValue.GetType().GetProperty("WorkFlowServiceId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue); 
            Assert.Equal(expectedSysSchema, sysSchemaId);
            Assert.Equal(expectedWorkflowServiceName, workFlowServiceId); 
            // Assert.Equal(expectedUserRights, userRightsValue); 
        }


        [Fact]
        public async Task Get_ReturnsNotFound_WhenIsIsZero() {
            // Arrange
            var id=0;

            // Act
            var result = await _controller.Get(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithValidId() {
            // Arrange
            var expectedReturnData = _fixture.Create<WorkflowStepDTO>();
            var expectedSysSchema = _fixture.CreateMany<CommonLookUp>().ToList();
            var expectedWorkflowServiceName = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedUserRights = "AEB";  
       
            _mockService.Setup(service => service.GetById(expectedReturnData.Id)).ReturnsAsync(expectedReturnData); 
            _mockService.Setup(service => service.GetSchemaDefinition()).ReturnsAsync(expectedSysSchema); 
            _mockService.Setup(service => service.GetWorkflowServiceName(expectedReturnData.Id)).ReturnsAsync(expectedWorkflowServiceName); 
            _mockService.Setup(service => service.AccessRights(SubjectTypes.WORKFLOWSTEP)).Returns(expectedUserRights); 
            
            // Act
            var result = await _controller.Get(expectedReturnData.Id);

            // Assert
             _mockService.Verify(service => service.GetById(expectedReturnData.Id), Times.Once);
            _mockService.Verify(service => service.GetSchemaDefinition(), Times.Exactly(2));
            _mockService.Verify(service => service.GetWorkflowServiceName(expectedReturnData.Id), Times.Once);
            _mockService.Verify(service => service.AccessRights(SubjectTypes.WORKFLOWSTEP), Times.Once);

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var sysSchemaId = returnValue.GetType().GetProperty("SysSchemaId")?.GetValue(returnValue);
            var workFlowServiceId = returnValue.GetType().GetProperty("WorkFlowServiceId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedReturnData, recordValue); 
            Assert.Equal(expectedSysSchema, sysSchemaId);
            Assert.Equal(expectedWorkflowServiceName, workFlowServiceId); 
            // Assert.Equal(expectedUserRights, userRightsValue);
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenWorkflowStepIsNull() {
            // Arrange
            WorkflowStepDTO? workflowStep = null;

            // Act
            var result = await _controller.Create(workflowStep);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowStepDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowStepDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreateAtAction_WhenWorkflowStepIsValid() {
            // Arrange
            var workflowStep = _fixture.Create<WorkflowStepDTO>();
            var validationResult = new ValidationResult();

            _mockValidator.Setup(v => v.ValidateAsync(workflowStep, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(workflowStep)).ReturnsAsync(workflowStep);

            // Act
            var result = await _controller.Create(workflowStep);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(workflowStep, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<WorkflowStepDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<WorkflowStepDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenWorkflowStepIsValid() {
            // Arrange
            var workflowStep = _fixture.Create<WorkflowStepDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(workflowStep, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(workflowStep)).ReturnsAsync(workflowStep);

            // Act
            var result = await _controller.Update(workflowStep);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero() {
            // Arrange
            int id = 0;

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid() {
            // Arrange
            var workflowStep = _fixture.Create<WorkflowStepDTO>();
           _mockService.Setup(s => s.Delete(workflowStep.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(workflowStep.Id);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

       
    }
}
