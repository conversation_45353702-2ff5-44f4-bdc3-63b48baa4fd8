﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Core.DocumentRepository
{
    public sealed class DocumentStoreDetailRepository(CommonDbContext dbContext): BaseRepository<DocumentStoreDetails>(dbContext), IDocumentStoreDetailRepository
    {
        public async Task<Boolean> AnyDocsAsync(int storeId)
        {
            return await dbContext.DocumentStoreDetails.AsNoTracking().AnyAsync(x => x.DocumentStoreId == storeId);
        }
    }
}
