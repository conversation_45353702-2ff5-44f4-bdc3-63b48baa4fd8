using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class PermissionServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IPermissionRepository> _mockPermissionRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly PermissionService _permissionService;
        private readonly Fixture _fixture;

        public PermissionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockPermissionRepository = new Mock<IPermissionRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IPermissionRepository>())
                           .Returns(_mockPermissionRepository.Object);

            
            _permissionService = new PermissionService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfBusinessUnits()
        {
            // Arrange          
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockPermissionRepository.Setup(repo => repo.GetBusinessUnits())
                                        .ReturnsAsync(expectedBusinessUnits);

            // Act
            var result = await _permissionService.GetBusinessUnits();

            // Assert
            Assert.Equal(expectedBusinessUnits, result);

            _mockPermissionRepository.Verify(repo => repo.GetBusinessUnits(), Times.Once); 
        }

        [Fact]
        public async Task GetRoles_ReturnsListOfRoles()
        {
            // Arrange          
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockPermissionRepository.Setup(repo => repo.GetRoles())
                                        .ReturnsAsync(expectedRoles);

            // Act
            var result = await _permissionService.GetRoles();

            // Assert
            Assert.Equal(expectedRoles, result);

            _mockPermissionRepository.Verify(repo => repo.GetRoles(), Times.Once); 
        }

        [Fact]
        public async Task GetModules_ReturnsListOfModules()
        {
            // Arrange          
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();

            _mockPermissionRepository.Setup(repo => repo.GetModules())
                                        .ReturnsAsync(expectedModules);

            // Act
            var result = await _permissionService.GetModules();

            // Assert
            Assert.Equal(expectedModules, result);

            _mockPermissionRepository.Verify(repo => repo.GetModules(), Times.Once); 
        }

    }
}
