﻿using AutoFixture;
using NSubstitute;

namespace UNAIDS.SystemAdministration.Application.Tests
{
    public class ProgramServiceTest
    {
        #region variables....
        /// <summary>
        /// variable declaration
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;
        private readonly IProgramRepository _programRepository;
        private readonly IProgramSetupRepository _programSetupRepository;
        private readonly IProgramStageRepository _programStageRepository;
        private readonly IMapper _mapper;
        private readonly Fixture _fixture;
        private readonly ProgramService _programService;
        #endregion

        #region constructor...
        /// <summary>
        /// Constructor function
        /// </summary>
        public ProgramServiceTest()
        {
            _unitOfWork = Substitute.For<IUnitOfWork>();
            _programRepository = Substitute.For<IProgramRepository>();
            _programSetupRepository = Substitute.For<IProgramSetupRepository>();
            _programStageRepository = Substitute.For<IProgramStageRepository>();
            _mapper = Substitute.For<IMapper>();
            _unitOfWork.ProgramRepository.Returns(_programRepository);
            _unitOfWork.ProgramSetupRepository.Returns(_programSetupRepository);
            _unitOfWork.ProgramStageRepository.Returns(_programStageRepository);
            _fixture = new Fixture();
            _programService = new ProgramService(_unitOfWork, _mapper);
        }
        #endregion

        #region list action tests...
        /// <summary>
        /// List action tests - return list of programs
        /// </summary>
        [Fact]
        public async void List_Programs_ReturnListOfPrograms()
        {
            // arrange
            var programs = _fixture.CreateMany<Program>(5);
            var programResponseDtos = _fixture.CreateMany<ProgramListResponseDto>(5);

            // setup
            _programRepository.GetAllAsync().Returns(Task.FromResult(programs));
            _mapper.Map<List<ProgramListResponseDto>>(programs).Returns(programResponseDtos.ToList());

            // act 
            var result = await _programService.GetAll();

            // assert
            Assert.Equal(5, result.Count);
        }
        #endregion

        #region onboard program tests...
        /// <summary>
        /// onboard the program and return onboarded program
        /// </summary>
        [Fact]
        public async void Onboard_Program_ReturnSuccess()
        {
            // arrange
            var programDto = new ProgramOnboardDto()
            {
                Name = "Test",
                Code = "TST",
                ProgramSetup = new ProgramSetupDto()
                {
                    Features = new List<ProgramFeature>()
                    {
                        new ProgramFeature() {
                            Id = 1,
                            Code = "TMG",
                            DisplayName = "Test Module Group",
                            Type = "Module Group",
                            Items = new List<ProgramFeature>()
                            {
                                new ProgramFeature()
                                {
                                    Id = 1,
                                    Code = "TMD",
                                    DisplayName = "Test Module",
                                    Type = "Module",
                                    Items = new List<ProgramFeature>()
                                    {
                                        new ProgramFeature()
                                        {
                                            Id = 1,
                                            Code = "TMN",
                                            DisplayName = "Test Menu",
                                            Type = "Menu"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                ProgramStages = new List<ProgramStageDto>() { new ProgramStageDto() {
                    Name = "Test Stage",
                    SortOrder = 1,
                    StageMenus =  new int[]{1, 2}
                } }
            };
            var program = new Program() { Name = "Test", Code = "TST" };
            var programSetup = new ProgramSetup()
            {
                Features = new List<ProgramFeature>()
                {
                    new ProgramFeature() {
                        Id = 1,
                        Code = "TMG",
                        DisplayName = "Test Module Group",
                        Type = "Module Group",
                        Items = new List<ProgramFeature>()
                        {
                            new ProgramFeature()
                            {
                                Id = 1,
                                Code = "TMD",
                                DisplayName = "Test Module",
                                Type = "Module",
                                Items = new List<ProgramFeature>()
                                {
                                    new ProgramFeature()
                                    {
                                        Id = 1,
                                        Code = "TMN",
                                        DisplayName = "Test Menu",
                                        Type = "Menu"
                                    }
                                }
                            }
                        }
                    }
                }
            };
            var programStages = new List<ProgramStage>() { new ProgramStage() {
                    Name = "Test Stage",
                    SortOrder = 1,
                    StageMenus =  new int[]{1, 2}
                } };

            // setup
            _programRepository.AddAsync(Arg.Any<Program>()).Returns(Task.FromResult(program));
            _programStageRepository.AddManyAsync(Arg.Any<List<ProgramStage>>()).Returns(Task.FromResult(programStages.AsEnumerable()));
            _programSetupRepository.AddAsync(Arg.Any<ProgramSetup>()).Returns(Task.FromResult(programSetup));

            _mapper.Map<Program>(programDto).Returns(program);
            _mapper.Map<ProgramSetup>(programDto.ProgramSetup).Returns(programSetup);
            _mapper.Map<List<ProgramStage>>(programDto.ProgramStages).Returns(programStages);
            _mapper.Map<ProgramOnboardDto>(Arg.Any<Program>()).Returns(programDto);

            var result = await _programService.OnBoardProgram(programDto);

            Assert.IsType<ProgramOnboardDto>(result);
        }

        #endregion
    }
}
