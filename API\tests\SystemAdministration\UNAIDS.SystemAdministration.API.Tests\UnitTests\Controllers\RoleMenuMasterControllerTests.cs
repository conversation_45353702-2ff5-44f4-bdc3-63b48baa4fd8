﻿using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;
using UNAIDS.Authorization.Shared;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class RoleMenuMasterControllerTests
    {
        private readonly Mock<IRoleMenuMasterService<RoleMenuMasterDTO>> _mockService;
        private readonly Mock<IValidator<RoleMenuMasterDTO>> _mockValidator;
        private readonly RoleMenuMasterController _controller;
        private readonly Fixture _fixture;

        public RoleMenuMasterControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<IRoleMenuMasterService<RoleMenuMasterDTO>>();
            _mockValidator = new Mock<IValidator<RoleMenuMasterDTO>>();
            _controller = new RoleMenuMasterController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsRoleMenuMasters_WithValidFilter()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedData = _fixture.CreateMany<RoleMenuMasterDTO>(5).ToList();
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList(); 

            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(expectedData);
            _mockService.Setup(s => s.GetRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.Getmodules()).ReturnsAsync(expectedModules);
          
            // Act
            var result = await _controller.GetAll(filter);

            // Assert
            _mockService.Verify(service => service.GetAll(filter), Times.Once);
            _mockService.Verify(service => service.GetRoles(), Times.Once);
            _mockService.Verify(service => service.Getmodules(), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);        
            _mockService.Verify(service => service.AccessRights(SubjectTypes.ROLE), Times.Once);

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            
            var roles = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var modules = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnits = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedData, recordValue); 
            Assert.Equal(expectedRoles, roles);
            Assert.Equal(expectedModules, modules); 
            Assert.Equal(expectedBusinessUnits, businessUnits); 
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsInvalid()
        {
            //Arrange
            var tempId = 0;   

            //Act
            var result = await _controller.Get(tempId);

            //Assert
            var okResult = Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOKObjectResult_WhenIdIsValid()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList(); 
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList(); 

            _mockService.Setup(s => s.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockService.Setup(s => s.GetRoles()).ReturnsAsync(expectedRoles);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.Getmodules()).ReturnsAsync(expectedModules);
           
            // Act
            var result = await _controller.Get(expectedData.Id);

            //Assert
            _mockService.Verify(service => service.GetById(expectedData.Id), Times.Once);
            _mockService.Verify(service => service.GetRoles(), Times.Once);
            _mockService.Verify(service => service.Getmodules(), Times.Once);
            _mockService.Verify(service => service.GetBusinessUnits(), Times.Once);        
            _mockService.Verify(service => service.AccessRights(SubjectTypes.ROLE), Times.Once);

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            
            var roles = returnValue.GetType().GetProperty("RoleId")?.GetValue(returnValue);
            var modules = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);
            var businessUnits = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var userRightsValue = returnValue.GetType().GetProperty("UserRights")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedData, recordValue); 
            Assert.Equal(expectedRoles, roles);
            Assert.Equal(expectedModules, modules); 
            Assert.Equal(expectedBusinessUnits, businessUnits); 

        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenDataIsNull()
        {

            //Act
            var result = await _controller.Create(null);

            //Assert
            var okResult = Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()
        {

            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Create(expectedData);

            //Asert 
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenSettingIsValid()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), default)).ReturnsAsync(new ValidationResult());
            _mockService.Setup(service => service.Create(expectedData)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.Create(expectedData);

            //Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.True(createdAtActionResult.StatusCode == 201);

        }


        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockService.Setup(service => service.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Update(expectedData);

            //Asert 
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsNoContent_WhenUpdateIsSuccessful()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();

            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<RoleMenuMasterDTO>(), default)).ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update(expectedData);

            // Assert
            Assert.IsType<OkResult>(result);
        }


        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsInvalid()
        {
            // Arrange
            int tempId = 0;   

            // Act
            var result = await _controller.Delete(tempId);

            // Assert
            var okResult= Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Delete_ReturnsOk_WhenDeleteIsSuccessful()
        {
            // Arrange
            var expectedData = _fixture.Create<RoleMenuMasterDTO>();
            _mockService.Setup(s => s.Delete(expectedData.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(expectedData.Id);

            // Assert
            Assert.IsType<OkResult>(result);

        }
    }
}
