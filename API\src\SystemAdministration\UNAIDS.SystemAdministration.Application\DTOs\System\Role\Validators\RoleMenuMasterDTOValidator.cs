using FluentValidation;
using System.Collections.Generic;
using System.Linq;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class RoleMenuMasterDTOValidator : AbstractValidator<RoleMenuMasterDTO>
    {
        public RoleMenuMasterDTOValidator()
        {
            // Validate RoleId: must be greater than 0
            RuleFor(r => r.RoleId)
                .GreaterThan(0).WithMessage("{PropertyName} must be a positive integer.");

            // Validate ModuleId: must be greater than 0
            RuleFor(r => r.ModuleId)
                .GreaterThan(0).WithMessage("{PropertyName} must be a positive integer.");

            // Validate IsSystemDefined: can be null or 0 or 1
            RuleFor(r => r.IsSystemDefined)
                .Must(value => value == null || (value >= 0 && value <= 1))
                .WithMessage("{PropertyName} must be either null or 0 (false) / 1 (true).");

            // Validate EqualRoleLevelFlag: must be true or false (this can be omitted if not required)
            RuleFor(r => r.EqualRoleLevelFlag)
                .NotNull().WithMessage("{PropertyName} must be specified.");

        }
    }
}
