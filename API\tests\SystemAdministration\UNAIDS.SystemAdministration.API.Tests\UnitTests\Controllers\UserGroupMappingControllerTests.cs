using Moq;
using AutoFixture;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.API.Controllers;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class UserGroupMappingControllerTests
    {
        private readonly Mock<IUserGroupMappingService<UserGroupMappingDTO>> _mockService;
        private readonly Mock<IValidator<UserGroupMappingDTO>> _mockValidator;
        private readonly UserGroupMappingController _controller;
        private readonly Fixture _fixture;

        public UserGroupMappingControllerTests()
        {
            _mockService = new Mock<IUserGroupMappingService<UserGroupMappingDTO>>();
            _mockValidator = new Mock<IValidator<UserGroupMappingDTO>>();
            _controller = new UserGroupMappingController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var expectedUserGroupMappings = _fixture.Create<List<UserGroupMappingDTO>>();
            var expectedUser = _fixture.Create<List<CommonLookUp>>();

            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(expectedUserGroupMappings);
            _mockService.Setup(s => s.GetUser()).ReturnsAsync(expectedUser);


            // Act
            var result = await _controller.GetAll(filter);

            // Assert
           _mockService.Verify(service => service.GetAll(filter), Times.Once);
            _mockService.Verify(service => service.GetUser(), Times.Once);
       
            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);            
            var user = returnValue.GetType().GetProperty("UserId")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedUserGroupMappings, recordValue); 
            Assert.Equal(expectedUser, user);
        }

        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {
            // Act
            var result = await _controller.Get(0);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WhenDataIsFound()
        {
            // Arrange
            var expectedUserGroupMapping = _fixture.Create<UserGroupMappingDTO>();

            _mockService.Setup(s => s.GetById(expectedUserGroupMapping.Id)).ReturnsAsync(expectedUserGroupMapping);

            // Act
            var result = await _controller.Get(expectedUserGroupMapping.Id);

            // Assert
            _mockService.Verify(s=>s.GetById(expectedUserGroupMapping.Id), Times.Once);

            var actionResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = actionResult.Value;

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);

            Assert.NotNull(actionResult);
            Assert.Equal(expectedUserGroupMapping, recordValue); 

        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenInputIsNull()
        {
            // Act
            var result = await _controller.Create(null);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenInputIsInvalid()
        {
            // Arrange
            var userGroupMapping = _fixture.Create<UserGroupMappingDTO>();
            var validationFailures = new List<ValidationFailure> { new ValidationFailure("Property", "Error message") };
            _mockValidator.Setup(v => v.ValidateAsync(userGroupMapping, default))
                .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(userGroupMapping);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedResult_WhenInputIsValid()
        {
            // Arrange
            var userGroupMapping = _fixture.Create<UserGroupMappingDTO>();
            _mockValidator.Setup(v => v.ValidateAsync(userGroupMapping, default))
                .ReturnsAsync(new ValidationResult());
            _mockService.Setup(s => s.Create(userGroupMapping)).ReturnsAsync(userGroupMapping);

            // Act
            var result = await _controller.Create(userGroupMapping) as CreatedAtActionResult;

            // Assert
            Assert.IsType<CreatedAtActionResult>(result);
        }

         [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
            // Arrange
            var expectedData = _fixture.Create<UserGroupMappingDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Code";
                failure.ErrorMessage = "The Code is required.";
            }
            _mockService.Setup(service => service.GetById(expectedData.Id)).ReturnsAsync(expectedData);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupMappingDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act
            var result = await _controller.Update(expectedData);

            //Asert 
            Assert.IsType<BadRequestObjectResult>(result);

        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenInputIsValid()
        {
            // Arrange
            var userGroupMapping = _fixture.Create<UserGroupMappingDTO>();
       
            _mockService.Setup(s => s.Update(userGroupMapping)).ReturnsAsync(userGroupMapping); 
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<UserGroupMappingDTO>(), default)).ReturnsAsync(new ValidationResult());

            // Act
            var result = await _controller.Update(userGroupMapping);

            // Assert
            _mockService.Verify(s=>s.Update(userGroupMapping), Times.Once);

            Assert.IsType<OkObjectResult>(result);

        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero()
        {
            // Act
            var result = await _controller.Delete(0);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid()
        {
            // Arrange
            int id = 1;
            _mockService.Setup(s=>s.Delete(id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            _mockService.Verify(s=>s.Delete(id), Times.Once);
            Assert.IsType<OkResult>(result);
        }
    }
}
