using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/obssettings")]
    [ApiController]
    public class OBSSettingController(IOBSSettingService _service, IValidator<OBSSettingDTO> _validator) : ControllerBase
    {
        /// <summary>
        /// Get all OBS Settings
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAll()
        {
            var data = await _service.GetAll();
            return Ok(new
            {
                Record = data
            });
        }

        /// <summary>
        /// Get a specific OBS Setting by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetById(int id)
        {
            var data = await _service.GetById(id);
            if (data == null)
                return NotFound();

            return Ok(data);
        }

        /// <summary>
        /// Create a new OBS Setting
        /// </summary>
        /// <param name="obsSetting"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        public async Task<IActionResult> Create([FromBody] OBSSettingDTO obsSetting)
        {
            if (obsSetting == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await _validator.ValidateAsync(obsSetting);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await _service.Create(obsSetting);
            return CreatedAtAction(nameof(Create), new { id = obsSetting.Id }, result);
        }

        /// <summary>
        /// Update an OBS Setting
        /// </summary>
        /// <param name="id"></param>
        /// <param name="obsSetting"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Update(int id, [FromBody] OBSSettingDTO obsSetting)
        {
            if (id != obsSetting.Id)
                return BadRequest();

            var exists = await _service.GetById(id);
            if (exists == null)
                return NotFound();

            var validationResult = await _validator.ValidateAsync(obsSetting);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await _service.Update(id, obsSetting);
            return NoContent();
        }

        /// <summary>
        /// Delete an OBS Setting
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Delete(int id)
        {
            var exists = await _service.GetById(id);
            if (exists == null)
                return NotFound();

            await _service.Delete(id);
            return NoContent();
        }
    }
}
