using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class RoleRepository(SystemAdminDbContext dbContext) : BaseRepository<Role>(dbContext), IRoleRepository
    {
        public async Task<List<CommonLookUp>> GetCurrentStep(int id)
        {
            return await dbContext.Modules.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }


    }
}
