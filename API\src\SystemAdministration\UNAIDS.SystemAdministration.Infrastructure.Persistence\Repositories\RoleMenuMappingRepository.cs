using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Core.Shared;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class RoleMenuMappingRepository(SystemAdminDbContext dbContext) : BaseRepository<RoleMenuMapping>(dbContext), IRoleMenuMappingRepository
    {
        public async Task<List<RoleMenuMappingDTO>> GetRoleMenus(int rolemenuId)
        {
            var rmMaster = dbContext.RoleMenuMasters.AsNoTracking().FirstOrDefault(x => x.Id == rolemenuId);

            var menus = await dbContext.Menus.AsNoTracking()
                .Where(x => x.ModuleId == rmMaster.ModuleId
                && x.ControllerType == "component"
                && x.ObjectType != (int)AppConstants.MenuType.Container)
                .Select(x => new {
                    x.Id,
                    x.Title,
                    x.ParentMenuId,
                    x.SortOrder
                }).ToListAsync();

            var mappings = await dbContext.RoleMenuMappings.AsNoTracking()
                .Where(x => x.RoleMenuMasterId == rmMaster.Id)
                .ToListAsync();

            var menuIds = menus.Select(x => x.Id).ToList();

            var parentMenus = menus.Where(x => !menuIds.Contains(x.ParentMenuId ?? 0)).OrderBy(x => x.SortOrder).ToList();
            List<RoleMenuMappingDTO> moduleMenus = new();
            foreach(var parent in parentMenus)
            {
                var mapping = mappings.Where(x => x.MenuId == parent.Id).FirstOrDefault();

                if(mapping != null)
                {
                    moduleMenus.Add(new RoleMenuMappingDTO() {
                        Id = mapping.Id,
                        MenuName = parent.Title,
                        RoleMenuMasterId = rmMaster.Id,
                        IsSystemDefined = mapping.IsSystemDefined,
                        MenuAdd = mapping.MenuAdd,
                        MenuApprove = mapping.MenuApprove,
                        MenuBulkDelete = mapping.MenuBulkDelete,
                        MenuBulkImport = mapping.MenuBulkImport,
                        MenuBulkUpdate = mapping.MenuBulkUpdate,
                        MenuCancel = mapping.MenuCancel,
                        MenuCorrect = mapping.MenuCorrect,
                        MenuDelete= mapping.MenuDelete,
                        MenuImport= mapping.MenuImport,
                        MenuExport= mapping.MenuExport,
                        MenuExportRecord= mapping.MenuExportRecord,
                        MenuId = parent.Id,
                        MenuModify = mapping.MenuModify,
                        MenuView = mapping.MenuView,
                        RestrictedView = mapping.RestrictedView
                    });
                } 
                else
                {
                    moduleMenus.Add(new RoleMenuMappingDTO()
                    {
                        MenuName = parent.Title,
                        RoleMenuMasterId = rmMaster.Id,
                        MenuAdd = 0,
                        MenuApprove = 0,
                        MenuBulkDelete = 0,
                        MenuBulkImport = 0,
                        MenuBulkUpdate = 0,
                        MenuCancel = 0,
                        MenuCorrect = 0,
                        MenuDelete = 0,
                        MenuImport = 0,
                        MenuExport = 0,
                        MenuExportRecord = 0,
                        MenuId = parent.Id,
                        MenuModify = 0,
                        MenuView = 0,
                        RestrictedView = 0
                    });
                }

                var childMenus = menus.Where(x => x.ParentMenuId == parent.Id).OrderBy(x => x.SortOrder).ToList();

                foreach (var childMenu in childMenus)
                {
                    var childMapping = mappings.Where(x => x.MenuId == childMenu.Id).FirstOrDefault();

                    if (childMapping != null)
                    {
                        moduleMenus.Add(new RoleMenuMappingDTO()
                        {
                            Id = childMapping.Id,
                            MenuName = $"   {childMenu.Title}",
                            RoleMenuMasterId = rmMaster.Id,
                            IsSystemDefined = childMapping.IsSystemDefined,
                            MenuAdd = childMapping.MenuAdd,
                            MenuApprove = childMapping.MenuApprove,
                            MenuBulkDelete = childMapping.MenuBulkDelete,
                            MenuBulkImport = childMapping.MenuBulkImport,
                            MenuBulkUpdate = childMapping.MenuBulkUpdate,
                            MenuCancel = childMapping.MenuCancel,
                            MenuCorrect = childMapping.MenuCorrect,
                            MenuDelete = childMapping.MenuDelete,
                            MenuImport = childMapping.MenuImport,
                            MenuExport = childMapping.MenuExport,
                            MenuExportRecord = childMapping.MenuExportRecord,
                            MenuId = childMenu.Id,
                            MenuModify = childMapping.MenuModify,
                            MenuView = childMapping.MenuView,
                            RestrictedView = childMapping.RestrictedView
                        });
                    }
                    else
                    {
                        moduleMenus.Add(new RoleMenuMappingDTO()
                        {
                            MenuName = $"   {childMenu.Title}",
                            RoleMenuMasterId = rmMaster.Id,
                            MenuAdd = 0,
                            MenuApprove = 0,
                            MenuBulkDelete = 0,
                            MenuBulkImport = 0,
                            MenuBulkUpdate = 0,
                            MenuCancel = 0,
                            MenuCorrect = 0,
                            MenuDelete = 0,
                            MenuImport = 0,
                            MenuExport = 0,
                            MenuExportRecord = 0,
                            MenuId = childMenu.Id,
                            MenuModify = 0,
                            MenuView = 0,
                            RestrictedView = 0
                        });
                    }
                }
            }

            return moduleMenus;
        }

        /// <summary>
        /// Get list of lookups - dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false)
        {
            return await dbContext.LookUpInfo.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Name,
                    Active = 1
                }).ToListAsync();
        }


        /// <summary>
        /// Get list of menu configurations - dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await dbContext.Menus.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    Active = 1
                }).ToListAsync();
        }

        /// <summary>
        /// Save permission settings - dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> SavePermission()
        {
            // Implement save permission logic if applicable, for now returning empty list
            return await Task.FromResult(new List<CommonLookUp>());
        }

        public async Task<List<RoleMenuMappingDTO>> GetExistingPermissions(int roleMenuMasterId)
        {
            return await dbContext.RoleMenuMappings.AsNoTracking()
                .Where(x => x.RoleMenuMasterId == roleMenuMasterId)
                .Select(x => new RoleMenuMappingDTO()
                {
                    MenuId = x.MenuId,
                    Id = x.Id
                }).ToListAsync();

        }
    }
}
