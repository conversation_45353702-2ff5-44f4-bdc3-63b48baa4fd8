﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using System.Data;

namespace UNAIDS.Core.DataAccess.Dynamic
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDynamicDataAccess(this IServiceCollection services, IConfiguration configuration)
        => services
                .AddDynamicDataServices(configuration);

        public static IServiceCollection AddDynamicDataServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Enable snake_case to PascalCase mapping
            Dapper.DefaultTypeMap.MatchNamesWithUnderscores = true;

            services.AddSingleton<IDbConnection>(sp =>
                new NpgsqlConnection(configuration.GetConnectionString("Default")));

            return services;
        }

    }
}
