using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using Microsoft.AspNetCore.Http;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IUserPermissionRepository : IBaseRepository<UserPermission>
    {
        Task<List<UserPermission>> GetPermissions(int userId);
        Task<List<CommonLookUp>> GetUser();
        Task<List<CommonLookUp>> GetRole();
        Task<List<CommonLookUp>> GetBusinessUnits();
    }
}
