﻿
using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.Core.DocumentRepository
{
    public class DocumentStoreDetailDto: BaseEntity
    {
        public int DocumentStoreId { get; set; }

        public string? Title { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string? UploadedName { get; set; }
        public string? UploadedPath { get; set; }
        public DateTime? UploadedDate { get; set; }
        public string? Remarks { get; set; }
        public int? FileSize { get; set; }
        public int? UserId { get; set; }
    }
}
