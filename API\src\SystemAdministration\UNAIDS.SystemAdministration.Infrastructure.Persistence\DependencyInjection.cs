﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public static class DependencyInjection
    {
        /// <summary>
        /// Add DbContext to the DI container
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration configuration)
            => services
                .AddPostgreSQL(configuration)
                .AddScoped(typeof(IUnitOfWork<>), typeof(UnitOfWork<>))
                .AddTransient(typeof(ISequenceRepository), typeof(SequenceRepository))
                .AddTransient(typeof(ICommonRepository), typeof(CommonRepository))
                .AddTransient(typeof(IBusinessUnitRepository), typeof(BusinessUnitRepository))
                .AddTransient(typeof(ILookUpInfoRepository), typeof(LookUpInfoRepository))
                .AddTransient(typeof(ILookupTypeRepository), typeof(LookUpTypeRepository))
                .AddTransient(typeof(IMenuConfigRepository), typeof(MenuConfigRepository))
                .AddTransient(typeof(IMenuDetailRepository), typeof(MenuDetailRepository))
                .AddTransient(typeof(IPermissionRepository), typeof(PermissionRepository))
                .AddTransient(typeof(IProgramRepository), typeof(ProgramRepository))
                .AddTransient(typeof(ITenantRepository), typeof(TenantRepository))
                .AddTransient(typeof(IRoleMenuMappingRepository), typeof(RoleMenuMappingRepository))
                .AddTransient(typeof(IRoleMenuMasterRepository), typeof(RoleMenuMasterRepository))
                .AddTransient(typeof(IRolePermissionMappingRepository), typeof(RolePermissionMappingRepository))
                .AddTransient(typeof(IRoleRepository), typeof(RoleRepository))
                .AddTransient(typeof(ISettingRepository), typeof(SettingRepository))
                .AddTransient(typeof(IUserGroupMappingRepository), typeof(UserGroupMappingRepository))
                .AddTransient(typeof(IUserGroupsRepository), typeof(UserGroupsRepository))
                .AddTransient(typeof(IUserPermissionRepository), typeof(UserPermissionRepository))
                .AddTransient(typeof(IWorkflowDefinitionRepository), typeof(WorkflowDefinitionRepository))
                .AddTransient(typeof(IWorkflowPermissionRepository), typeof(WorkflowPermissionRepository))
                .AddTransient(typeof(IUserRepository), typeof(UserRepository))
                .AddTransient(typeof(IWorkflowStepRepository), typeof(WorkflowStepRepository));

        private static IServiceCollection AddPostgreSQL(this IServiceCollection services, IConfiguration configuration)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

            // In a PostgreSQL database, when using .NET 8 and EF Core 8, you need to enable EnableDynamicJson
            // to be able to store and manipulate dynamic JSON data in "jsonb" columns.
            var dataSourceBuilder = new NpgsqlDataSourceBuilder(configuration.GetConnectionString("Default"))
                .EnableDynamicJson()
                .Build();

            // context for db migration...
            services.AddDbContext<MigrationDbContext>((serviceProvider,  options) =>
            {
                options.UseNpgsql(dataSourceBuilder,
                    b => b.MigrationsAssembly(typeof(MigrationDbContext).Assembly.FullName)
                ).UseSnakeCaseNamingConvention();
            }
            );

            services.AddDbContext<TenantManagementDbContext>(options => options.UseNpgsql(dataSourceBuilder,
                        b => b.MigrationsAssembly(typeof(TenantManagementDbContext).Assembly.FullName)
                    ).UseSnakeCaseNamingConvention());

            services.AddDbContext<CommonDbContext>(options => options.UseNpgsql(dataSourceBuilder,
                        b => b.MigrationsAssembly(typeof(CommonDbContext).Assembly.FullName)
                    ).UseSnakeCaseNamingConvention());

            return services
                .AddDbContext<SystemAdminDbContext>((serviceProvider, options) =>
                {
                    options.UseNpgsql(dataSourceBuilder,
                        b => b.MigrationsAssembly(typeof(SystemAdminDbContext).Assembly.FullName)
                    ).UseSnakeCaseNamingConvention();

                    // This ensures EF calls our SaveChangesInterceptor
                    var interceptor = serviceProvider.GetRequiredService<TenantSaveChangesInterceptor>();
                    options.AddInterceptors(interceptor);
                }                    
                );
        }
    }
}
