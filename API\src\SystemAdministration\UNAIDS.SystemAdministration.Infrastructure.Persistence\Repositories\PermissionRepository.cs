using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class PermissionRepository(SystemAdminDbContext dbContext) : BaseRepository<RoleMenuMaster>(dbContext), IPermissionRepository
    {
        /// <summary>
        /// Get list of business unit - dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp() { 
                    Value = x.Id,
                    DisplayText = x.Name,
                    Active = x.Active }).ToListAsync();
        }

        /// <summary>
        /// Get list of roles - dropdown options
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetRoles()
        {
            return await dbContext.Roles.AsNoTracking()
               .Select(x => new CommonLookUp()
               {
                   Value = x.Id,
                   DisplayText = x.Name,
                   Active = 1
               }).ToListAsync();
        }

        /// <summary>
        /// Get list of modules
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetModules()
        {
            return await dbContext.Modules.AsNoTracking()
               .Select(x => new CommonLookUp()
               {
                   Value = x.Id,
                   DisplayText = x.Name,
                   Active = 1
               }).ToListAsync();
        }
    }
}