﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/programs")]
    [ApiController]
    public class ProgramsController(IProgramService _service, IValidator<ProgramOnboardDTO> _validator) : ControllerBase
    {
        /// <summary>
        /// Get all indicators
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAll()
        {
            var data = await _service.GetAll();

            return Ok(new
            {
                Record = data.Value
            });
        }

        /// <summary>
        /// Get all indicators
        /// </summary>
        /// <returns></returns>
        [HttpGet("options")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetOptions()
        {

            return Ok(new
            {
                Modules = await _service.GetModules(),
                Roles = await _service.GetRoles(),
                Models = await _service.GetModels(),
                DataElements = await _service.GetDataElements(),
                Menus = await _service.GetMenus()
            });
        }


        /// <summary>
        /// Onboard new Program
        /// </summary>
        /// <param name="permission"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        public async Task<IActionResult> Create([FromBody] ProgramOnboardDTO program)
        {
            if (program == null)
                return BadRequest();

            // Asynchronously validate the model
            var validationResult = await _validator.ValidateAsync(program);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await _service.OnBoardProgram(program);

            return CreatedAtAction(nameof(Create), new { id = program.Id }, result);
        }
    }
}
