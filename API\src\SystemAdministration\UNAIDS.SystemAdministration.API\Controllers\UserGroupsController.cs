using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-groups")]
    [ApiController]
    public class UserGroupsController(IUserGroupsService<UserGroupsDTO> userGroupsService,IValidator<UserGroupsDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list user groups
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPS}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var userGroups = await userGroupsService.GetAll(filter);
            return Ok(new
            {
                Record = userGroups,
                UserRights = userGroupsService.AccessRights(SubjectTypes.USERGROUPS)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get the user group by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPS}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var userGroup = await userGroupsService.GetById(id);
            return Ok(new
            {
                Record = userGroup,
                UserRights = userGroupsService.AccessRights(SubjectTypes.USERGROUPS)
            });
        }
        #endregion

        /// <summary>
        /// Create a new User Group
        /// </summary>
        /// <param name="userGroup"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.USERGROUPS}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] UserGroupsDTO userGroup)
        {
            if (userGroup == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(userGroup);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }


            var result = await userGroupsService.Create(userGroup);
            return CreatedAtAction(nameof(Create), new { id = userGroup.Id }, result);
        }

        /// <summary>
        /// Update a user group
        /// </summary>
        /// <param name="userGroup"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.USERGROUPS}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] UserGroupsDTO userGroup)
        {
            var validationResult = await validator.ValidateAsync(userGroup);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await userGroupsService.Update(userGroup);
            return Ok(userGroup);
        }

        /// <summary>
        /// Delete user group
        /// </summary>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERGROUPS}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await userGroupsService.Delete(id);
            return Ok();
        }
    }
}
