using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class WorkflowDefinitionRepository(SystemAdminDbContext dbContext) : BaseRepository<WorkflowDefinition>(dbContext), IWorkflowDefinitionRepository
    {
        public async Task<List<CommonLookUp>> GetModuleGroups()
        {
            return await dbContext.ModuleGroups.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
                
        }
    }
}