using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IUserAssignedPermissionService<T> : IBaseAPIService<T> where T : class
    {
        Task<List<CommonLookUp>> GetUserAssignedPermissions(Filter filter);
        Task<List<CommonLookUp>> GetBusinessUnits();
        Task<List<CommonLookUp>> GetUserNames();
        Task<List<CommonLookUp>> GetScreenName();
        Task<List<CommonLookUp>> GetModule();
        Task<List<CommonLookUp>> GetRole();
    }
}
