﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;

namespace UNAIDS.Core.Taxonomy
{
    [Route("api/taxonomy")]
    [ApiController]
    public class TaxonomyController(ITaxonomyService service, ITenantProvider tenantProvider) : ControllerBase
    {
        /// <summary>
        /// Get models
        /// </summary>
        /// <returns></returns>
        [HttpGet("models")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetModels()
        {
            var data = await service.GetModels();
            return Ok(data);
        }
        /// <summary>
        /// Get all conepts
        /// </summary>
        /// <returns></returns>
        [HttpGet("concepts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAll()
        {
            var data = await service.GetConcepts();
            return Ok(data);
        }


        [HttpGet("concept/{slug}/data-elements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetConceptElements(string slug)
        {
            var data = await service.GetConceptElements(slug);
            return Ok(data);
        }

        [HttpGet("model/{model}/data-elements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetModelElements(string model)
        {
            var data = await service.GetModelElements(model);
            return Ok(data);
        }

        [HttpGet("data-elements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDataElements()
        {
            var data = await service.GetDataElements();
            return Ok(data);
        }
    }
}
