using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class UserGroupsDTOValidator : AbstractValidator<UserGroupsDTO>
    {
        public UserGroupsDTOValidator()
        {
            // Code: Required and max length of 50
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("The Code is required.")
                .MaximumLength(50).WithMessage("The Code must be at most 50 characters long.");

            // Name: Required and max length of 50
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("The Name is required.")
                .MaximumLength(50).WithMessage("The Name must be at most 50 characters long.");

            // Email: must be a valid email format and max length of 50
            RuleFor(x => x.Email)
                .EmailAddress().WithMessage("Enter a valid email address.")
                .MaximumLength(50).WithMessage("The Email must be at most 50 characters long.")
                .When(l => !string.IsNullOrEmpty(l.<PERSON><PERSON>));

        }
    }
}
