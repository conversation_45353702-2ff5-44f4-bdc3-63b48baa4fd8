using FluentValidation;

namespace UNAIDS.SystemAdministration.Application
{
    public class BusinessUnitDTOValidator : AbstractValidator<BusinessUnitDTO>
    {
        public BusinessUnitDTOValidator()
        {
            // Validate Code: cannot be null or empty
            RuleFor(bu => bu.Code)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(100).WithMessage("{PropertyName} must not exceed 100 characters.");

            // Validate Name: cannot be null or empty
            RuleFor(bu => bu.Name)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(200).WithMessage("{PropertyName} must not exceed 200 characters.");

            // Validate Address: optional but if provided, must not exceed 300 characters
            RuleFor(bu => bu.Address)
                .MaximumLength(300).WithMessage("{PropertyName} must not exceed 300 characters.");

            // Validate PinCode: optional but if provided, must be a valid format
            RuleFor(bu => bu.PinCode)
                .Matches(@"^\d{5}(-\d{4})?$").WithMessage("{PropertyName} must be a valid postal code format.");

            // Validate Phone: optional but if provided, must be a valid format
            RuleFor(bu => bu.Phone)
                .Matches(@"^\+?\d{10,15}$").WithMessage("{PropertyName} must be a valid phone number format.");

            // Validate Fax: optional but if provided, must be a valid format
            RuleFor(bu => bu.Fax)
                .Matches(@"^\+?\d{10,15}$").WithMessage("{PropertyName} must be a valid fax number format.");

            // Validate Email: optional but if provided, must be a valid email format
            RuleFor(bu => bu.Email)
                .EmailAddress().WithMessage("{PropertyName} must be a valid email address format.");

            // Validate WebSite: optional but if provided, must be a valid URL format
            RuleFor(bu => bu.WebSite)
                .Matches(@"^(http|https)://").WithMessage("{PropertyName} must be a valid URL format.");

            // Validate Logo: optional but if provided, must not exceed 500 characters
            RuleFor(bu => bu.Logo)
                .MaximumLength(500).WithMessage("{PropertyName} must not exceed 500 characters.");

            // Optionally, you can add more validations for other fields if needed
        }
    }
}
