﻿namespace UNAIDS.Core.Workflow
{
    public class WorkflowData
    {
        public string? CurrentStepId { get; set; }
        public List<ChildStep>? ChildSteps { get; set; }
    }

    public class ChildStep
    {
        public int WorkflowStatusId { get; set; }
        public string? StepId { get; set; }
        public string? Description { get; set; }
        public int? ChildWorkflowId { get; set; }
        public int? StepStatusId { get; set; }
        public int? IsCompleted { get; set; }
        public int? IsReturned { get; set; }

    }
}
