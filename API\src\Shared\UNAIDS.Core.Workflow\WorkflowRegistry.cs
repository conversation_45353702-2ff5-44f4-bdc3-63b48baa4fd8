﻿using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Core.Base;
using UNAIDS.Core.Workflow.Models;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.Core.Workflow
{
    public class WorkflowRegistry(
        IServiceProvider serviceProvider,
        IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IWorkflowExecutor workflowExecutor,
        IMapper mapper,
        IWorkflowUtils workflowUtils
        ) : IWorkflowRegistry
    {
        /// <summary>
        /// Register workflow and starts initial execution
        /// </summary>
        /// <param name="workflowDefinitionName">Workflow definition name</param>
        /// <param name="userId"></param>
        /// <param name="variables"></param>
        /// <returns>Returns workflow executor result</returns>
        public async Task<WorkflowExecutorResult> StartWorkflow(string workflowDefinitionName, int userId, Variables variables)
        {
            // Gets workflow definition by name
            var workflowDefinition = await unitOfWork.Repository<IWorkflowRepository>().GetWorkflowByName(workflowDefinitionName);

            if (workflowDefinition == null)
                throw new NotFoundException("The workflow definition cannot be found");

            // Build step transitions
            var initialStepId = await unitOfWork.Repository<IWorkflowRepository>().GetInitialStepId(workflowDefinition.Id);

            // Creates workflow instance
            var workflowInstance = new WorkflowInstanceDTO()
            {
                WorkflowInstanceId = Guid.NewGuid(),
                WorkflowDefinitionId = workflowDefinition.Id,
                CorrelationId = Guid.NewGuid(),
                IsInitialStep = 1,
                IsCompleted = 0,
                ExecutionStatus = (int)WorkflowStatus.Complete,
                CreatedOn = DateTime.UtcNow,
                CreatedById = userId
            };

            var executorResult = await workflowExecutor.Execute(workflowInstance, initialStepId, userId, variables);

            if (executorResult.Errors.Count > 0)
            {
                workflowInstance.WorkflowInstanceId = null;
            }

            return executorResult;
        }

        /// <summary>
        /// Register workflow and starts initial execution
        /// </summary>
        /// <param name="workflowId">workflow defintion id</param>
        /// <param name="userId">user id</param>
        /// <param name="variables">variables</param>
        /// <returns></returns>
        public async Task<WorkflowExecutorResult> StartWorkflow(int workflowId, int userId, Variables variables)
        {
            // Build step transitions
            var initialStepId = await unitOfWork.Repository<IWorkflowRepository>().GetInitialStepId(workflowId);

            // Creates workflow instance
            var workflowInstance = new WorkflowInstanceDTO()
            {
                WorkflowInstanceId = Guid.NewGuid(),
                WorkflowDefinitionId = workflowId,
                CorrelationId = Guid.NewGuid(),
                IsInitialStep = 1,
                IsCompleted = 0,
                ExecutionStatus = (int)WorkflowStatus.Complete,
                CreatedOn = DateTime.UtcNow,
                CreatedById = userId
            };

            var executorResult = await workflowExecutor.Execute(workflowInstance, initialStepId, userId, variables);

            if (executorResult.Errors.Count > 0)
            {
                workflowInstance.WorkflowInstanceId = null;
            }

            return executorResult;
        }

        /// <summary>
        /// Register workflow and starts initial execution
        /// </summary>
        /// <param name="workflowDefinitionName">Workflow definition name</param>
        /// <param name="userId"></param>
        /// <returns>Returns workflow executor result</returns>
        public async Task<WorkflowExecutorResult> StartWorkflow(string workflowDefinitionName, int userId)
        {
            return await StartWorkflow(workflowDefinitionName, userId, new Variables());
        }

        /// <summary>
        /// Trigger workflow transition
        /// </summary>
        /// <param name="workflowInstanceId">Workflow instance Id</param>
        /// <param name="stepId">Transition to which step</param>
        /// <param name="userId"></param>
        /// <param name="variables"></param>
        /// <returns>Returns workflow executor result</returns>
        public async Task<WorkflowExecutorResult> TriggerWorkflow(Guid workflowInstanceId, int stepId, int userId, Variables variables)
        {
            // Gets workflow instance id
            var workflowInstance = await unitOfWork.Repository<IWorkflowRepository>().GetWorkflowInstance(workflowInstanceId);

            if (workflowInstance == null)
                throw new NotFoundException("The workflow instance cannot be found");

            workflowInstance.IsInitialStep = 0;

            var objInstance = mapper.Map<WorkflowInstanceDTO>(workflowInstance);

            var workflowExecutorResult = await workflowExecutor.Execute(objInstance, stepId, userId, variables);

            return workflowExecutorResult;
        }

        /// <summary>
        /// Pause the workflow and unlock the record to edit
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<WorkflowExecutorResult> Return(Guid workflowInstanceId, int userId, Variables? variables)
        {
            var result = new WorkflowExecutorResult();

            var repo = unitOfWork.Repository<IWorkflowRepository>();

            // Gets workflow instance id
            var workflowInstance = await repo.GetWorkflowInstance(workflowInstanceId);

            var objInstance = mapper.Map<WorkflowInstanceDTO>(workflowInstance);

            IWorkflowActivity activityInstance = null;

            if (workflowInstance != null)
            {
                var workflowDefinition = await repo.GetWorkflowName(workflowInstance.WorkflowDefinitionId);
                var activityName = $"{workflowDefinition}Return".ConvertToActivityName();
                var services = serviceProvider.GetServices<IWorkflowActivity>();

                if (services != null)
                    activityInstance = services.FirstOrDefault(x => x.Type == activityName);

                // #. execute return pre activities
                if (activityInstance != null)
                {
                    var beforeActivityResp = await activityInstance.BeforeExecuteAsync(new ActivityBeforeExecutionContext(serviceProvider, objInstance, new WorkflowPermissionMapping(), new Variables()));

                    if (beforeActivityResp.Errors.Count() > 0)
                    {
                        result.Errors.AddRange(beforeActivityResp.Errors);
                        return result;
                    }
                }

                workflowInstance.IsReturned = 1;
                workflowInstance.RevisionNo = (workflowInstance.RevisionNo ?? 0) + 1;
                await repo.UpdateAsync(workflowInstance);

                var returnLog = new WorkflowReturnLog()
                {
                    WorkflowInstanceId = workflowInstanceId,
                    ReturnStatus = 1,
                    RevisionNo = workflowInstance.RevisionNo,
                    CurrentStepId = workflowInstance.CurrentStepId,
                    Comment = (string)(variables?.Get("comment") ?? ""),
                    IsDeleted = false,
                    CreatedById = userId,
                    CreatedAt = DateTime.UtcNow
                };

                await unitOfWork.Repository<IWorkflowReturnLogRepository>().AddAsync(returnLog);

                await unitOfWork.SaveChangesAsync();

                // #. execute return post activities
                if (activityInstance != null)
                {
                    var afterActivityResp = await activityInstance.AfterExecuteAsync(new ActivityAfterExecutionContext(serviceProvider, mapper.Map<WorkflowInstanceDTO>(objInstance), new WorkflowPermissionMapping(), new Variables()));
                    if (afterActivityResp.Errors.Count() > 0)
                    {
                        result.Errors.AddRange(afterActivityResp.Errors); ;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// resubmit the returned record for edit
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<WorkflowExecutorResult> ReSubmit(Guid workflowInstanceId, int userId, Variables? variables)
        {
            var result = new WorkflowExecutorResult();

            var repo = unitOfWork.Repository<IWorkflowRepository>();

            // Gets workflow instance id
            var workflowInstance = await repo.GetWorkflowInstance(workflowInstanceId);
            var objInstance = mapper.Map<WorkflowInstanceDTO>(workflowInstance);

            IWorkflowActivity activityInstance = null;

            if (workflowInstance != null)
            {
                var workflowDefinition = await repo.GetWorkflowName(workflowInstance.WorkflowDefinitionId);
                var activityName = $"{workflowDefinition}ReSubmit".ConvertToActivityName();
                var services = serviceProvider.GetServices<IWorkflowActivity>();

                if (services != null)
                    activityInstance = services.FirstOrDefault(x => x.Type == activityName);

                // #. execute resubmit pre activities
                if (activityInstance != null)
                {
                    var beforeActivityResp = await activityInstance.BeforeExecuteAsync(new ActivityBeforeExecutionContext(serviceProvider, objInstance, new WorkflowPermissionMapping(), new Variables()));
                    if (beforeActivityResp.Errors.Count() > 0)
                    {
                        result.Errors.AddRange(beforeActivityResp.Errors);
                        return result;
                    }
                }

                workflowInstance.IsReturned = 0;
                await repo.UpdateAsync(workflowInstance);

                var returnLog = new WorkflowReturnLog()
                {
                    WorkflowInstanceId = workflowInstanceId,
                    ReturnStatus = 0,
                    RevisionNo = workflowInstance.RevisionNo,
                    CurrentStepId = workflowInstance.CurrentStepId,
                    Comment = (string)(variables?.Get("comment") ?? ""),
                    IsDeleted = false,
                    CreatedById = userId,
                    CreatedAt = DateTime.UtcNow
                };

                await unitOfWork.Repository<IWorkflowReturnLogRepository>().AddAsync(returnLog);

                await unitOfWork.SaveChangesAsync();

                // #. execute resubmit post activities
                if (activityInstance != null)
                {
                    var afterActivityResp = await activityInstance.AfterExecuteAsync(new ActivityAfterExecutionContext(serviceProvider, mapper.Map<WorkflowInstanceDTO>(objInstance), new WorkflowPermissionMapping(), new Variables()));
                    if (afterActivityResp.Errors.Count() > 0)
                    {
                        result.Errors.AddRange(afterActivityResp.Errors); ;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// Trigger workflow transition
        /// </summary>
        /// <param name="workflowInstanceId">Workflow instance Id</param>
        /// <param name="stepId">Transition to which step</param>
        /// <param name="userId"></param>
        /// <returns>Returns workflow executor result</returns>
        public async Task<WorkflowExecutorResult> TriggerWorkflow(Guid workflowInstanceId, int stepId, int userId)
        {
            return await TriggerWorkflow(workflowInstanceId, stepId, userId, new Variables());
        }


        /// <summary>
        /// Get workflow steps
        /// </summary>
        /// <param name="workflowName"></param>
        /// <returns></returns>
        public async Task<object> GetWorkflowSteps(string workflowName)
        {
            return await unitOfWork.Repository<IWorkflowRepository>().GetWorkflowSteps(workflowName);
        }

        /// <summary>
        /// Get workflow history with next action item
        /// </summary>
        /// <param name="workflowInstanceId"></param>
        /// <param name="controller"></param>
        /// <param name="userId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<WorkflowJourney> GetHistoryWithActionItem(Guid workflowInstanceId, int userId, string language)
        {
            var workflowJourney = await workflowUtils.GetHistoryWithActionItem(workflowInstanceId, userId, language);

            return workflowJourney;
        }
    }
}
