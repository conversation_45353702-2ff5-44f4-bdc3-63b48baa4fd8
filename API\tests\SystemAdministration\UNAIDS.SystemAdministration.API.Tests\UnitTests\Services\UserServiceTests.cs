using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.Authentication.Shared;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class UserServiceTests
    {
        private readonly Mock<IUnitOfWork<CommonDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IUserManager> _mockUserManager;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private Mock<IUserContext> _mockUserContext;
        private Mock<ICommonRepository> _mockCommonRepository;
        private readonly UserService _userService;
        private readonly Fixture _fixture;

        public UserServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<CommonDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockUserManager = new Mock<IUserManager>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockCommonRepository = new Mock<ICommonRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IUserRepository>())
                           .Returns(_mockUserRepository.Object);

            
            _userService = new UserService(
                    _mockUnitOfWork.Object,
                    _mockMapper.Object,
                    _mockSequenceRepository.Object,
                    _mockUserManager.Object,
                    _mockUserContext.Object,
                    _mockCommonRepository.Object
                );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsListOfUsers()
        {
            // Arrange   
            var filter = _fixture.Create<Filter>();
            var expectedUsers = _fixture.CreateMany<UserDTO>(1).ToList();
           
            _mockUserManager.Setup(um => um.GetAll()).ReturnsAsync(expectedUsers);

            // Act
            var result = await _userService.GetAll(filter);

            // Assert
            _mockUserManager.Verify(um => um.GetAll(), Times.Once);

            Assert.Equal(expectedUsers, result);
        }

        [Fact]
        public async Task GetById_ReturnsUser()
        {
            // Arrange   
            var filter = _fixture.Create<Filter>();
            var expectedUser = _fixture.Create<UserDTO>();
           
            _mockUserManager.Setup(um => um.GetById(filter.Id)).ReturnsAsync(expectedUser);

            // Act
            var result = await _userService.GetById(filter.Id);

            // Assert
            _mockUserManager.Verify(um => um.GetById(filter.Id), Times.Once);

            Assert.Equal(expectedUser, result);
        }

        [Fact]
        public async Task Create_ReturnsUser()
        {
            // Arrange   
            var expectedUser = _fixture.Create<UserDTO>();
           
            _mockUserManager.Setup(um => um.AddUser(expectedUser)).ReturnsAsync(expectedUser);

            // Act
            var result = await _userService.Create(expectedUser);

            // Assert
            _mockUserManager.Verify(um => um.AddUser(expectedUser), Times.Once);

            Assert.Equal(expectedUser, result);
        }

        [Fact]
        public async Task GetSearchable_ReturnsListOfSearchables()
        {
            // Arrange   
            var id = _fixture.Create<int>();
            var fieldName = _fixture.Create<string>();
            var expectedSearchables = _fixture.CreateMany<CommonLookUp>(1).ToList();
           
            _mockUnitOfWork.Setup(uow => uow.Repository<IUserRepository>().GetSearchable(id, fieldName)).ReturnsAsync(expectedSearchables);

            // Act
            var result = await _userService.GetSearchable(id, fieldName);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IUserRepository>().GetSearchable(id, fieldName), Times.Once);

            Assert.Equal(expectedSearchables, result);
        }
        
        [Fact]
        public async Task GetRole_ReturnsListOfRoles()
        {
            // Arrange   
            var expectedRoles = _fixture.CreateMany<CommonLookUp>(1).ToList();
           
            _mockUnitOfWork.Setup(uow => uow.Repository<IUserRepository>().GetRole()).ReturnsAsync(expectedRoles);

            // Act
            var result = await _userService.GetRole();

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IUserRepository>().GetRole(), Times.Once);

            Assert.Equal(expectedRoles, result);
        }
        
        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfBusinessUnits()
        {
            // Arrange   
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(1).ToList();
           
            _mockCommonRepository.Setup(c => c.GetTenants()).ReturnsAsync(expectedBusinessUnits);

            // Act
            var result = await _userService.GetBusinessUnits();

            // Assert
            _mockCommonRepository.Verify(c => c.GetTenants(), Times.Once);

            Assert.Equal(expectedBusinessUnits, result);
        }
        
    }
}
