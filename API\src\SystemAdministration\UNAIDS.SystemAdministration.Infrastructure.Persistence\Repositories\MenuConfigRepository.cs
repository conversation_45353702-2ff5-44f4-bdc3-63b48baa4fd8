using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class MenuConfigRepository(SystemAdminDbContext dbContext) : BaseRepository<Menu>(dbContext), IMenuConfigRepository
    {
        public async Task<List<MCHierarchyDTO>> GetMenuHierarchy()
        {
            // Fetch menu items
            var menuItems = await (from menu in dbContext.Menus.AsNoTracking()
                                   where (menu.IsHidden ?? 0) != 1
                                   orderby menu.ModuleId ascending
                                   select new MCHierarchyDTO()
                                   {
                                       Id = menu.Id,
                                       ModuleId = menu.ModuleId,
                                       Title = menu.Title,
                                       Action = menu.Action,
                                       URL = menu.URL,
                                       Controller = menu.Controller,
                                       MenuType = menu.MenuType,
                                       Area = menu.Area,
                                       SortOrder = menu.SortOrder,
                                       ParentMenuId = menu.ParentMenuId ?? 0,
                                   }).ToListAsync();

            // Build the hierarchy starting with root nodes
            var hierarchy = menuItems
                .Where(x => x.ParentMenuId == 0)
                .Select(x => new MCHierarchyDTO
                {
                    Id = x.Id,
                    ModuleId = x.ModuleId,
                    Title = x.Title,
                    Action = x.Action,
                    URL = x.URL,
                    Controller = x.Controller,
                    MenuType = x.MenuType,
                    Area = x.Area,
                    SortOrder = x.SortOrder,
                    ParentMenuId = x.ParentMenuId,
                    Children = GetChildMenus(x.Id, menuItems)
                })
                .ToList();

            return hierarchy;
        }

        private List<MCHierarchyDTO> GetChildMenus(int parentId, List<MCHierarchyDTO> menuItems)
        {
            return menuItems
                .Where(x => x.ParentMenuId == parentId)
                .Select(x => new MCHierarchyDTO
                {
                    Id = x.Id,
                    ModuleId = x.ModuleId,
                    Title = x.Title,
                    Action = x.Action,
                    URL = x.URL,
                    Controller = x.Controller,
                    MenuType = x.MenuType,
                    Area = x.Area,
                    SortOrder = x.SortOrder,
                    ParentMenuId = x.ParentMenuId,
                    Children = GetChildMenus(x.Id, menuItems)
                })
                .ToList();
        }


        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await dbContext.Menus.AsNoTracking()
                .Select(menuconfig => new CommonLookUp
                {
                    DisplayText = menuconfig.Title,
                    Value = menuconfig.Id,
                    Active = 1
                }).ToListAsync();
        }
        public async Task<List<CommonLookUp>> GetModule()
        {
            return await dbContext.Modules.AsNoTracking()
                .OrderBy(module => module.Description)
                .Select(module => new CommonLookUp
                {
                    DisplayText = module.Description,
                    Value = module.Id,
                    Active = 1
                }).ToListAsync();
        }
        public async Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false)
        {
            return await dbContext.LookUpInfo.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Name,
                    Active = 1
                }).ToListAsync();
        }
        public async Task<List<CommonLookUp>> GetObjectTypes(string fieldName)
        {
            return await dbContext.LookUpInfo.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Name,
                    Active = 1
                }).ToListAsync();
        }
        public async Task<List<CommonLookUp>> GetMenuConfigDetails()
        {
            return await dbContext.Menus.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Title,
                    Active = 1
                }).ToListAsync();
        }
    }
}