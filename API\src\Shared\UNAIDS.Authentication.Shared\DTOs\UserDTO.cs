using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.Authentication.Shared
{
    public class UserDTO: BaseEntity
    {
        public required string UserName { get; set; }
        public required string LoginName { get; set; }
        public string NormalizedLoginName { get; set; } = string.Empty;
        public required string Email { get; set; }
        public string NormalizedEmail { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public int RoleId { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsLocked { get; set; }

    }
}