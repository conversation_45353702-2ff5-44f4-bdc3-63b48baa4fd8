﻿using Microsoft.AspNetCore.Http;
using UNAIDS.Core.Base;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public class TenantResolutionMiddleware
    {
        private readonly RequestDelegate _next;

        public TenantResolutionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context, ITenantProvider tenantProvider)
        {
            if (context.User.Identity.IsAuthenticated)
            {
                // Gets the value associated with the specified key from request header
                if (context.Request.Headers.TryGetValue("Tenant", out var tenantId))
                {
                    if (string.IsNullOrWhiteSpace(tenantId))
                    {
                        context.Response.StatusCode = StatusCodes.Status400BadRequest;
                        await context.Response.WriteAsync($"Access Denied.", context.RequestAborted);

                        return;
                    }

                    //Validate tenant
                    if (!tenantProvider.Validate(Convert.ToInt32(tenantId)))
                    {
                        await context.Response.WriteAsync($"Access Denied.", context.RequestAborted);

                        return;
                    }

                    using var scope = tenantProvider.BeginScope(Convert.ToInt32(tenantId));
                    await _next(context);
                }
                else
                {
                    context.Response.StatusCode = StatusCodes.Status400BadRequest;
                    await context.Response.WriteAsync($"Access Denied.", context.RequestAborted);

                    return;
                }
            }
            else
            {
                await _next(context);
            }
        }
    }
}
