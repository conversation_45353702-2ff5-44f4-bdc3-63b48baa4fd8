using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/roles")]
    [ApiController]
    public class RoleController(IRoleService<RoleDTO> roleService, IValidator<RoleDTO> validator) : ControllerBase
    {
        /// <summary>
        /// Get all Roles
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter, int id = 0)
        {
            var roles = await roleService.GetAll(filter);
            return Ok(new
            {
                Record = roles,
                CurrentStep = await roleService.GetCurrentStep(id),
                UserRights = roleService.AccessRights(SubjectTypes.ROLE)
            });
        }

        /// <summary>
        /// Get a specific Role by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            var data = await roleService.GetById(id);
            var currentStep = await roleService.GetCurrentStep(id);

            return Ok(new
            {
                Record = data,
                UserRights = roleService.AccessRights(SubjectTypes.ROLE)
            });
        }

        /// <summary>
        /// Create a new Role
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] RoleDTO role)
        {
            if (role == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(role);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await roleService.Create(role);
            return CreatedAtAction(nameof(Get), new { id = role.Id }, result);
        }

        /// <summary>
        /// Update a Role
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update(int id, [FromBody] RoleDTO role)
        {
            var validationResult = await validator.ValidateAsync(role);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await roleService.Update(role);
            return Ok(role);
        }

        /// <summary>
        /// Delete a Role
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await roleService.Delete(id);
            return NoContent();
        }

    }
}
