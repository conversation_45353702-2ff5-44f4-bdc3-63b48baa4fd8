using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using System.Threading.Tasks;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/user-permissions")]
    [ApiController]
    public class UserPermissionController(IUserPermissionService<UserPermissionDTO> userPermissionService, IValidator<UserPermissionDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list userpermission
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERPERMISSIONS}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var users = await userPermissionService.GetAll(filter);
            return Ok(new
            {
                Record = users,
                UserId = await userPermissionService.GetUser(),
                RoleId = await userPermissionService.GetRole(),
                BusinessUnitId = await userPermissionService.GetBusinessUnits(),
                UserRights = userPermissionService.AccessRights(SubjectTypes.USERPERMISSIONS)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get the userpermission by id
        /// </summary>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERPERMISSIONS}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var user = await userPermissionService.GetById(id);
            return Ok(new
            {
                Record = user,
                UserId = await userPermissionService.GetUser(),
                RoleId = await userPermissionService.GetRole(),
                BusinessUnitId = await userPermissionService.GetBusinessUnits(),
                UserRights = userPermissionService.AccessRights(SubjectTypes.USERPERMISSIONS)
            });
        }
        #endregion

        #region Create...
        /// <summary>
        /// Create a new userpermission
        /// </summary>
        /// <param name="userpermission"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.USERPERMISSIONS}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] UserPermissionDTO userPermission)
        {
            if (userPermission == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(userPermission);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await userPermissionService.Create(userPermission);
            return CreatedAtAction(nameof(Create), new { id = userPermission.Id }, result);
        }
        #endregion

        #region Update...
        /// <summary>
        /// Update a userpermission
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.USERPERMISSIONS}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] UserPermissionDTO userPermission)
        {
            var validationResult = await validator.ValidateAsync(userPermission);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }
            await userPermissionService.Update(userPermission);
            return Ok(userPermission);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a userpermission by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.USERPERMISSIONS}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await userPermissionService.Delete(id);
            return Ok();
        }
        #endregion

          
    }
}
