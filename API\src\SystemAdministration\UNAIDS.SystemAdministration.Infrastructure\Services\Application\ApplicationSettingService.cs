﻿using Microsoft.EntityFrameworkCore;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System.Globalization;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public class ApplicationSettingService(CommonDbContext dbContext, ApplicationDbContext applicationDbContext, IUserContext context, ITenantProvider tenantProvider) : IApplicationSettingService
    {
        public async Task<TemplateResponseDto> GetTemplate(int id)
        {
            var result = new TemplateResponseDto();

            //#.Fetch menuConfig details
            var menuConfig = await (from mc in dbContext.Menus.AsNoTracking()
                                    where mc.Id == id
                                    select mc).FirstOrDefaultAsync();

            if (menuConfig != null)
            {
                var template = await applicationDbContext.Templates.AsNoTracking()
                .Where(x => x.TemplatesId == menuConfig.TemplateId)
                .FirstOrDefaultAsync();

                if (menuConfig.ObjectType == (int)LookupConstants.ScreenTypes.MST || menuConfig.ObjectType == (int)LookupConstants.ScreenTypes.RPT)
                {
                    var serviceComponent = await applicationDbContext.ServiceComponents.AsNoTracking()
                        .Where(x => x.ServiceComponentId == menuConfig.ServiceComponentId).FirstOrDefaultAsync();

                    result.Component = [$"{menuConfig.Controller}Component"];
                    result.Caption = [menuConfig.Title];
                    result.Tooltip = [null];
                    result.ParentID = menuConfig.ParentMenuId;
                    result.Template = template?.DashboardJson;
                    result.UserId = context.Id;
                    result.Tabular = [menuConfig.Controller ?? ""];
                    result.SearchURL = menuConfig.URL;
                    result.Strapi = false;
                    result.MenuConfigId = [menuConfig.Id];

                }
                else
                {
                    var cardDetails = await dbContext.Menus.AsNoTracking()
                    .Where(x => x.ParentMenuId == menuConfig.ParentMenuId
                    && x.ObjectType != (int)LookupConstants.ScreenTypes.CONT)
                    .Select(x => new
                    {
                        x.Controller,
                        Caption = x.Title,
                        Tooltip = (string?)null,
                        x.SortOrder,
                        Strapi = false,
                        x.Id
                    })
                    .OrderBy(x => x.SortOrder)
                    .ToListAsync();

                    result.Component = cardDetails.Select(x => $"{x.Controller}Component").ToArray();
                    result.Caption = cardDetails.Select(x => x.Caption).ToArray();
                    result.Tooltip = cardDetails.Select(x => x.Tooltip).ToArray();
                    result.ParentID = menuConfig.ParentMenuId;
                    result.Template = template?.DashboardJson;
                    result.UserId = context.Id;
                    result.Tabular = cardDetails.Select(x => x.Caption).ToArray();
                    result.SearchURL = menuConfig.URL;
                    result.Strapi = false;
                    result.MenuConfigId = cardDetails.Select(x => x.Id).ToArray();
                    result.Child = true;
                }
            }

            return result;
        }

       
        public async Task<UserProfileDTO?> GetUserProfile()
        {
            return await dbContext.Users.AsNoTracking()
                .Where(x => x.Id == context.Id)
                .Select(x => new UserProfileDTO()
                {
                    UserEmail = x.Email,
                    UserName = x.UserName
                }).FirstOrDefaultAsync();
        }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {

            return await dbContext.BusinessUnits.AsNoTracking()
                .Where(x => context.Tenants.Contains(x.Id))
                .Select(x => new CommonLookUp()
                {
                    Value = x.Id,
                    DisplayText = x.Name,
                    Active = 1
                }).ToListAsync();
        }

        #region private methods....

        private int?[] GetParentMenus(List<MenuDto> menus, int?[] menuIds)
        {
            int?[] parentIds = menus
                .Where(x => menuIds.Contains(x.Id) && x.ParentId != null)
                .Select(x => x.ParentId)
                .Distinct()
                .ToArray();

            if (parentIds.Any())
                return parentIds.Union(GetParentMenus(menus, parentIds)).ToArray();
            else
                return parentIds;
        }

        #region GetChildren
        /// <summary>
        /// Method      : GetChildren
        /// Description : To fetch child menus of a parent menu
        /// </summary>
        /// <param name="MenuList"></param>
        /// <param name="parentId"></param>
        /// <returns></returns>
        public static List<MenuDto> GetChildren(List<MenuDto> menuList, float parentId)
        {
            string grandParent = string.Empty;
            var parent = (from rm in menuList
                          where (rm.Id == parentId)
                          select new
                          {
                              rm.Name,
                              rm.ParentId
                          }).Distinct().FirstOrDefault();

            if (parent != null)
            {
                grandParent = (from rm in menuList
                               where (rm.Id == parent.ParentId)
                               select rm.Name).FirstOrDefault() ?? string.Empty;
            }

            return menuList
                    .Where(a => a.ParentId == parentId)
                    .Select(menu => new MenuDto
                    {
                        Id = menu.Id,
                        ModuleId = menu.ModuleId,
                        Name = menu.Name,
                        URL = menu.URL,
                        SortOrder = menu.SortOrder ?? 0,
                        ModuleGroupId = menu.ModuleGroupId,
                        Action = menu.Action,
                        Area = menu.Area,
                        Controller = menu.Controller,
                        MenuLink = menu.MenuLink,
                        ParentId = menu.ParentId,
                        ParentName = parent?.Name ?? "",
                        BaseUrl = "",
                        ControllerType = menu.ControllerType,
                        Module = menu.Module,
                        ModuleTeaser = menu.ModuleTeaser,
                        IconColor = menu.IconColor,
                        MenuIcon = menu.MenuIcon,
                        Children = GetChildren(menuList, menu.Id)
                    }).OrderBy(b => b.SortOrder).ToList();
        }

        public Task<MenuResponseDto> GetMenu()
        {
            throw new NotImplementedException();
        }
        #endregion

        #endregion

    }
}
