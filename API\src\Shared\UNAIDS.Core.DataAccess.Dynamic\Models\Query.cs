﻿namespace UNAIDS.Core.DataAccess.Dynamic
{
    public class Query
    {
        public string Entity { set; get; } = string.Empty;
        public string[]? SelectedProperties { set; get; }
        public List<FilterRule>? FilterRules { set; get; }
    }

    public class FilterRule
    {
        public string Field { get; set; }
        public string Operator { get; set; }
        public string DataType { get; set; }
        public object Value { get; set; }
        public object AdditionalValue { get; set; }
    }
}
