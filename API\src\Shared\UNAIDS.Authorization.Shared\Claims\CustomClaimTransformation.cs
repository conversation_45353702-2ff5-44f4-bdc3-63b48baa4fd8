﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System.Security.Claims;

namespace UNAIDS.Authorization.Shared
{
    public class CustomClaimTransformation(IUnitOfWork<CommonDbContext> unitOfWork, IClaimCache claimCache, IHttpContextAccessor httpContextAccessor) : IClaimsTransformation
    {
        public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal) 
        {
            var tenantId = Convert.ToInt32(httpContextAccessor.HttpContext.Request.Headers["Tenant"]);

            var claimKey = $"{principal.FindFirst(ClaimTypes.NameIdentifier)?.Value}~{tenantId}";


            if (principal.HasClaim(claim => claim.Type == ClaimTypes.Email))
            {
                var emailAddress = principal.FindFirst(ClaimTypes.Email)?.Value;

                var claims = claimCache.GetToken(claimKey ?? "");
                if (claims == null)
                {
                    var userClaims = await unitOfWork.Repository<IClaimRepository>().GetUserClaims(emailAddress ?? "", tenantId);

                    // Extract the 'exp' claim
                    var expiry = DateTime.UtcNow.AddDays(1);
                    var expClaim = principal.FindFirst(c => c.Type == "exp");

                    if (expClaim != null) { 
                        // Convert the 'exp' claim value to DateTime
                        expiry = DateTimeOffset.FromUnixTimeSeconds(long.Parse(expClaim.Value)).UtcDateTime; 
                    }                    

                    claims = new UserClaims()
                    {
                        ExpiryDate = expiry,
                        Tenant = tenantId,
                        Claims = userClaims.Select(x => new Claim(x.Key, x.Value)).ToList()
                    };

                    claimCache.SetToken(claimKey, claims);
                }

                ((ClaimsIdentity)principal.Identity).AddClaims(claims.Claims);
            }

            return principal;
        }
    }
}
