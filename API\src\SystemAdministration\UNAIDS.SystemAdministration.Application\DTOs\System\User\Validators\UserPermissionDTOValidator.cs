using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System;
using System.Threading.Tasks;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class UserPermissionDTOValidator : AbstractValidator<UserPermissionDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public UserPermissionDTOValidator(SystemAdminDbContext dbContext)
        {
            _dbContext = dbContext;

            // UserId: Required
            RuleFor(x => x.UserId)
                .NotEmpty().WithMessage("The UserId is required.");

            // BusinessUnitId: Required
            RuleFor(x => x.BusinessUnitId)
                .NotEmpty().WithMessage("The BusinessUnitId is required.");

            // RoleId: Required
            RuleFor(x => x.RoleId)
                .NotEmpty().WithMessage("The RoleId is required.");

            // BusinessUnitId and RoleId with UserId, ModuleGroupId, and ModuleId must be unique
            RuleFor(x => x)
                .MustAsync(async (x, cancellation) => await UniqueUserPermissionMapping(x))
                .WithMessage("The User Permission Mapping already exists.");

        }
        private async Task<bool> UniqueUserPermissionMapping(UserPermissionDTO userPermissionDTO)
        {
            // Check if a record already exists with the same combination of UserId, BusinessUnitId, RoleId, ModuleGroupId, and ModuleId
            return !await _dbContext.UserPermissions.AsNoTracking()
                .AnyAsync(a =>
                    a.UserId == userPermissionDTO.UserId &&
                    a.BusinessUnitId == userPermissionDTO.BusinessUnitId &&
                    a.RoleId == userPermissionDTO.RoleId &&
                    a.Id != userPermissionDTO.Id);
        }
    }
}
