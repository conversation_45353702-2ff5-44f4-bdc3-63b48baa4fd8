﻿
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class BusinessUnitRepository(SystemAdminDbContext dbContext) : BaseRepository<BusinessUnit>(dbContext), IBusinessUnitRepository
    {
        public async Task<List<BUHierarchyDTO>> GetBUHierarchy()
        {
            var businessunits = await (from bu in dbContext.BusinessUnits.AsNoTracking()
                                       join obs in dbContext.OBSSettings.AsNoTracking()
                                       on bu.OBSSettingId equals obs.Id
                                       where bu.Id > 0
                                       select new BUHierarchyDTO()
                                       {
                                           Id = bu.Id,
                                           OBSSettingId = bu.OBSSettingId,
                                           Address = bu.Address,
                                           Code = bu.Code,
                                           Name = bu.Name,
                                           Level = bu.Level,
                                           OBSSettingName = obs.Name,
                                           ParentBusinessUnitId = bu.ParentBusinessUnitId,
                                           IsDeleted = bu.IsDeleted,
                                           CreatedAt = bu.CreatedAt,
                                           CreatedById = bu.CreatedById,
                                           UpdatedById = bu.UpdatedById,
                                           UpdatedAt = bu.UpdatedAt
                                           
                                       }).ToListAsync();

            var hierarchy = businessunits.Where(x => x.ParentBusinessUnitId == null || x.ParentBusinessUnitId == 0)
                .Select(x => new BUHierarchyDTO()
                {
                    Id = x.Id,
                    OBSSettingId = x.OBSSettingId,
                    Address = x.Address,
                    Code = x.Code,
                    Name = x.Name,
                    Level = x.Level,
                    OBSSettingName = x.Name,
                    ParentBusinessUnitId = x.ParentBusinessUnitId,
                    IsDeleted = x.IsDeleted,
                    CreatedAt = x.CreatedAt,
                    CreatedById = x.CreatedById,
                    UpdatedById = x.UpdatedById,
                    UpdatedAt = x.UpdatedAt,
                    Children = GetChildren(x.Id, businessunits)
                }).ToList();

            return hierarchy;
        }

        public async Task<List<CommonLookUp>> GetOBSSettings()
        {
            return await dbContext.OBSSettings.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetParentBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp()
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                }).ToListAsync();
        }

        private List<BUHierarchyDTO> GetChildren(int parentId, List<BUHierarchyDTO> bunits)
        {
            return bunits
                .Where(x => x.ParentBusinessUnitId == parentId)
                .Select(x => new BUHierarchyDTO() {
                    Id = x.Id,
                    OBSSettingId = x.OBSSettingId,
                    Address = x.Address,
                    Code = x.Code,
                    Name = x.Name,
                    Level = x.Level,
                    OBSSettingName = x.Name,
                    ParentBusinessUnitId = x.ParentBusinessUnitId,
                    IsDeleted = x.IsDeleted,
                    CreatedAt = x.CreatedAt,
                    CreatedById = x.CreatedById,
                    UpdatedById = x.UpdatedById,
                    UpdatedAt = x.UpdatedAt,
                    Children = GetChildren(x.Id, bunits)
                })
                .ToList();
        }
    }
}
