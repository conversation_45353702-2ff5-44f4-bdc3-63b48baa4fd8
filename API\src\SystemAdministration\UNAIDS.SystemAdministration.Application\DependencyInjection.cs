﻿using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using UNAIDS.Authentication.Shared;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public static class DependencyInjection
    {
        /// <summary>
        /// Add application service to specified IServiceCollection
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
            => services
                .AddFluentValidators()
                .AddAutoMapper(typeof(SystemAdministrationMappingProfile));

        /// <summary>
        /// Automatically register all validators from the assembly containing the specified type
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddFluentValidators(this IServiceCollection services)
            => services
                .AddValidatorsFromAssemblyContaining<ProgramOnboardDTOValidator>()
                .AddValidatorsFromAssemblyContaining<UserDTOValidator>();
    }
}
