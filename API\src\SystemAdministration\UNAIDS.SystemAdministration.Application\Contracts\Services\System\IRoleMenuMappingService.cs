using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IRoleMenuMappingService<T> : IBaseAPIService<T> where T : class
    {
        Task<List<RoleMenuMappingDTO>> GetRoleMenus(int rolemenuId);
        Task<List<CommonLookUp>> GetMenuConfig();
        Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false);
        Task<List<CommonLookUp>> SavePermission();
        Task<List<RoleMenuMappingDTO>> BulkUpdate(List<RoleMenuMappingDTO> permissions);
    }
}
