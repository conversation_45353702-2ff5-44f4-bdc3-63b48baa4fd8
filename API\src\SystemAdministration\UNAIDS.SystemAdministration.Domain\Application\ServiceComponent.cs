﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;

namespace UNAIDS.SystemAdministration.Domain
{
    [ExcludeFromCodeCoverage]
    [Table(name: "service_components")]
    public class ServiceComponent : Audit
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ServiceComponentId { get; set; }

        [Display(Name = "Code")]
        [Required(ErrorMessage = "The Code required.")]
        [MaxLength(45, ErrorMessage = "Code length must not exceed 45 characters.")]
        public string Code { get; set; } = string.Empty;

        [Display(Name = "Name")]
        [Required(ErrorMessage = "The Name required.")]
        [MaxLength(100, ErrorMessage = "Name length must not exceed 100 characters.")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        public int? ParameterSchemaDefinitionId { get; set; }

        [MaxLength(5000)]
        [Display(Name = "Default Parameter JSON")]
        public string? DefaultParamJSON { get; set; }

        [MaxLength(200)]
        [Display(Name = "Caption")]
        public string? Caption { get; set; }

        [MaxLength(300)]
        [Display(Name = "Tool Tip")]
        public string? Tooltip { get; set; }

        [MaxLength(200)]
        [Display(Name = "Controller")]
        public string? Controller { get; set; }

        [MaxLength(200)]
        [Display(Name = "Area")]
        public string? Area { get; set; }

        [MaxLength(200)]
        [Display(Name = "Action")]
        public string? Action { get; set; }

        [Display(Name = "Object Type")]
        public int? ObjectType { get; set; }

        [Display(Name = "Menu Type")]
        public int? MenuType { get; set; }

        [MaxLength(500)]
        [Display(Name = "Controller Type")]
        public string? ControllerType { get; set; }

        [MaxLength(1000)]
        public string? MenuDetailsJSON { get; set; }

        [Display(Name = "Model")]
        public int? ModelDefinitionId { get; set; }
    }
}
