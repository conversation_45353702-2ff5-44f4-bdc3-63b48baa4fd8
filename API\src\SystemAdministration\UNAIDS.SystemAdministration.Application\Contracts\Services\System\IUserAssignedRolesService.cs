using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IUserAssignedRolesService<T> : IBaseAPIService<T> where T : class
    {
        Task<List<CommonLookUp>> GetUserAssignedRoles(Filter filter);
        Task<List<CommonLookUp>> GetBusinessUnits();
        Task<List<CommonLookUp>> GetUserNames();
        Task<List<CommonLookUp>> GetLoginNames();
    }
}
