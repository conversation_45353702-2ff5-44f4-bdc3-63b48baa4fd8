﻿using Dapper;
using System.Collections;
using System.Text;
using System.Text.Json;

namespace UNAIDS.Core.DataAccess.Dynamic
{
    public static class FilterQueryBuilder
    {
        public static void BuildFilterQuery(this StringBuilder q, DbEntity entity, List<FilterRule> filterRules, ref DynamicParameters parameters)
        {          
            q.Append("WHERE ");

            var index = 1;
            foreach (var rule in filterRules) 
            {
                var property = entity.Properties.FirstOrDefault(p => p.Name == rule.Field.Split('.')[0])
                            ?? throw new ApplicationException($"Dynamic Query Exception : Invalid field name {rule.Field}");

                if(index != 1) q.Append(" AND ");

                string condition = rule.Operator.ToLower() switch { 
                    "in" or "not in" => rule.In(property, ref parameters),
                    "==" or "!=" or ">" or "<" or ">=" or "<=" => rule.EqualTo(property, ref parameters),
                    "like" or "ilike" => rule.Like(property, ref parameters),
                    "between" or "not between" => rule.Between(property, ref parameters),
                    "null" or "not null" => rule.IsNull(),
                    _ => throw new ApplicationException($"Dynamic Query Exception : Unsupported operator {rule.Operator}")
                };

                q.Append(condition);

                index++;
            }
        }

        // in, not in
        public static string In(this FilterRule filterRule, EntityProperties property, ref DynamicParameters parameters) 
        {
            if (filterRule.Value is not IEnumerable list || filterRule.Value is string)
                throw new ApplicationException($"Dynamic Query Exception : Invalid field Value {filterRule.Value}");
            
            var parameterNames = new List<string>();
            string sqlField = filterRule.Field.FormatFieldPath();
            bool isNotIn = filterRule.Operator.ToLower().Equals("not in", StringComparison.OrdinalIgnoreCase);

            foreach (var item in list.Cast<object>())
            {
                var paramName = $"P{parameters.ParameterNames.Count()}";
                
                if (item is JsonDocument jsonDoc)
                {
                    parameters.Add(paramName, jsonDoc.RootElement.GetRawText());
                    parameterNames.Add($"CAST(@{paramName} AS JSONB)");
                }
                else
                {
                    parameters.Add(paramName, item.ValidateSupportedType(isNotIn ? "NOT IN filter":"IN filter"));
                    parameterNames.Add($"@{paramName}");
                }
            }

            // Handle array types differently for "NOT IN"
            if (property.Type.Contains("[]"))
            {
                return isNotIn
                    ? $"NOT EXISTS (SELECT 1 FROM unnest({sqlField}) elem WHERE elem = ANY(ARRAY[{string.Join(", ", parameterNames)}]))"
                    : $"{sqlField} && ARRAY[{string.Join(", ", parameterNames)}]";
            }
            
            return $"{sqlField} {filterRule.Operator.ToUpper()} ({string.Join(", ", parameterNames)})";
        }

        // ==, !=, >, <, >=, <=
        public static string EqualTo(this FilterRule filterRule, EntityProperties property, ref DynamicParameters parameters)
        {
            if (filterRule.Value == null)
                throw new ApplicationException($"Dynamic Query Exception : Invalid field Value {filterRule.Value}");

            var paramName = $"P{parameters.ParameterNames.Count()}";
            string sqlField = filterRule.Field.FormatFieldPath();
            
            // Handle JSON
            if (filterRule.Value is JsonDocument jsonDoc)
            {
                parameters.Add(paramName, jsonDoc.RootElement.GetRawText());
                return filterRule.Operator.Equals("!=", StringComparison.OrdinalIgnoreCase)
                    ? $"{sqlField}::jsonb <> CAST(@{paramName} AS JSONB)"
                    : $"{sqlField}::jsonb {filterRule.Operator.Replace("==", "=").ToUpper()} CAST(@{paramName} AS JSONB)";
            }

            // Handle Array
            if (property.Type.Contains("[]"))
            {
                parameters.Add(paramName, filterRule.Value.ValidateSupportedType($"{filterRule.Operator.ToUpper()} filter"));

                if (filterRule.Operator.Equals("!=", StringComparison.OrdinalIgnoreCase))
                    return $"NOT EXISTS (SELECT 1 FROM unnest({sqlField}) elem WHERE elem = @{paramName})";

                return  $"@{paramName} {filterRule.Operator.Replace("==", "=").ToUpper()} ANY({sqlField})";
            }

            // Handle Other Value
            parameters.Add(paramName, filterRule.Value.ValidateSupportedType($"{filterRule.Operator.ToUpper()} filter"));
            return $"{sqlField} {filterRule.Operator.Replace("==", "=").ToUpper()} @{paramName}";
        }

        // like, ilike (case-insensitive)
        public static string Like(this FilterRule filterRule, EntityProperties properties, ref DynamicParameters parameters)
        {
            if (filterRule.Value == null || filterRule.Value is not string)
                throw new ApplicationException($"Dynamic Query Exception : {filterRule.Operator.ToUpper()} filter requires a string value");
             if (properties.Type != "String" && properties.Type != "JsonDocument")
                throw new ApplicationException($"Dynamic Query Exception : {filterRule.Operator.ToUpper()} filter can only be used against a string value");

            var paramName = $"P{parameters.ParameterNames.Count()}";
            string sqlField = filterRule.Field.FormatFieldPath();
            
            parameters.Add(paramName, filterRule.Value);
            return $"{sqlField} {filterRule.Operator.ToUpper()} @{paramName}";
        }

        // between
        public static string Between(this FilterRule filterRule, EntityProperties properties, ref DynamicParameters parameters)
        {
            if (filterRule.Value == null || filterRule.Value is not List<object> values || values.Count != 2)
                throw new ApplicationException($"Dynamic Query Exception: {filterRule.Operator.ToUpper()} filter requires exactly two values");

            if (properties.Type is not ("Int32" or "Decimal" or "Double" or "DateTime"))
                throw new ApplicationException($"Dynamic Query Exception : {filterRule.Operator.ToUpper()} filter can only be used with numeric or date values");

            var param1 = $"P{parameters.ParameterNames.Count()}";
            var param2 = $"P{parameters.ParameterNames.Count() + 1}";

            string sqlField = filterRule.Field.FormatFieldPath();

            parameters.Add(param1, values[0]);
            parameters.Add(param2, values[1]);

            return $"{sqlField} {filterRule.Operator.ToUpper()} @{param1} AND @{param2}";
        }

        // is null, is not null
        public static string IsNull(this FilterRule filterRule)
        {
            return $"{filterRule.Field.FormatFieldPath()} IS {filterRule.Operator.ToUpper()}";
        }


    }
    
}
