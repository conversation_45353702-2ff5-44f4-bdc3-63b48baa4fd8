using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using Microsoft.AspNetCore.Http;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/business-units")]
    [ApiController]

    public class BusinessUnitController(IBusinessUnitService<BusinessUnitDTO> businessUnitService, IValidator<BusinessUnitDTO> validator) : ControllerBase
    {
        /// <summary>
        /// Get all BusinessUnits
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.BUSINESSUNIT}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var data = await businessUnitService.GetBuHierarchy();
            return Ok(new
            {
                Record = data,
                OBSSettingId = await businessUnitService.GetOBSSettings(),
                ParentBusinessUnitId = await businessUnitService.GetParentBusinessUnits(),
                TotalRecordCount = data.Count,
                UserRights =  businessUnitService.AccessRights(SubjectTypes.BUSINESSUNIT)
            });
        }

        /// <summary>
        /// Get a specific BusinessUnit by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.BUSINESSUNIT}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetById(int id)
        {
            var data = await businessUnitService.GetById(id);
            if (data == null)
                return NotFound();

            return Ok(data);
        }

        /// <summary>
        /// Create a new BusinessUnit
        /// </summary>
        /// <param name="businessUnit"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.BUSINESSUNIT}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] BusinessUnitDTO businessUnit)
        {
            if (businessUnit == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(businessUnit);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await businessUnitService.Create(businessUnit);
            return CreatedAtAction(nameof(GetById), new { id = businessUnit.Id }, result);
        }

        /// <summary>
        /// Update a BusinessUnit
        /// </summary>
        /// <param name="id"></param>
        /// <param name="businessUnit"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.BUSINESSUNIT}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] BusinessUnitDTO businessUnit)
        {
            var validationResult = await validator.ValidateAsync(businessUnit);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await businessUnitService.Update(businessUnit);
            return Ok(businessUnit);
        }

        /// <summary>
        /// Delete a BusinessUnit
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.BUSINESSUNIT}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            var exists = await businessUnitService.GetById(id);
            if (exists == null)
                return NotFound();

            await businessUnitService.Delete(id);
            return NoContent();
        }
    }
}
