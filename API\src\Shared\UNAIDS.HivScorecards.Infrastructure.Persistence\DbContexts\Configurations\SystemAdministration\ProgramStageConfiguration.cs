﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UNAIDS.HivScorecards.Domain.TenantManagement;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public sealed class ProgramStageConfiguration : IEntityTypeConfiguration<ProgramStage>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<ProgramStage> builder)
        {
            builder.ToTable("program_stage");
            builder.<PERSON><PERSON><PERSON>(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever();
            builder.Property(c => c.Name).IsRequired().HasMaxLength(500);
            builder.HasOne(c => c.Program).WithMany().HasForeignKey(x => x.ProgramId);
            builder.Navigation(c => c.Program).AutoInclude();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new ProgramStageConfiguration());
        }
    }
}
