using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System.Threading.Tasks;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class WorkflowStepDTOValidator : AbstractValidator<WorkflowStepDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public WorkflowStepDTOValidator(SystemAdminDbContext dbContext)
        {
            _dbContext = dbContext;

            // WorkflowDefinitionId: Required and must be greater than 0
            RuleFor(x => x.WorkflowDefinitionId)
                .GreaterThan(0).WithMessage("The WorkflowDefinitionId must be greater than 0.");

            // StepNo: Required and must be greater than 0
            RuleFor(x => x.StepNo)
                .GreaterThan(0).WithMessage("The StepNo must be greater than 0.");

            // StepCode: Required and max length of 50
            RuleFor(x => x.StepCode)
                .NotEmpty().WithMessage("The StepCode is required.")
                .MaximumLength(50).WithMessage("The StepCode must be at most 50 characters long.");

            // StepName: Required and max length of 100
            RuleFor(x => x.StepName)
                .NotEmpty().WithMessage("The StepName is required.")
                .MaximumLength(100).WithMessage("The StepName must be at most 100 characters long.");

            // Action: Optional, but max length of 250
            RuleFor(x => x.Action)
                .NotEmpty().WithMessage("The Action is required.")
                .MaximumLength(250).WithMessage("The Action must be at most 250 characters long.");

            // SortOrder: Required and must be non-negative
            RuleFor(x => x.SortOrder)
                .GreaterThanOrEqualTo(0).WithMessage("The SortOrder must be a non-negative value.");

            // IsForwardFlow, LockStatus, TriggerService, Published: Optional and must be either 0 or 1
            RuleFor(x => x.IsForwardFlow)
                .InclusiveBetween((short)0, (short)1).WithMessage("The IsForwardFlow must be 0 or 1.")
                .When(x => x.IsForwardFlow.HasValue);

            RuleFor(x => x.LockStatus)
                .InclusiveBetween((short)0, (short)1).WithMessage("The LockStatus must be 0 or 1.")
                .When(x => x.LockStatus.HasValue);

            RuleFor(x => x.TriggerService)
                .InclusiveBetween((short)0, (short)1).WithMessage("The TriggerService must be 0 or 1.")
                .When(x => x.TriggerService.HasValue);

            RuleFor(x => x.Published)
                .InclusiveBetween((short)0, (short)1).WithMessage("The Published must be 0 or 1.")
                .When(x => x.Published.HasValue);

            RuleFor(x => x)
                .MustAsync((dto, stepCode, cancellationToken) => BeUniqueStepCode(dto, cancellationToken))
                .WithMessage("The WorkFlow Step with this code already exists for the specified WorkflowDefinition.");

        }
        
        private async Task<bool> BeUniqueStepCode(WorkflowStepDTO dto, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(dto.StepCode))
                return true;

            return !await _dbContext.WorkflowSteps.AsNoTracking()
                .AnyAsync(a =>
                    a.Id != dto.Id && a.StepCode.Trim().ToUpper() == dto.StepCode.Trim().ToUpper()
                    && a.WorkflowDefinitionId == dto.WorkflowDefinitionId
                    , cancellationToken);
        }

    }
}
