using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class RolePermissionMappingRepository(SystemAdminDbContext dbContext) : BaseRepository<RolePermissionMapping>(dbContext), IRolePermissionMappingRepository
    {
        

    }
}
