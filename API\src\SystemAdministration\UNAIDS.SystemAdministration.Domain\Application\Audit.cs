﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;

namespace UNAIDS.SystemAdministration.Domain
{
    [ExcludeFromCodeCoverage]
    public abstract class Audit
    {
        [Display(Name = "Active")]
        public short? Active { get; set; }

        [ScaffoldColumn(false)]
        public int? CreatedById { get; set; }

        [ScaffoldColumn(false)]
        public DateTime? CreatedDate { get; set; }

        [ScaffoldColumn(false)]
        public int? UpdatedById { get; set; }

        [ScaffoldColumn(false)]
        public DateTime? UpdatedDate { get; set; }

        [ScaffoldColumn(false)]
        public int? SysSchemaId { get; set; }

        [ScaffoldColumn(false)]
        public string SysJSON { get; set; }

        [ScaffoldColumn(false)]
        public int? CusSchemaId { get; set; }

        [ScaffoldColumn(false)]
        public string CusJSON { get; set; }

        [ScaffoldColumn(false)]
        public int? OriginBusinessUnitId { get; set; }

        [ScaffoldColumn(false)]
        public int? ModelDefinitionId { get; set; }

        public Guid? TenantId { get; set; }
    }
}
