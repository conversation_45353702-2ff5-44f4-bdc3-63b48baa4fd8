﻿using AutoMapper;
using UNAIDS.HivScorecards.Domain.TenantManagement;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.Model.Administration;
using UNAIDS.Authentication.Shared;

namespace UNAIDS.SystemAdministration.Application
{
    public class SystemAdministrationMappingProfile : Profile
    {
        public SystemAdministrationMappingProfile()
        {
            CreateMap<Program, ProgramListResponseDTO>().ReverseMap();
            CreateMap<Program, ProgramOnboardDTO>().ReverseMap();
            CreateMap<LookUpType, LookupTypeDTO>().ReverseMap();
            CreateMap<LookUpInfo, LookupInfoDTO>().ReverseMap();
            CreateMap<Setting, SettingDTO>().ReverseMap();
            CreateMap<OBSSetting, OBSSettingDTO>().ReverseMap();
            CreateMap<BusinessUnit, BusinessUnitDTO>().ReverseMap()
                .BeforeMap((s, d) => d.OBSSetting = null);
            //CreateMap<UNAIDS.Domain.TenantManagement.ProgramConcept, Concept>().ReverseMap();
            //CreateMap<ProgramCategory, Category>().ReverseMap();
            CreateMap<Role, RoleDTO>().ReverseMap();
            CreateMap<RoleMenuMapping, RoleMenuMappingDTO>().ReverseMap();
            CreateMap<RoleMenuMaster, RoleMenuMasterDTO>().ReverseMap();
            CreateMap<RolePermissionMapping, RolePermissionMappingDTO>().ReverseMap();
            CreateMap<Menu, MenuConfigDTO>().ReverseMap();
            CreateMap<MenuDetail, MenuDetailDTO>().ReverseMap();
            CreateMap<User, UserDTO>().ReverseMap();
            CreateMap<UserPermission, UserPermissionDTO>().ReverseMap()
                .BeforeMap((s, d) => d.Role = null);
            CreateMap<UserGroupMapping, UserGroupMappingDTO>().ReverseMap();
            CreateMap<UserGroups, UserGroupsDTO>().ReverseMap();
            CreateMap<WorkflowDefinition, WorkflowDefinitionDTO>().ReverseMap();
            CreateMap<WorkflowStep, WorkflowStepDTO>().ReverseMap();
            CreateMap<WorkflowPermission, WorkflowPermissionDTO>().ReverseMap();
        }
    }
}
