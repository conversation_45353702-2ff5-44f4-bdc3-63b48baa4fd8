using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application.DTOs;


namespace UNAIDS.SystemAdministration.Application
{
    public interface IRoleMenuMappingRepository : IBaseRepository<RoleMenuMapping>
    {
        Task<List<RoleMenuMappingDTO>> GetRoleMenus(int rolemenuId);
        Task<List<CommonLookUp>> GetMenuConfig();
        Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false);
        Task<List<CommonLookUp>> SavePermission();
        Task<List<RoleMenuMappingDTO>> GetExistingPermissions(int roleMenuMasterId);
    }
}
