namespace UNAIDS.SystemAdministration.Application
{
    public class SettingDTO
    {
        public int Id { get; set; }
        public int ModuleId { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Criteria { get; set; } = string.Empty;
        public int DataTypeId { get; set; }  // Assuming this is a foreign key reference to `LookUpInfo`
        public string Value { get; set; } = string.Empty;
        public string Group { get; set; } = string.Empty;
        public int? SchemaDefinitionId { get; set; }
        public string? Remarks { get; set; }
        public string? BusinessRules { get; set; }
    }
}
