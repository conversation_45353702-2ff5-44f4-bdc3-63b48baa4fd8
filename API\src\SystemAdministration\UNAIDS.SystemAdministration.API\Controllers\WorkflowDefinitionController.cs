using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.Core.Base;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/workflow-definition")]
    [ApiController]
    public class WorkflowDefinitionController(IWorkflowDefinitionService<WorkflowDefinitionDTO> workflowDefinitionService,IValidator<WorkflowDefinitionDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list workflowDefinitionService
        /// </summary>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWDEFINITION}.{ActionAliases.VIEW}")]
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var returnData = await workflowDefinitionService.GetAll(filter);
            
            return Ok(new
            {
                Record = returnData,
                ModuleGroupId = await workflowDefinitionService.GetModuleGroups(),
                UserRights = workflowDefinitionService.AccessRights(SubjectTypes.WORKFLOWDEFINITION)
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get workflow definition details
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.WORKFLOWDEFINITION}.{ActionAliases.VIEW}")]
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var workFlowDefinition = await workflowDefinitionService.GetById(id);
            return Ok(new
            {
                Record = workFlowDefinition,
                ModuleGroupId = await workflowDefinitionService.GetModuleGroups(),
                UserRights = workflowDefinitionService.AccessRights(SubjectTypes.WORKFLOWDEFINITION)
            });
        }
        #endregion

        #region Post...
        /// <summary>
        /// Create a new workflow definition
        /// </summary>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.WORKFLOWDEFINITION}.{ActionAliases.ADD}")]
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] WorkflowDefinitionDTO workflowDefinition)
        {
            if (workflowDefinition == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(workflowDefinition);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await workflowDefinitionService.Create(workflowDefinition);
            return CreatedAtAction(nameof(Create), new { id = workflowDefinition.Id }, result);
        }
        #endregion

        #region Put...
        /// <summary>
        /// Update a workflow definition
        /// </summary>
        /// <returns></returns> 
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.WORKFLOWDEFINITION}.{ActionAliases.EDIT}")]
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] WorkflowDefinitionDTO workflowDefinition)
        {
            var validationResult = await validator.ValidateAsync(workflowDefinition);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            await workflowDefinitionService.Update(workflowDefinition);
            return Ok(workflowDefinition);
        }
        #endregion

        #region Delete...
        /// <summary>
        /// Delete a workflow definition
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns> 
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [HttpDelete("{id}")]
        [Authorize($"{SubjectTypes.WORKFLOWDEFINITION}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await workflowDefinitionService.Delete(id);
            return Ok();
        }
        #endregion
    }
}