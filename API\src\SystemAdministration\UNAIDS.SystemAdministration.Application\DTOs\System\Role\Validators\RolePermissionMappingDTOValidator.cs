using FluentValidation;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class RolePermissionMappingDTOValidator : AbstractValidator<RolePermissionMappingDTO>
    {
        public RolePermissionMappingDTOValidator()
        {
            // RoleId: Required
            RuleFor(x => x.RoleId)
                .GreaterThan(0).WithMessage("The RoleId is required and must be greater than 0.");

            // Menu permissions: Each must be non-negative
            RuleFor(x => x.MenuAdd).GreaterThanOrEqualTo(0).WithMessage("MenuAdd must be non-negative.");
            RuleFor(x => x.MenuModify).GreaterThanOrEqualTo(0).WithMessage("MenuModify must be non-negative.");
            RuleFor(x => x.MenuCancel).GreaterThanOrEqualTo(0).WithMessage("MenuCancel must be non-negative.");
            RuleFor(x => x.MenuView).GreaterThanOrEqualTo(0).WithMessage("MenuView must be non-negative.");
            RuleFor(x => x.MenuPrint).GreaterThanOrEqualTo(0).WithMessage("MenuPrint must be non-negative.");
            RuleFor(x => x.MenuRePrint).GreaterThanOrEqualTo(0).WithMessage("MenuRePrint must be non-negative.");
            RuleFor(x => x.MenuDelete).GreaterThanOrEqualTo(0).WithMessage("MenuDelete must be non-negative.");
            RuleFor(x => x.MenuProcess).GreaterThanOrEqualTo(0).WithMessage("MenuProcess must be non-negative.");
            RuleFor(x => x.MenuApprove).GreaterThanOrEqualTo(0).WithMessage("MenuApprove must be non-negative.");
            RuleFor(x => x.MenuPreDatedEntry).GreaterThanOrEqualTo(0).WithMessage("MenuPreDatedEntry must be non-negative.");
            RuleFor(x => x.MenuImport).GreaterThanOrEqualTo(0).WithMessage("MenuImport must be non-negative.");
            RuleFor(x => x.MenuExport).GreaterThanOrEqualTo(0).WithMessage("MenuExport must be non-negative.");
            RuleFor(x => x.MenuValidation).GreaterThanOrEqualTo(0).WithMessage("MenuValidation must be non-negative.");
            RuleFor(x => x.MenuCorrect).GreaterThanOrEqualTo(0).WithMessage("MenuCorrect must be non-negative.");
            RuleFor(x => x.MenuBulkImport).GreaterThanOrEqualTo(0).WithMessage("MenuBulkImport must be non-negative.");
            RuleFor(x => x.MenuBulkUpdate).GreaterThanOrEqualTo(0).WithMessage("MenuBulkUpdate must be non-negative.");
            RuleFor(x => x.MenuBulkDelete).GreaterThanOrEqualTo(0).WithMessage("MenuBulkDelete must be non-negative.");
            RuleFor(x => x.MenuExportRecord).GreaterThanOrEqualTo(0).WithMessage("MenuExportRecord must be non-negative.");
            RuleFor(x => x.RestrictedView).GreaterThanOrEqualTo(0).WithMessage("RestrictedView must be non-negative.");
            RuleFor(x => x.MenuJSONEdit).GreaterThanOrEqualTo(0).WithMessage("MenuJSONEdit must be non-negative.");
            RuleFor(x => x.MenuSpecial1).GreaterThanOrEqualTo(0).WithMessage("MenuSpecial1 must be non-negative.");
            RuleFor(x => x.MenuSpecial2).GreaterThanOrEqualTo(0).WithMessage("MenuSpecial2 must be non-negative.");
            RuleFor(x => x.MenuSpecial3).GreaterThanOrEqualTo(0).WithMessage("MenuSpecial3 must be non-negative.");
            RuleFor(x => x.MenuSpecial4).GreaterThanOrEqualTo(0).WithMessage("MenuSpecial4 must be non-negative.");
            RuleFor(x => x.MenuSpecial5).GreaterThanOrEqualTo(0).WithMessage("MenuSpecial5 must be non-negative.");

        }
    }
}
