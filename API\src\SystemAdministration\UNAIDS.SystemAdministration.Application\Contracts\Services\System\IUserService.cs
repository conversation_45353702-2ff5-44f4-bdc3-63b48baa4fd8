using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using Microsoft.AspNetCore.Http;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IUserService<T> : IBaseAPIService<T> where T : class
    {
        Task<List<CommonLookUp>> GetSearchable(int id, string fieldName);
        Task<List<CommonLookUp>> GetRole();
        Task<List<CommonLookUp>> GetBusinessUnits();
    }
}
