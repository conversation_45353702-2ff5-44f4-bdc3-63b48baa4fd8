using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class SettingServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<ISettingRepository> _mockSettingRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly SettingService _settingService;
        private readonly Fixture _fixture;

        public SettingServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockSettingRepository = new Mock<ISettingRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<ISettingRepository>())
                           .Returns(_mockSettingRepository.Object);

            
            _settingService = new SettingService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfBusinessUnits()
        {
            // Arrange          
            var expectedBusinessUnits = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockSettingRepository.Setup(repo => repo.GetBusinessUnits())
                                        .ReturnsAsync(expectedBusinessUnits);

            // Act
            var result = await _settingService.GetBusinessUnits();

            // Assert
            Assert.Equal(expectedBusinessUnits, result);
            _mockSettingRepository.Verify(repo => repo.GetBusinessUnits(), Times.Once); 
        }

        [Fact]
        public async Task GetModules_ReturnsListOfModules()
        {
            // Arrange          
            var expectedModules = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockSettingRepository.Setup(repo => repo.GetModules())
                                        .ReturnsAsync(expectedModules);

            // Act
            var result = await _settingService.GetModules();

            // Assert
            Assert.Equal(expectedModules, result);
            _mockSettingRepository.Verify(repo => repo.GetModules(), Times.Once); 
        }

        [Fact]
        public async Task GetDataTypes_ReturnsListOfDataTypes()
        {
            // Arrange          
            var expectedDataTypes = _fixture.CreateMany<CommonLookUp>(2).ToList();
            _mockSettingRepository.Setup(repo => repo.GetDataTypes())
                                        .ReturnsAsync(expectedDataTypes);

            // Act
            var result = await _settingService.GetDataTypes();

            // Assert
            Assert.Equal(expectedDataTypes, result);
            _mockSettingRepository.Verify(repo => repo.GetDataTypes(), Times.Once); 
        }

    }
}
