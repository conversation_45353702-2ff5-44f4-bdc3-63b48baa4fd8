﻿using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Workflow.Models;
using System.Text.Json;
using UNAIDS.HivScorecards.Domain.System;

namespace UNAIDS.Core.Workflow
{
    public sealed class WorkflowRepository(SystemAdminDbContext dbContext): BaseRepository<WorkflowInstance>(dbContext), IWorkflowRepository
    {
        /// <summary>
        /// Get the workflow Name
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public async Task<string> GetWorkflowName(int workflowId)
        {
            return await dbContext.WorkflowDefinitions.AsNoTracking()
                .Where(x => x.Id == workflowId)
                .Select(x => x.Name).FirstOrDefaultAsync() ?? string.Empty;
        }

        /// <summary>
        /// Get the workflow instance
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<WorkflowInstance> GetWorkflowInstance(Guid id)
        {
            return await dbContext.WorkflowInstances.AsNoTracking()
                .Where(x => x.WorkflowInstanceId == id)
                .FirstOrDefaultAsync();
        }


        public async Task<object> GetWorkflowSteps(string workflowName)
        {
            return await (from step in dbContext.WorkflowSteps.AsNoTracking()
                          join def in dbContext.WorkflowDefinitions.AsNoTracking()
                          on step.WorkflowDefinitionId equals def.Id
                          where (string.IsNullOrEmpty(workflowName) || def.Name == workflowName)
                          select new
                          {
                              step.ReturnStatus,
                              DisplayText = step.StepName,
                              Value = step.Id,
                              Active = 1
                          }).ToListAsync();
        }

        /// <summary>
        /// Get the workflow step name
        /// </summary>
        /// <param name="stepId"></param>
        /// <returns></returns>
        public async Task<string> GetWorkflowStep(int stepId)
        {
            return await dbContext.WorkflowSteps.AsNoTracking()
                .Where(x => x.Id == stepId)
                .Select(x => x.StepName).FirstOrDefaultAsync() ?? string.Empty;
        }


        /// <summary>
        /// Get the workflow step code
        /// </summary>
        /// <param name="stepId"></param>
        /// <returns></returns>
        public async Task<string?> GetStepCode(int stepId)
        {
            return await dbContext.WorkflowSteps.AsNoTracking()
                .Where(x => x.Id == stepId)
                .Select(x => x.StepCode).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Get workflow by workflow name
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<WorkflowDefinition?> GetWorkflowByName(string name)
        {
            return await dbContext.WorkflowDefinitions.AsNoTracking()
                .Where(x => x.Name == name).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Get the initial step of a workflow
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public async Task<int> GetInitialStepId(int workflowId)
        {
            return await dbContext.WorkflowSteps.AsNoTracking()
                .Where(x => x.WorkflowDefinitionId == workflowId)
                .OrderBy(x => x.StepNo)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Get the workflow steps
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public async Task<List<WorkflowStep>> GetWorkflowSteps(int workflowId)
        {
            return await dbContext.WorkflowSteps.AsNoTracking()
                .Where(x => x.WorkflowDefinitionId == workflowId)
                .OrderBy(x => x.StepNo)
                .ToListAsync();
        }

        /// <summary>
        /// Get the current step
        /// </summary>
        /// <param name="instanceId"></param>
        /// <returns></returns>
        public async Task<WorkflowStatusInfo> GetCurrentStep(Guid instanceId)
        {
            return await (from history in dbContext.WorkflowHistories.AsNoTracking()
                          join step in dbContext.WorkflowSteps.AsNoTracking()
                          on history.ToStepId equals step.Id
                          join instance in dbContext.WorkflowInstances.AsNoTracking()
                          on history.WorkflowInstanceId equals instance.WorkflowInstanceId
                          where history.WorkflowInstanceId == instanceId
                          orderby history.CreatedDate
                          select new WorkflowStatusInfo()
                          {
                              WorkflowHistoryId = history.WorkflowHistoryId,
                              WorkFlowStepId = step.Id,
                              InstanceId = history.WorkflowInstanceId,
                              CanPause = step.CanPause,
                              ReturnStatus = instance.IsReturned ?? 0,
                              IsLocked = step.LockStatus,
                              ReturnStatusDescription = step.ReturnStatus
                          }).LastOrDefaultAsync();
        }

        public async Task<List<WorkflowJourneyHistory>> GetWorklowHistory(Guid instanceId)
        {
            return await (from instance in dbContext.WorkflowInstances.AsNoTracking()
                                     join def in dbContext.WorkflowDefinitions.AsNoTracking()
                                     on instance.WorkflowDefinitionId equals def.Id
                                     join history in dbContext.WorkflowHistories.AsNoTracking()
                                     on instance.WorkflowInstanceId equals history.WorkflowInstanceId
                                     join step in dbContext.WorkflowSteps.AsNoTracking()
                                     on history.ToStepId equals step.Id
                                     join user in dbContext.Users.AsNoTracking()
                                     on history.CreatedById equals user.Id
                                     where instance.WorkflowInstanceId == instanceId
                                     orderby history.CreatedDate
                                     select new WorkflowJourneyHistory
                                     {
                                         HistoryId = history.WorkflowHistoryId,
                                         LogDate = history.CreatedDate,
                                         IsCompleted = history.ExecutionStatus,
                                         Username = user.UserName,
                                         Comments = history.Comments,
                                         StepId = step.Id,
                                         Status = step.StepName,
                                         Action = step.Action,
                                         IsForward = step.IsForwardFlow ?? 0,
                                         ChildData = history.JsonData
                                     }).ToListAsync();
        }

        public Task<List<JsonDocument>> GetWorkflowPermissions(int workflowId, int[] roles)
        {
            throw new NotImplementedException();
        }
    }
}
