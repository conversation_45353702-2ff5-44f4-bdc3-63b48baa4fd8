using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class MenuDetailRepository(SystemAdminDbContext dbContext) : BaseRepository<MenuDetail>(dbContext), IMenuDetailRepository
    {
        public async Task<List<CommonLookUp>> GetMenuConfig()
        {
            return await dbContext.Menus.AsNoTracking()
                .Select(menuconfig => new CommonLookUp
                {
                    DisplayText = menuconfig.Title,
                    Value = menuconfig.Id,
                    Active = 1
                }).ToListAsync();
        }
    }
}