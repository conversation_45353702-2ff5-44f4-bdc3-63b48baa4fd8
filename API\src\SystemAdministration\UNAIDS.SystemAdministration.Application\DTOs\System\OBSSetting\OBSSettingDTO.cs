namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class OBSSettingDTO
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int OBSTypeId { get; set; }  // Assuming this is a foreign key reference to `LookUp`
        public int? ParentOBSTypeId { get; set; }  // Optional foreign key reference to `LookUp`
        public short IsAccountingUnit { get; set; }
        public short? IsExpenseTrackingUnit { get; set; }
        public int Level { get; set; }
        public string Hierarchy { get; set; } = string.Empty;
        public int? SchemaDefinitionId { get; set; }
        public short Active { get; set; }
    }
}
