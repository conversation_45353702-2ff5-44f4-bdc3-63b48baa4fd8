using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Model.Administration;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class RolePermissionMappingService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
                                     IMapper mapper,
                                     ISequenceRepository sequenceRepository,
        IUserContext userContext) : 
        BaseAPIService<RolePermissionMapping, SystemAdminDbContext, RolePermissionMappingDTO, IRolePermissionMappingRepository>(unitOfWork, mapper, sequenceRepository, userContext), 
        IRolePermissionMappingService<RolePermissionMappingDTO>
    {
        // Add any other methods 
    }
}