using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class UserPermissionRepository(SystemAdminDbContext dbContext) : BaseRepository<UserPermission>(dbContext), IUserPermissionRepository
    {
        public async Task<List<UserPermission>> GetPermissions(int userId)
        {
            return await dbContext.UserPermissions.AsNoTracking()
                .Where(x => x.UserId == userId)
                .ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetRole()
        {
            return await dbContext.Roles.AsNoTracking()
                .Select(x => new CommonLookUp
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                })
                .ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetUser()
        {
            return await dbContext.Users.AsNoTracking()
                .Select(x => new CommonLookUp
                {
                    DisplayText = x.UserName,
                    Value = x.Id,
                    Active = 1
                })
                .ToListAsync();
        }
        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp
                {
                    DisplayText = x.Name,
                    Value = x.Id,
                    Active = 1
                })
                .ToListAsync();
        }
    }
}