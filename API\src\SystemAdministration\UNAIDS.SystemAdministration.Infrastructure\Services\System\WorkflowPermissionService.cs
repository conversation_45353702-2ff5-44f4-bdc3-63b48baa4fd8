using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class WorkflowPermissionService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<WorkflowPermission, SystemAdminDbContext, WorkflowPermissionDTO, IWorkflowPermissionRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IWorkflowPermissionService<WorkflowPermissionDTO>
    {
        public override async Task<List<WorkflowPermissionDTO>> GetAll(Filter filter)
        {
            var permissions = await unitOfWork.Repository<IWorkflowPermissionRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id, null, null, null);
            return mapper.Map<List<WorkflowPermissionDTO>>(permissions);
        }
        /// <summary>
        /// Get UserGroups
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetUserRoles()
        {
            return await unitOfWork.Repository<IWorkflowPermissionRepository>().GetUserRoles();
        }

        public async Task<JsonDocument?> GetPermissions(int permissionId)
        {
            return await unitOfWork.Repository<IWorkflowPermissionRepository>().GetPermissions(permissionId);
        }
        public async Task<List<CommonLookUp>> GetWorkflowSteps(int wfPermissionId)
        {
            return await unitOfWork.Repository<IWorkflowPermissionRepository>().GetWorkflowSteps(wfPermissionId);
        }

        public async Task UpdateTransitions(int id, JsonDocument transitions)
        {
            await unitOfWork.Repository<IWorkflowPermissionRepository>().UpdateTransitions(id, transitions);
        }
    }
}
