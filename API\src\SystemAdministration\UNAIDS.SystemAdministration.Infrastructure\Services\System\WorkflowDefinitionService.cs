using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure
{
    public sealed class WorkflowDefinitionService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<WorkflowDefinition, SystemAdminDbContext, WorkflowDefinitionDTO, IWorkflowDefinitionRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        IWorkflowDefinitionService<WorkflowDefinitionDTO>
    {
        /// <summary>
        /// Get ModuleGroups
        /// </summary>
        /// <returns></returns>
        public async Task<List<CommonLookUp>> GetModuleGroups()
        {
            return await unitOfWork.Repository<IWorkflowDefinitionRepository>().GetModuleGroups();
        }


    }
}
