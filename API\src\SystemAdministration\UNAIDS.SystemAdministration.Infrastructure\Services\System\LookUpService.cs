using AutoMapper;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Infrastructure;

public sealed class LookUpService(IUnitOfWork<SystemAdminDbContext> unitOfWork,
        IMapper mapper,
        ISequenceRepository sequenceRepository,
        IUserContext userContext
        ) :
        BaseAPIService<LookUpInfo, SystemAdminDbContext, LookupInfoDTO, ILookUpInfoRepository>(unitOfWork, mapper, sequenceRepository, userContext),
        ILookUpService<LookupInfoDTO>
{
    public async Task<List<LookupInfoDTO>> GetAll(Filter filter)
    {
        var lookups = await unitOfWork.Repository<ILookUpInfoRepository>().GetManyAsync(x => x.LookUpTypeId == filter.Id, null, null, null);

        return mapper.Map<List<LookupInfoDTO>>(lookups);
    }

    public string AccessRights(string subject)
    {
        return userContext.AccessRights(subject);
    }

}
