using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using Microsoft.AspNetCore.Authorization;
using UNAIDS.Authorization.Shared;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.API.Controllers
{
    [Route("api/role-menu-mappings")]
    [ApiController]
    public class RoleMenuMappingController(IRoleMenuMappingService<RoleMenuMappingDTO> roleMenuMappingService, IValidator<RoleMenuMappingDTO> validator) : ControllerBase
    {
        #region GetAll...
        /// <summary>
        /// Apply filter and list RoleMenuMappings
        /// </summary>
        /// <returns></returns> 
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> GetAll([FromQuery] Filter filter)
        {
            var roleMenuMappings = await roleMenuMappingService.GetRoleMenus(filter.Id);
            var userRights = roleMenuMappingService.AccessRights(SubjectTypes.ROLE);
            return Ok(new
            {
                Record = roleMenuMappings,
                MenuType = await roleMenuMappingService.GetLookUp("ScreenType"),
                ParentMenuId = await roleMenuMappingService.GetMenuConfig(),
                UserRights = userRights.Contains("E") ? $"{userRights}BX" : userRights
            });
        }
        #endregion

        #region Get by id...
        /// <summary>
        /// Get a specific RoleMenuMapping by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns> 
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.VIEW}")]
        public async Task<IActionResult> Get(int id)
        {
            if (id == 0)
                return BadRequest();

            var roleMenuMapping = await roleMenuMappingService.GetById(id);
            return Ok(new
            {
                Record = roleMenuMapping,
                UserRights = roleMenuMappingService.AccessRights(SubjectTypes.ROLE)
            });
        }
        #endregion

        /// <summary>
        /// Create a new RoleMenuMapping
        /// </summary>
        /// <param name="roleMenuMapping"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesDefaultResponseType]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.ADD}")]
        public async Task<IActionResult> Create([FromBody] RoleMenuMappingDTO roleMenuMapping)
        {
            if (roleMenuMapping == null)
                return BadRequest();

            // Validate the DTO
            var validationResult = await validator.ValidateAsync(roleMenuMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }

            var result = await roleMenuMappingService.Create(roleMenuMapping);
            return CreatedAtAction(nameof(Create), new { id = roleMenuMapping.MenuId }, result);
        }

        /// <summary>
        /// Update an existing RoleMenuMapping
        /// </summary>
        /// <param name="roleMenuMapping"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.EDIT}")]
        public async Task<IActionResult> Update([FromBody] RoleMenuMappingDTO roleMenuMapping)
        {
            var validationResult = await validator.ValidateAsync(roleMenuMapping);
            if (!validationResult.IsValid)
            {
                return ValidationProblem(new ValidationProblemDetails(validationResult.ToDictionary()));
            }
            await roleMenuMappingService.Update(roleMenuMapping);
            return Ok(roleMenuMapping);
        }

        /// <summary>
        /// Update an existing RoleMenuMapping
        /// </summary>
        /// <param name="roleMenuMapping"></param>
        /// <returns></returns>
        [HttpPost("bulk")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.BULK_UPDATE}")]
        public async Task<IActionResult> BulkUpdate([FromBody] List<RoleMenuMappingDTO> roleMenuMappings)
        {
            await roleMenuMappingService.BulkUpdate(roleMenuMappings);
            return Ok(roleMenuMappings);
        }

        /// <summary>
        /// Delete a RoleMenuMapping by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns> 
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [Authorize($"{SubjectTypes.ROLE}.{ActionAliases.DELETE}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (id == 0)
                return BadRequest();

            await roleMenuMappingService.Delete(id);
            return Ok();
        }

    }
}
