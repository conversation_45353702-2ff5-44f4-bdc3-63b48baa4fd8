using Microsoft.EntityFrameworkCore;
using UNAIDS.Authentication.Shared;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class UserAssignedPermissionRepository(SystemAdminDbContext dbContext)
        : BaseRepository<User>(dbContext), IUserAssignedPermissionRepository
    {

        public async Task<List<CommonLookUp>> GetUserAssignedPermissions(Filter filter)
        {
            var query = (from user in dbContext.Users.AsNoTracking()
                         join usr in dbContext.UserPermissions.AsNoTracking() on user.Id equals usr.UserId
                         join rol in dbContext.Roles.AsNoTracking() on usr.RoleId equals rol.Id
                         join menuMaster in dbContext.RoleMenuMasters.AsNoTracking() on usr.RoleId equals menuMaster.RoleId
                         join rolemenu in dbContext.RoleMenuMappings.AsNoTracking()
                             on menuMaster.Id equals (rolemenu.RoleMenuMasterId ?? 0)
                         join mod in dbContext.Modules.AsNoTracking() on menuMaster.ModuleId equals mod.Id
                         join modGroup in dbContext.ModuleGroups.AsNoTracking() on mod.ModuleGroupId equals modGroup.Id
                         join menus in dbContext.Menus.AsNoTracking() on rolemenu.MenuId equals menus.Id
                         join lookup in dbContext.LookUpInfo.AsNoTracking() on menus.MenuType equals lookup.Id
                         join lookuptype in dbContext.LookUpTypes.AsNoTracking() on lookup.LookUpTypeId equals lookuptype.Id
                         where lookuptype.FieldName == "ScreenType"
                            && menus.IsDeleted == false
                            && ((usr.ModuleGroupId == 0 && usr.ModuleId == 0) ? true : (usr.ModuleId == menuMaster.ModuleId))
                            && usr.EffectiveFrom <= DateTime.Now
                            && (usr.EffectiveTo == null || usr.EffectiveTo >= DateTime.Now)
                            && usr.IsDeleted == false
                            && mod.IsDeleted == false
                            && user.IsActive == true
                            && modGroup.IsDeleted == false
                            && ((usr.BusinessUnitId == menuMaster.BusinessUnitId) ||
                                (menuMaster.BusinessUnitId == 0) ||
                                (usr.BusinessUnitId == 0))
                         select new
                         {
                             UserId = user.Id,
                             Id = usr.UserId,
                             LoginName = user.UserName + "(" + user.LoginName + ")",
                             BusinessUnitId = menuMaster.BusinessUnitId,
                             Role = rol.Id,
                             ModuleGroup = modGroup.Name,
                             Module = mod.Id,
                             ScreenType = lookup.Name,
                             ScreenName = menus.Id,
                             MenuView = rolemenu.MenuView ?? 0,
                             MenuAdd = rolemenu.MenuAdd ?? 0,
                             MenuDelete = rolemenu.MenuDelete ?? 0,
                             MenuModify = rolemenu.MenuModify ?? 0,
                             RestrictedView = rolemenu.RestrictedView ?? 0,
                             MenuImport = rolemenu.MenuImport ?? 0,
                             MenuExport = rolemenu.MenuExport ?? 0,
                             MenuBulkUpdate = rolemenu.MenuBulkUpdate ?? 0,
                             MenuBulkDelete = rolemenu.MenuBulkDelete ?? 0,
                             MenuSpecial1 = rolemenu.MenuSpecial1 ?? 0,
                             MenuSpecial2 = rolemenu.MenuSpecial2 ?? 0,
                             MenuSpecial3 = rolemenu.MenuSpecial3 ?? 0,
                             MenuSpecial4 = rolemenu.MenuSpecial4 ?? 0,
                             MenuSpecial5 = rolemenu.MenuSpecial5 ?? 0
                         }).Distinct().ToListAsync();

            // Execute query immediately and store results in memory
            var queryResult = await query;

            // Handle advanced search manually if criteria provided
            if (!string.IsNullOrEmpty(filter.JsonAdvancedSearchFilterCriteria))
            {
                // Example: Simple filtering (customize this as needed)
                queryResult = queryResult
                                .Where(x => x.LoginName.Contains("example")) // Customize filtering
                                .ToList();
            }

            // Handle pagination manually if specified
            if (filter.PageSize != 0)
            {
                int pageSize = filter.PageSize ?? 10;
                int pageIndex = filter.StartIndex ?? 0;
                queryResult = queryResult
                                .Skip(pageIndex)
                                .Take(pageSize)
                                .ToList();
            }

            // Convert results to CommonLookUp objects
            var result = queryResult.Select(x => new CommonLookUp
            {
                Value = x.Id,
                DisplayText = x.LoginName,
                Active = 1,
                // Map additional fields if needed
            }).ToList();

            return result;
        }



        // public async Task<List<CommonLookUp>> GetUserAssignedPermissions(Filter filter)
        // {
        //     var query = (from user in dbContext.Users.AsNoTracking()
        //                  join usr in dbContext.UserPermissions.AsNoTracking() on user.Id equals usr.UserId
        //                  join rol in dbContext.Roles.AsNoTracking() on usr.RoleId equals rol.Id
        //                  join menuMaster in dbContext.RoleMenuMasters.AsNoTracking() on usr.RoleId equals menuMaster.RoleId
        //                  join rolemenu in dbContext.RoleMenuMappings.AsNoTracking()
        //                      on menuMaster.Id equals rolemenu.RoleMenuMasterId ?? 0
        //                  join mod in dbContext.Modules.AsNoTracking() on menuMaster.ModuleId equals mod.Id
        //                  join modGroup in dbContext.ModuleGroups.AsNoTracking() on mod.ModuleGroupId equals modGroup.Id
        //                  join menus in dbContext.Menus.AsNoTracking() on rolemenu.MenuId equals menus.Id
        //                  join lookup in dbContext.LookUpInfo.AsNoTracking() on menus.MenuType equals lookup.Id
        //                  join lookuptype in dbContext.LookUpTypes.AsNoTracking() on lookup.LookUpTypeId equals lookuptype.Id
        //                  where lookuptype.FieldName == "ScreenType" && menus.IsDeleted == false
        //                  && ((usr.ModuleGroupId == 0 && usr.ModuleId == 0) ? true : (usr.ModuleId == menuMaster.ModuleId))
        //                  && usr.EffectiveFrom <= DateTime.Now
        //                  && (usr.EffectiveTo == null || usr.EffectiveTo >= DateTime.Now)
        //                  && usr.IsDeleted == false && mod.IsDeleted == false && user.IsActive == true
        //                  && modGroup.IsDeleted == false
        //                  && ((usr.BusinessUnitId == menuMaster.BusinessUnitId) || (menuMaster.BusinessUnitId == 0) || (usr.BusinessUnitId == 0))
        //                  select new
        //                  {
        //                      UserId = user.Id,
        //                      Id = usr.UserId,
        //                      LoginName = user.UserName + "(" + user.LoginName + ")",
        //                      BusinessUnitId = menuMaster.BusinessUnitId,
        //                      Role = rol.Id,
        //                      ModuleGroup = modGroup.Name,
        //                      Module = mod.Id,
        //                      ScreenType = lookup.Name,
        //                      ScreenName = menus.Id,
        //                      MenuView = rolemenu.MenuView ?? 0,
        //                      MenuAdd = rolemenu.MenuAdd ?? 0,
        //                      MenuDelete = rolemenu.MenuDelete ?? 0,
        //                      MenuModify = rolemenu.MenuModify ?? 0,
        //                      RestrictedView = rolemenu.RestrictedView ?? 0,
        //                      MenuImport = rolemenu.MenuImport ?? 0,
        //                      MenuExport = rolemenu.MenuExport ?? 0,
        //                      MenuBulkUpdate = rolemenu.MenuBulkUpdate ?? 0,
        //                      MenuBulkDelete = rolemenu.MenuBulkDelete ?? 0,
        //                      MenuSpecial1 = rolemenu.MenuSpecial1 ?? 0,
        //                      MenuSpecial2 = rolemenu.MenuSpecial2 ?? 0,
        //                      MenuSpecial3 = rolemenu.MenuSpecial3 ?? 0,
        //                      MenuSpecial4 = rolemenu.MenuSpecial4 ?? 0,
        //                      MenuSpecial5 = rolemenu.MenuSpecial5 ?? 0
        //                  }).Distinct();

        //     // Handle pagination if specified
        //     if (filter.PageSize != 0)
        //     {
        //         string sort = string.IsNullOrEmpty(filter.Sorting) ? "LoginName ASC" : filter.Sorting;
        //         int pageSize = filter.PageSize ?? 10;
        //         int pageIndex = filter.StartIndex ?? 0;

        //         var orderedResult = query.OrderBy(sort);
        //         var totalRecords = query.Count();

        //         if (!string.IsNullOrEmpty(filter.JsonAdvancedSearchFilterCriteria))
        //         {
        //             var searchResult = QueryBuilder.AdvancedSearchQueryBuilder(orderedResult, filter.JsonAdvancedSearchFilterCriteria, dbContext);
        //             totalRecords = searchResult.Count();
        //             query = await searchResult.Skip(pageIndex).Take(pageSize).ToListAsync();
        //         }
        //         else
        //         {
        //             query = await orderedResult.Skip(pageIndex).Take(pageSize).ToListAsync();
        //         }
        //     }
        //     else
        //     {
        //         query = await query.ToListAsync();
        //     }

        //     // Convert query results to CommonLookUp objects
        //     var result = query.Select(x => new CommonLookUp
        //     {
        //         Value = x.Id,
        //         DisplayText = x.LoginName,
        //         Active = 1,
        //         // Map other properties as needed for your LookUp structure
        //     }).ToList();

        //     return result;
        // }

        public async Task<List<CommonLookUp>> GetBusinessUnits()
        {
            return await dbContext.BusinessUnits.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetUserNames()
        {
            return await dbContext.Users.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.UserName, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetScreenName()
        {
            return await dbContext.Menus.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.Title, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetModule()
        {
            return await dbContext.Modules.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }

        public async Task<List<CommonLookUp>> GetRole()
        {
            return await dbContext.Roles.AsNoTracking()
                .Select(x => new CommonLookUp { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
        }
    }
}
