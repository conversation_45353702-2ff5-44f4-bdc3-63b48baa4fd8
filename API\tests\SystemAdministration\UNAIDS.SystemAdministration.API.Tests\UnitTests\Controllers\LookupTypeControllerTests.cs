using Moq;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.API.Controllers;
using Microsoft.AspNetCore.Mvc;
using FluentValidation;
using AutoFixture;
using UNAIDS.Core.Base;
using FluentValidation.Results;


namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{
    public class LookUpTypeControllerTests
    {
        private readonly Mock<ILookUpTypeService<LookupTypeDTO>> _mockService;

        private readonly Mock<IValidator<LookupTypeDTO>> _mockValidator;
        private readonly LookUpTypeController _controller;
        private readonly Fixture _fixture;


        public LookUpTypeControllerTests()
        {
            _mockService = new Mock<ILookUpTypeService<LookupTypeDTO>>();
            _mockValidator = new Mock<IValidator<LookupTypeDTO>>();
            _controller = new LookUpTypeController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();

        }

        [Fact]
        public async Task GetAll_ReturnsOkResult_WithExpectedData()
        {
            //Arrange
            var filter = _fixture.Create<Filter>();
            var lookupTypes = _fixture.Create<List<LookupTypeDTO>>();
            var expectedBusinessUnits = _fixture.Create<List<CommonLookUp>>();
            var expectedModules = _fixture.Create<List<CommonLookUp>>();

            _mockService.Setup(s => s.GetAll(filter)).ReturnsAsync(lookupTypes);
            _mockService.Setup(s => s.GetBusinessUnits()).ReturnsAsync(expectedBusinessUnits);
            _mockService.Setup(s => s.GetModules()).ReturnsAsync(expectedModules);

            //Act
            var result = await _controller.GetAll(filter);

            //Assert
            var actionResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);
            var businessUnits = returnValue.GetType().GetProperty("BusinessUnitId")?.GetValue(returnValue);
            var modules = returnValue.GetType().GetProperty("ModuleId")?.GetValue(returnValue);

            Assert.NotNull(result);
            Assert.Equal(lookupTypes, recordValue);
            Assert.Equal(expectedBusinessUnits, businessUnits);
            Assert.Equal(expectedModules, modules);
        }


        [Fact]
        public async Task Get_ReturnsBadRequest_WhenIdIsZero()
        {
            // Act 
            var result = await _controller.Get(0);
            // Assert
            Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Get_ReturnsOkResult_WhenIdIsValid()
        {
            // Arrange 
            int id = 1; 
            var lookupType = _fixture.Create<LookupTypeDTO>();

            _mockService.Setup(s => s.GetById(id)).ReturnsAsync(lookupType);

            // Act 
            var result = await _controller.Get(id);

            // Assert 
            Assert.NotNull(result);

             _mockService.Verify(service => service.GetById(id), Times.Once);
            

            var actionResult = Assert.IsType<OkObjectResult>(result); 
            var returnValue = actionResult.Value; 

            var recordValue = returnValue.GetType().GetProperty("Record")?.GetValue(returnValue);

            Assert.Equal(lookupType, recordValue); 

           
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenInputIsNull()
        {
            // Act 
            var result = await _controller.Create(null);

            // Assert 
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<LookupTypeDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<LookupTypeDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedResult_WhenInputIsValid()
        { 
            // Arrange
            var lookupType = _fixture.Create<LookupTypeDTO>();
            var validationResult = new ValidationResult();

            _mockValidator.Setup(v => v.ValidateAsync(lookupType, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(lookupType)).ReturnsAsync(lookupType);

            // Act
            var result = await _controller.Create(lookupType);

            // Assert
             var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(lookupType, createdAtActionResult.Value);
        }
        
        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails() {
            // Arrange
            var invalidProgram = _fixture.Create<LookupTypeDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<LookupTypeDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Update(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);

        }
        
        [Fact]
        public async Task Update_ReturnsOkResult_WhenInputIsValid()
        { 
            // Arrange
            var lookupType = _fixture.Create<LookupTypeDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(lookupType, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Update(lookupType)).ReturnsAsync(lookupType);

            // Act
            var result = await _controller.Update(lookupType);

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsBadRequest_WhenIdIsZero()
        {
            // Act 
            var result = await _controller.Delete(0);
            // Assert 
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task Delete_ReturnsOkResult_WhenIdIsValid()
        { 
            // Arrange
            var lookupType = _fixture.Create<LookupTypeDTO>();
           _mockService.Setup(s => s.Delete(lookupType.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(lookupType.Id);

            // Assert
            Assert.IsType<OkResult>(result);
        }

    }
}