﻿
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.Core.Shared;
using System.Security.Claims;
using System.Text.Json;

namespace UNAIDS.HivScorecards.Infrastructure.Persistence
{
    public class ClaimRepository(CommonDbContext dbContext): IClaimRepository
    {
        public async Task<Dictionary<string, string>> GetUserClaims(string email, int tenantId)
        {
            Dictionary<string, string> claims = new();

            if (!string.IsNullOrEmpty(email)) 
            {
                var userInfo = await dbContext.Users.AsNoTracking()
                    .Where(x => x.Email == email).FirstOrDefaultAsync();

                if(userInfo != null)
                {
                    claims.Add(ClaimTypes.NameIdentifier, userInfo.Id.ToString());
                    claims.Add(ClaimTypes.GivenName, userInfo.UserName.ToString());
                    claims.Add(ClaimTypes.Email, userInfo.Email.ToString());

                    var userRoles = await dbContext.UserPermissions.AsNoTracking()
                    .Where(x => x.UserId == userInfo.Id && x.BusinessUnitId == tenantId)
                    .Select(x => x.RoleId).ToListAsync();

                    if(userInfo.RoleId > 0)
                        userRoles.Add(userInfo.RoleId ?? 0);

                    claims.Add(CustomClaimTypes.Roles, JsonSerializer.Serialize(userRoles));

                    var permissions = await (from perm in dbContext.RoleMenuMappings.AsNoTracking()
                                       join menu in dbContext.Menus.AsNoTracking()
                                       on perm.MenuId equals menu.Id
                                       join rm in dbContext.RoleMenuMasters.AsNoTracking()
                                       on perm.RoleMenuMasterId equals rm.Id
                                       where menu.Controller != null && userRoles.Contains(rm.RoleId)
                                       && (EF.Property<int?>(perm, "TenantId") == tenantId) && perm.MenuView == 1
                                       select new
                                       {
                                           menu.Controller,
                                           perm.MenuAdd,
                                           perm.MenuModify,
                                           perm.MenuDelete,
                                           perm.MenuBulkImport
                                       })
                                      .GroupBy(x => x.Controller)
                                      .Select(x => new
                                      {
                                          Subject = x.Key,
                                          Add = x.Max(y => y.MenuAdd),
                                          Edit = x.Max(y => y.MenuModify),
                                          Delete = x.Max(y => y.MenuDelete),
                                          Bulk = x.Max(y => y.MenuBulkImport)
                                      }).ToListAsync();

                    claims.Add(CustomClaimTypes.Permissions, JsonSerializer.Serialize(permissions
                        .ToDictionary(x => x.Subject,
                        x => $"{(x.Add == 1 ? "A" : "")}{(x.Edit == 1 ? "E" : "")}{(x.Delete == 1 ? "D" : "")}{(x.Bulk == 1 ? "X" : "")}")));

                }                
            
            }

            return claims;
        }
    }
}
