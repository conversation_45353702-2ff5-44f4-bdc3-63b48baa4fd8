using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IWorkflowPermissionRepository : IBaseRepository<WorkflowPermission>
    {
        Task UpdateTransitions(int id, JsonDocument transitions);
        Task<List<CommonLookUp>> GetUserRoles();
        Task<JsonDocument?> GetPermissions(int permissionId);
        Task<List<CommonLookUp>> GetWorkflowSteps(int wfPermissionId);
    }
}
