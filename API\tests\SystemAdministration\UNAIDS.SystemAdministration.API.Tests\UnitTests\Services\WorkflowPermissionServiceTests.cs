using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain;
using System.Text.Json;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class WorkflowPermissionServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IWorkflowPermissionRepository> _mockWorkflowPermissionRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly WorkflowPermissionService _workflowPermissionService;
        private readonly Fixture _fixture;

        public WorkflowPermissionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockWorkflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IWorkflowPermissionRepository>())
                           .Returns(_mockWorkflowPermissionRepository.Object);

            
            _workflowPermissionService = new WorkflowPermissionService(
                _mockUnitOfWork.Object,
                _mockMapper.Object,
                _mockSequenceRepository.Object,
                _mockUserContext.Object
            );

            _fixture = new Fixture();
            
        }

        [Fact]
        public async Task GetAll_ReturnsMappedPermissions()
        {
            // Arrange
            _fixture.Customize<WorkflowPermission>(c => c.With(dto => dto.StepTransitions, JsonDocument.Parse("{}")));
            _fixture.Customize<WorkflowPermissionDTO>(c => c.With(dto => dto.StepTransitions, JsonDocument.Parse("{}")));

            var filter = _fixture.Create<Filter>();

            var workflowPermissions = _fixture.CreateMany<WorkflowPermission>().ToList();
            var workflowPermissionDTOs = _fixture.CreateMany<WorkflowPermissionDTO>(workflowPermissions.Count).ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IWorkflowPermissionRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id,null, null, null))
                        .ReturnsAsync(workflowPermissions);

            _mockMapper.Setup(m => m.Map<List<WorkflowPermissionDTO>>(workflowPermissions))
                    .Returns(workflowPermissionDTOs);

            // Act
            var result = await _workflowPermissionService.GetAll(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(workflowPermissionDTOs, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IWorkflowPermissionRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id, null, null, null), Times.Once);

            _mockMapper.Verify(m => m.Map<List<WorkflowPermissionDTO>>(workflowPermissions), Times.Once);
        }
        
        [Fact]
        public async Task GetUserRoles_ReturnsListOfUserRoles()
        {
            // Arrange   
            var expectedUserRoles = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockWorkflowPermissionRepository.Setup(repo => repo.GetUserRoles())
                                        .ReturnsAsync(expectedUserRoles);

            // Act
            var result = await _workflowPermissionService.GetUserRoles();

            // Assert
            Assert.Equal(expectedUserRoles, result);

            _mockWorkflowPermissionRepository.Verify(repo => repo.GetUserRoles(), Times.Once); 
        }

        [Fact]
        public async Task GetPermissions_ReturnsListOfPermissions()
        {
            // Arrange   
            int id=1;
            var expectedPermissions = JsonDocument.Parse("{}");

            _mockWorkflowPermissionRepository.Setup(repo => repo.GetPermissions(id)).ReturnsAsync(expectedPermissions);

            // Act
            var result = await _workflowPermissionService.GetPermissions(id);

            // Assert
            Assert.Equal(expectedPermissions, result);

            _mockWorkflowPermissionRepository.Verify(repo => repo.GetPermissions(id), Times.Once); 
        }

        [Fact]
        public async Task GetWorkflowSteps_ReturnsListOfWorkflowSteps()
        {
            // Arrange  
            int id=0; 
            var expectedWorkflowSteps = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockWorkflowPermissionRepository.Setup(repo => repo.GetWorkflowSteps(id)).ReturnsAsync(expectedWorkflowSteps);

            // Act
            var result = await _workflowPermissionService.GetWorkflowSteps(id);

            // Assert
            Assert.Equal(expectedWorkflowSteps, result);

            _mockWorkflowPermissionRepository.Verify(repo => repo.GetWorkflowSteps(id), Times.Once); 
        }

        [Fact]
        public async Task UpdateTransitions_updatesTransitions()
        {
            // Arrange  
            int id=0; 
            var transitions = JsonDocument.Parse("{}");
            
            _mockUnitOfWork.Setup(uow => uow.Repository<IWorkflowPermissionRepository>()
                                     .UpdateTransitions(id, transitions))
                   .Returns(Task.CompletedTask);

            // Act
            await _workflowPermissionService.UpdateTransitions(id, transitions);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.Repository<IWorkflowPermissionRepository>().UpdateTransitions(id, transitions), Times.Once);
        }

    }
}
