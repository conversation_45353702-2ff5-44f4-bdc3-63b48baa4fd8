using Microsoft.AspNetCore.Mvc;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain.System;
using UNAIDS.SystemAdministration.Application.DTOs;

namespace UNAIDS.SystemAdministration.Application
{
    public interface IMenuConfigRepository : IBaseRepository<Menu>
    {
        Task<List<CommonLookUp>> GetMenuConfig();
        Task<List<MCHierarchyDTO>> GetMenuHierarchy();
        Task<List<CommonLookUp>> GetModule();
        Task<List<CommonLookUp>> GetLookUp(string fieldName, bool flag = false);
        Task<List<CommonLookUp>> GetObjectTypes(string fieldName);
        Task<List<CommonLookUp>> GetMenuConfigDetails();
    }
}