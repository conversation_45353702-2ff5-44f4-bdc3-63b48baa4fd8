﻿using Azure.Identity;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace UNAIDS.Core.DocumentRepository
{
    public static class DependencyInjection
    {
        public static void AddStorage(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            var storageType = configuration.GetSection("Storage:Type").Value;

            serviceCollection.AddAutoMapper(typeof(DocumentRepositoryMappingProfile))
            .AddTransient(typeof(IDocumentStoreRepository), typeof(DocumentStoreRepository))
            .AddTransient(typeof(IDocumentStoreDetailRepository), typeof(DocumentStoreDetailRepository));

            switch (storageType)
            {
                case "Blob":
                default:
                    serviceCollection.AddAzureClients(builder =>
                    {
                        // Add a storage account client
                        builder.AddBlobServiceClient(configuration.GetSection("Storage"));

                        // Use the environment credential by default
                        builder.UseCredential(new DefaultAzureCredential());
                    });
                    // document repo...
                    serviceCollection.AddScoped<IDocumentRepository, BlobDocumentRepository>();
                    break;
                case "File-System":
                    // document repo...
                    serviceCollection.AddScoped<IDocumentRepository, LocalDocumentRepository>();
                    break;

            }
        }
    }
}
