using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.Base;
using UNAIDS.HivScorecards.Domain;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;

namespace UNAIDS.SystemAdministration.Infrastructure.Persistence
{
    public sealed class WorkflowStepRepository(SystemAdminDbContext dbContext) : BaseRepository<WorkflowStep>(dbContext), IWorkflowStepRepository
    {
        public async Task<List<CommonLookUp>> GetSchemaDefinition()
        {
            return await dbContext.Modules.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.Name, Value = x.Id, Active = 1 }).ToListAsync();
                
        }

        public async Task<List<CommonLookUp>> GetWorkflowServiceName(int id)
        {

            return await dbContext.WorkflowSteps.AsNoTracking()
                .Select(x => new CommonLookUp() { DisplayText = x.StepName, Value = x.Id, Active = 1 }).ToListAsync();
            
        }
    }
}