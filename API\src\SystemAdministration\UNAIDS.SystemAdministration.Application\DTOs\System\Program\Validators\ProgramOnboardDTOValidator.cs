﻿using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class ProgramOnboardDTOValidator : AbstractValidator<ProgramOnboardDTO>
    {
        private readonly TenantManagementDbContext _dbContext;
        public ProgramOnboardDTOValidator(TenantManagementDbContext dbContext)
        {
            _dbContext = dbContext;

            RuleFor(c => c.Code).Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be blank.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MustAsync(async (program, code, cancellation) => await IsUniqueCode(program.Id, code))
                .WithMessage("The Code already exists."); 

            RuleFor(c => c.Name).Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be blank.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MustAsync(async (program, name, cancellation) => await IsUniqueName(program.Id, name))
                .WithMessage("The Name already exists.");

            //RuleFor(c => c.ProgramSetup.Features).Must(list => list.Count > 0)
            //    .WithMessage("The program must contain atleast one feature");
        }

        private async Task<bool> IsUniqueCode(int id, string code)
        {
            return !await _dbContext.Programs.AsNoTracking()
                .AnyAsync(r => r.Code!= null && r.Code.Trim().ToUpper() == code.Trim().ToUpper() && r.Id != id);
        }

        private async Task<bool> IsUniqueName(int id, string name)
        {
            return !await _dbContext.Programs.AsNoTracking()
                .AnyAsync(r => r.Name != null && r.Name.Trim().ToUpper() == name.Trim().ToUpper() && r.Id != id);
        }

    }
}
