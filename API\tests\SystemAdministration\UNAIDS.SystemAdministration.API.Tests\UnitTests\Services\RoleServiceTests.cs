using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class RoleServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IRoleRepository> _mockRoleRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly RoleService _roleService;
        private readonly Fixture _fixture;

        public RoleServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockRoleRepository = new Mock<IRoleRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IRoleRepository>())
                           .Returns(_mockRoleRepository.Object);

            
            _roleService = new RoleService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }
        [Fact]
        public async Task GetCurrentStep_ReturnsListOfCurrentStep()
        {
            // Arrange      
            int id=1;    
            var expectedCurrentStep = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockRoleRepository.Setup(repo => repo.GetCurrentStep(id))
                                        .ReturnsAsync(expectedCurrentStep);

            // Act
            var result = await _roleService.GetCurrentStep(id);

            // Assert
            Assert.Equal(expectedCurrentStep, result);

            _mockRoleRepository.Verify(repo => repo.GetCurrentStep(id), Times.Once); 
        }

    }
}
