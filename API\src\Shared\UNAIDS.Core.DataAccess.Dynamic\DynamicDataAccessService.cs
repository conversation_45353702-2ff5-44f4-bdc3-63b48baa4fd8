﻿using Dapper;
using Microsoft.EntityFrameworkCore;
using UNAIDS.Core.DataAccess.Dynamic.QueryBuilder;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using System.Data;
using System.Linq.Dynamic.Core;
using System.Text.Json;

namespace UNAIDS.Core.DataAccess.Dynamic
{
    public sealed class DynamicDataAccessService(IDbConnection dbConnection, MigrationDbContext dbContext) : IDynamicDataAccessService
    {
        public async Task<List<T>> GetAllAsync<T>(Query objQuery)
        {
            DynamicParameters parameters = new();

            var entity = GetDbEntity(objQuery.Entity);

            var q = entity.BuildQuery( objQuery, ref parameters);

            return [.. await dbConnection.QueryAsync<T>(q, parameters)];
        }


        public async Task<List<dynamic>> GetAllAsync(Query objQuery)
        {
            DynamicParameters parameters = new();

            var entity = GetDbEntity(objQuery.Entity);

            var q = entity.BuildQuery( objQuery, ref parameters);
            
             var rows = (await dbConnection.QueryAsync(q, parameters))
                    .Select(row =>
                    {
                        var dict = ((IDictionary<string, object>)row)
                            .ToDictionary(k => k.Key, v => v.Value);

                        // Convert JSONB columns to JsonDocument dynamically
                        foreach (var prop in entity.Properties)                        
                            if (prop.Type.Equals("JsonDocument", StringComparison.OrdinalIgnoreCase) 
                                && dict.TryGetValue(prop.Name, out object? value) && value is string jsonString)                            
                                    dict[prop.Name] = JsonDocument.Parse(jsonString);

                        return dict;
                    })
                    .ToDynamicList();

            return rows;
        }

        // public async Task<T> GetFirstOrDefaultAsync<T>(Query objQuery)
        // {
        //     DynamicParameters parameters = new();

        //     var entity = GetDbEntity(objQuery.Entity);

        //     var q = entity.BuildQuery( objQuery, ref parameters);

        //     return [.. await dbConnection.QueryFirstOrDefaultAsync<T>(q, parameters)];
        // }

        public async Task<dynamic> GetFirstOrDefaultAsync(Query objQuery)
        {
            DynamicParameters parameters = new();

            var entity = GetDbEntity(objQuery.Entity);

            var q = entity.BuildQuery(objQuery, ref parameters);

            var row = await dbConnection.QueryFirstOrDefaultAsync(q, parameters);

            if (row == null) return null;

            var dict = ((IDictionary<string, object>)row)
                .ToDictionary(k => k.Key, v => v.Value);

            // Convert JSONB columns to JsonDocument dynamically
            foreach (var prop in entity.Properties)
                if (prop.Type.Equals("JsonDocument", StringComparison.OrdinalIgnoreCase) 
                    && dict.TryGetValue(prop.Name, out object? value) && value is string jsonString)
                        dict[prop.Name] = JsonDocument.Parse(jsonString);

            return dict;
        }

        private DbEntity GetDbEntity(string entity)
        {
            DbEntity dbEntity = new();

            var entityType = dbContext.Model.GetEntityTypes().FirstOrDefault(x => x.ClrType.Name == entity);

            if (entityType == null) throw new ApplicationException("Entity Not Found");

            dbEntity.Name = Convert.ToString(entityType?.GetAnnotation("Relational:TableName").Value);
            dbEntity.Schema = entityType?.GetSchema();

            // Retrieve all properties
            dbEntity.Properties = new List<EntityProperties>();
            foreach (var property in entityType.GetProperties())
            {
                Type propertyType = property.ClrType;

                // Check if the property is nullable
                Type? underlyingType = Nullable.GetUnderlyingType(propertyType);
                string typeName = underlyingType != null ? underlyingType.Name : propertyType.Name;

                dbEntity.Properties.Add(new EntityProperties() { Name = property.Name, Type = typeName });
            }

            return dbEntity;
        }
    }
}
