﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Repositories\Bases\**" />
    <EmbeddedResource Remove="Repositories\Bases\**" />
    <None Remove="Repositories\Bases\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\UNAIDS.Core.Shared\UNAIDS.Core.Shared.csproj" />
    <ProjectReference Include="..\..\Shared\UNAIDS.HivScorecards.Infrastructure.Persistence\UNAIDS.HivScorecards.Infrastructure.Persistence.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Application\UNAIDS.SystemAdministration.Application.csproj" />
    <ProjectReference Include="..\UNAIDS.SystemAdministration.Domain\UNAIDS.SystemAdministration.Domain.csproj" />
  </ItemGroup>

</Project>
