using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.Core.Base.Contracts;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class RoleDTOValidator : AbstractValidator<RoleDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        private readonly IUserContext _userContext;
        public RoleDTOValidator(SystemAdminDbContext dbContext, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;

            // Code: Required and unique, max length of 50
            RuleFor(x => x.Code)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("The Code is required.")
                .MaximumLength(50).WithMessage("The Code must be at most 50 characters long.")
                .MustAsync(async (role, code, cancellation) => await IsUniqueCode(role.Id, code))
                .WithMessage("The Code already exists.");

            // Name: Required and max length of 100
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("The Name is required.")
                .MaximumLength(100).WithMessage("The Name must be at most 100 characters long.");

            // Level: Greater than 0
            RuleFor(x => x.Level)
                .GreaterThan(0).WithMessage("The Level must be greater than 0.");

            // Validate Level relation
            RuleFor(x => x.Level)
                .MustAsync(async (role, level, cancellation) => await IsValidLevel(level))
                .WithMessage(role => $"The Level must be greater than or equal to the current user's minimum level.");

        }

        private async Task<bool> IsUniqueCode(int id, string code)
        {
            return !await _dbContext.Roles.AsNoTracking()
                .AnyAsync(r => r.Code.Trim().ToUpper() == code.Trim().ToUpper() && r.Id != id);
        }

        private async Task<bool> IsValidLevel(decimal level)
        {
            var currentUserId = _userContext?.Id ?? 0;

            if (currentUserId == 0)
                throw new InvalidOperationException("User context is not set.");

            var currentUserLevel = await _dbContext.Roles.AsNoTracking()
                .Where(r => r.Id == currentUserId)
                .Select(r => r.Level)
                .FirstOrDefaultAsync();

            return level >= currentUserLevel;
        }
    }
}
