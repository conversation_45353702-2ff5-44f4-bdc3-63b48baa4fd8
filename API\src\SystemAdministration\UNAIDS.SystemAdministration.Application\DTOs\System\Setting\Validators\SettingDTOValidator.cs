using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class SettingDTOValidator : AbstractValidator<SettingDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public SettingDTOValidator(SystemAdminDbContext dbcontext)
        {
            _dbContext = dbcontext;

            // Validate Code: cannot be null or empty
            RuleFor(s => s.Code)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(100).WithMessage("{PropertyName} must not exceed 100 characters.");

            // Validate Criteria: cannot be null or empty
            RuleFor(s => s.Criteria)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MaximumLength(200).WithMessage("{PropertyName} must not exceed 200 characters.");

            // Validate DataTypeId: Must not be -1
            RuleFor(s => s.DataTypeId)
                .NotEqual(-1).WithMessage("The Data Type is required.");

            // Validate Value: cannot be null or empty
            RuleFor(s => s.Value)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.");

            // Validate for duplicate records in the database
            RuleFor(s => s)
                .MustAsync(async (setting, cancellation) =>
                {
                    // Check for duplicate records
                    var isDuplicate = await _dbContext.Settings.AsNoTracking()
                        .AnyAsync(a => a.ModuleId == setting.ModuleId
                                       && a.Code == setting.Code
                                       && a.Criteria == setting.Criteria
                                       && a.Id != setting.Id);
                    return !isDuplicate;
                })
                .WithMessage("Record already exists.");
        }
    }
}
