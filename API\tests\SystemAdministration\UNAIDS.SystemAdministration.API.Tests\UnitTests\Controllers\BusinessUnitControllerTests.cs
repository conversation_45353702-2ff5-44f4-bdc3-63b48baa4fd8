using Moq;
using FluentValidation;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.API.Controllers;
using Microsoft.AspNetCore.Mvc;
using FluentValidation.Results;
using AutoFixture;
using UNAIDS.Core.Base;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Controllers
{

    public class BusinessUnitControllerTests
    {
        private readonly Mock<IBusinessUnitService<BusinessUnitDTO>> _mockService;
        private readonly Mock<IValidator<BusinessUnitDTO>> _mockValidator;
        private readonly BusinessUnitController _controller;
        private readonly Fixture _fixture;

        public BusinessUnitControllerTests()
        {
            //Initialize the mock services and controller
            _mockService = new Mock<IBusinessUnitService<BusinessUnitDTO>>();
            _mockValidator = new Mock<IValidator<BusinessUnitDTO>>();
            _controller = new BusinessUnitController(_mockService.Object, _mockValidator.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Create_ReturnsBadRequest_WhenBusinessUnitIsNull()
        {
            // Arrange
            BusinessUnitDTO? businessUnit = null;

            // Act
            var result = await _controller.Create(businessUnit);

            // Assert
            Assert.IsType<BadRequestResult>(result);

        }

        [Fact]
        public async Task Create_ReturnsCreatedAtAction_WhenBusinessUnitIsValid()
        {
            // Arrange
            var businessUnitDTO = _fixture.Create<BusinessUnitDTO>();
            var validationResult = new ValidationResult();
            _mockValidator.Setup(v => v.ValidateAsync(businessUnitDTO, default)).ReturnsAsync(validationResult);
            _mockService.Setup(s => s.Create(businessUnitDTO)).ReturnsAsync(businessUnitDTO);

            // Act
            var result = await _controller.Create(businessUnitDTO);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(businessUnitDTO, createdAtActionResult.Value);
        }

         [Fact]
        public async Task Create_ReturnsValidationProblem_WhenValidationFails()  {
            // Arrange
            var invalidProgram = _fixture.Create<BusinessUnitDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(2).ToList();
            validationFailures[0] = new ValidationFailure("Name", "Name cannot be blank.");
            validationFailures[1] = new ValidationFailure("Name", "Name cannot be empty.");
            var validationResult = new ValidationResult(validationFailures);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<BusinessUnitDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _controller.Create(invalidProgram);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task GetAll_ReturnsOkResult_WithCorrectData()
        {
            // Arrange
            var filter = new Filter();
            _mockService.Setup(service => service.GetBuHierarchy()).ReturnsAsync(new List<BUHierarchyDTO>());
            _mockService.Setup(service => service.GetOBSSettings()).ReturnsAsync(new  List<CommonLookUp>());
            _mockService.Setup(service => service.GetParentBusinessUnits()).ReturnsAsync(new List<CommonLookUp>());
           
            // Act
            var result = await _controller.GetAll(filter);

            //Assert
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task GetById_ReturnsOkResult_WhenBusinessUnitIsFound()
        {
            // Arrange
            var expectedBusinessUnit = _fixture.Create<BusinessUnitDTO>();     
            _mockService.Setup(s => s.GetById(expectedBusinessUnit.Id)).ReturnsAsync(expectedBusinessUnit);

            // Act
            var result = await _controller.GetById(expectedBusinessUnit.Id);

            // Assert
            var actionResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<BusinessUnitDTO>(actionResult.Value);
            Assert.Equal(expectedBusinessUnit.Id, returnValue.Id);
            Assert.Equal(expectedBusinessUnit.Name, returnValue.Name);
        }

        [Fact]
        public async Task GetById_ReturnsNotFound_WhenDataIsNotFound()
        {
            // Arrange
            var tempBUid = 1;
            _mockService.Setup(service => service.GetById(tempBUid)).ReturnsAsync((BusinessUnitDTO)null);

            // Act
            var result = await _controller.GetById(tempBUid);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Update_ReturnsValidationProblem_WhenValidationFails()
        {
           
            // Arrange
            var businessUnit = _fixture.Create<BusinessUnitDTO>();
            var validationFailures = _fixture.CreateMany<ValidationFailure>(1).ToList();
            foreach (var failure in validationFailures)
            {
                failure.PropertyName = "Name";  
                failure.ErrorMessage = "Name cannot be empty"; 
            }
            _mockService.Setup(service => service.GetById(businessUnit.Id)).ReturnsAsync(businessUnit);
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<BusinessUnitDTO>(), It.IsAny<CancellationToken>())).ReturnsAsync(new ValidationResult(validationFailures));

            //Act 
            var result = await _controller.Update(businessUnit);

            //Assert
            Assert.IsType<BadRequestObjectResult>(result);
           
        }

        [Fact]
        public async Task Update_ReturnsNoContent_WhenUpdateIsSuccessful()
        {

            // Arrange
            var businessUnit = _fixture.Create<BusinessUnitDTO>();  
            _mockValidator.Setup(v => v.ValidateAsync(It.IsAny<BusinessUnitDTO>(), default)).ReturnsAsync(new ValidationResult());       
            _mockService.Setup(service => service.Update(businessUnit)).ReturnsAsync(businessUnit);  

            // Act
            var result = await _controller.Update(businessUnit);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

       
        [Fact]
        public async Task Delete_ReturnsNotFound_WhenBusinessUnitDoesNotExist()
        {
            // Arrange
            int tempBUid = 1;
           _mockService.Setup(s => s.GetById(tempBUid)).ReturnsAsync((BusinessUnitDTO)null);
          
            // Act
            var result = await _controller.GetById(tempBUid);

            // Assert
            Assert.IsType<NotFoundResult>(result);

        }

        [Fact]
        public async Task Delete_ReturnsNoContent_WhenDeleteIsSuccessful()
        {
            // Arrange
            var businessUnit = _fixture.Create<BusinessUnitDTO>();
            _mockService.Setup(s => s.GetById(businessUnit.Id)).ReturnsAsync(businessUnit);
           _mockService.Setup(s => s.Delete(businessUnit.Id)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Delete(businessUnit.Id);

            // Assert
            Assert.IsType<NoContentResult>(result);

        }

    }

}
