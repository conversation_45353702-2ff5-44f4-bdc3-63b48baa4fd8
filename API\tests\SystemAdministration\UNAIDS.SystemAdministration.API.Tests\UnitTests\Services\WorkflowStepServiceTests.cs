using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Application.DTOs;
using UNAIDS.SystemAdministration.Infrastructure;
using UNAIDS.HivScorecards.Domain;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class WorkflowStepServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private readonly Mock<IWorkflowStepRepository> _mockWorkflowStepRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly WorkflowStepService _workflowStepService;
        private readonly Fixture _fixture;

        public WorkflowStepServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockWorkflowStepRepository = new Mock<IWorkflowStepRepository>();
            _mockUserContext = new Mock<IUserContext>();
            _mockUnitOfWork.Setup(u => u.Repository<IWorkflowStepRepository>())
                           .Returns(_mockWorkflowStepRepository.Object);
            
            _workflowStepService = new WorkflowStepService(
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockSequenceRepository.Object,
            _mockUserContext.Object
        );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAll_ReturnsWorkflowSteps()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();

            var workflowSteps = _fixture.CreateMany<WorkflowStep>().ToList();
            var workflowStepDTOs = _fixture.CreateMany<WorkflowStepDTO>(workflowSteps.Count).ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IWorkflowStepRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id,null, null, null)).ReturnsAsync(workflowSteps);

            _mockMapper.Setup(m => m.Map<List<WorkflowStepDTO>>(workflowSteps))
                    .Returns(workflowStepDTOs);

            // Act
            var result = await _workflowStepService.GetAll(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(workflowStepDTOs, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IWorkflowStepRepository>().GetManyAsync(x => x.WorkflowDefinitionId == filter.Id, null, null, null), Times.Once);

            _mockMapper.Verify(m => m.Map<List<WorkflowStepDTO>>(workflowSteps), Times.Once);
        }
        
        [Fact]
        public async Task GetSchemaDefinition_ReturnsListOfSchemaDefinition()
        {
            // Arrange          
            var expectedSchemaDefinition = _fixture.CreateMany<CommonLookUp>(1).ToList();

            _mockWorkflowStepRepository.Setup(repo => repo.GetSchemaDefinition())
                                        .ReturnsAsync(expectedSchemaDefinition);

            // Act
            var result = await _workflowStepService.GetSchemaDefinition();

            // Assert
            Assert.Equal(expectedSchemaDefinition, result);
            
            _mockWorkflowStepRepository.Verify(repo => repo.GetSchemaDefinition(), Times.Once); 
        }

        [Fact]
        public async Task GetWorkflowServiceName_ReturnsListOfWorkflowServiceName()
        {
            // Arrange          
            var expectedWorkflowServiceName = _fixture.CreateMany<CommonLookUp>(1).ToList();
            int id=1;
            
            _mockWorkflowStepRepository.Setup(repo => repo.GetWorkflowServiceName(id))
                                        .ReturnsAsync(expectedWorkflowServiceName);

            // Act
            var result = await _workflowStepService.GetWorkflowServiceName(id);

            // Assert
            Assert.Equal(expectedWorkflowServiceName, result);
            
            _mockWorkflowStepRepository.Verify(repo => repo.GetWorkflowServiceName(id), Times.Once); 
        }

    }
}
