using AutoFixture;
using AutoMapper;
using Moq;
using UNAIDS.Core.Base;
using UNAIDS.Core.Base.Contracts;
using UNAIDS.HivScorecards.Infrastructure.Persistence;
using UNAIDS.SystemAdministration.Application;
using UNAIDS.SystemAdministration.Infrastructure;

namespace UNAIDS.SystemAdministration.API.Tests.UnitTests.Services
{
    public class UserAssignedPermissionServiceTests
    {
        private readonly Mock<IUnitOfWork<SystemAdminDbContext>> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISequenceRepository> _mockSequenceRepository;
        private Mock<IUserContext> _mockUserContext;
        private readonly UserAssignedPermissionService _service;
        private readonly Fixture _fixture;

        public UserAssignedPermissionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork<SystemAdminDbContext>>();
            _mockMapper = new Mock<IMapper>();
            _mockSequenceRepository = new Mock<ISequenceRepository>();
            _mockUserContext = new Mock<IUserContext>();
            
            _service = new UserAssignedPermissionService(
                _mockUnitOfWork.Object,
                _mockMapper.Object,
                _mockSequenceRepository.Object,
                _mockUserContext.Object
            );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetUserAssignedPermissions_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var filter = _fixture.Create<Filter>();
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetUserAssignedPermissions(filter))
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetUserAssignedPermissions(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetUserAssignedPermissions(filter), Times.Once);

        }

        [Fact]
        public async Task GetBusinessUnits_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetBusinessUnits())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetBusinessUnits();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetBusinessUnits(), Times.Once);

        }

        [Fact]
        public async Task GetUserNames_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetUserNames())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetUserNames();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetUserNames(), Times.Once);

        }

        [Fact]
        public async Task GetScreenName_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetScreenName())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetScreenName();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetScreenName(), Times.Once);

        }

        [Fact]
        public async Task GetModule_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetModule())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetModule();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetModule(), Times.Once);

        }

        [Fact]
        public async Task GetRole_ReturnsListOfCommonLookUp()
        {
            // Arrange
            var perms = _fixture.CreateMany<CommonLookUp>().ToList();

            _mockUnitOfWork.Setup(uow => uow.Repository<IUserAssignedPermissionRepository>().GetRole())
                    .ReturnsAsync(perms);

            // Act
            var result = await _service.GetRole();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(perms, result);

            _mockUnitOfWork.Verify(uow => uow.Repository<IUserAssignedPermissionRepository>().GetRole(), Times.Once);

        }
        
    }

}
