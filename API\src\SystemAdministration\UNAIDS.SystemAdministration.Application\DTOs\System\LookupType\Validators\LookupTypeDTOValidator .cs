using FluentValidation;
using Microsoft.EntityFrameworkCore;
using UNAIDS.HivScorecards.Infrastructure.Persistence;

namespace UNAIDS.SystemAdministration.Application.DTOs
{
    public class LookupTypeDTOValidator : AbstractValidator<LookupTypeDTO>
    {
        private readonly SystemAdminDbContext _dbContext;
        public LookupTypeDTOValidator(SystemAdminDbContext dbcontext)
        {
            _dbContext = dbcontext;

            // Validate FieldName (equivalent to Name in ProgramOnboardDTO): cannot be null or empty
            RuleFor(l => l.FieldName)
                .Cascade(CascadeMode.Stop)
                .NotNull().WithMessage("{PropertyName} cannot be null.")
                .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
                .MustAsync(async(entity, value, c) => await UniqueFieldName(entity.Id, value)).WithMessage("{PropertyName} must be unique");

            // Validate Description: optional, but if provided, max length of 200 characters
            RuleFor(l => l.Description)
                .MaximumLength(200).WithMessage("{PropertyName} must be at most 200 characters.")
                .When(l => !string.IsNullOrEmpty(l.Description));

            // Validate IsSystemDefined: optional, must be either 0 or 1 (if provided)
            RuleFor(l => l.IsSystemDefined)
                .Must(value => value == null || value == 0 || value == 1)
                .WithMessage("{PropertyName} must be either 0 or 1.");
        }

        public async Task<bool> UniqueFieldName(int id,  string fieldName)
        {
            if(await _dbContext.LookUpTypes.AsNoTracking().AnyAsync(x => x.FieldName == fieldName && x.Id != id))
            {
                return false;
            }
            return true;
        }
    }
}
